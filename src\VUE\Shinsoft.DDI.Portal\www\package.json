{"name": "cdms", "version": "1.0.0", "description": "A Vue.js project", "author": "shinsoft", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue,.ts src"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.0", "echarts": "^5.4.3", "element-plus": "^2.4.4", "js-file-download": "^0.4.12", "moment": "^2.29.4", "quill": "^1.3.7", "v-viewer": "^3.0.11", "vue": "^3.3.8", "vue-count-to": "^1.0.13", "vue-i18n": "^9.8.0", "vue-moment": "^4.1.0", "vue-quill-editor": "^3.0.6", "vue-router": "^4.2.5", "vue3-puzzle-vcode": "^1.1.7", "vuedraggable": "^4.1.0", "vuex": "^4.1.0", "vxe-table": "^4.5.17"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "@vue/compiler-sfc": "^3.3.8", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "less": "^4.2.0", "sass": "^1.69.5", "sass-loader": "^13.3.2", "typescript": "^5.2.2", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}