/* eslint-disable no-trailing-spaces */
// 公共-->中英文
export const system = {
    'zhCN': {
        system: {
            accountNumber: '账号',
            password: '密码',
            resetPassword: '重置密码',
            logIn: '登录',
            accountCannotBeEmpty: '账号不能为空',
            passwordBeEmpty: '密码不能为空',
            home: '首页',
            language: '中',
            search: '查询',
            add: '新增',
            insert: '添加',
            save: '保存',
            cancel: '取消',
            close: '关闭',
            export: '导出',
            edit: '编辑',
            delete: '删除',
            withdraw: '撤回',
            action: '操作',
            no: '序号',
            alter: '提示',
            confirm: '确定',
            import: '导入',
            view: '查看',
            modify: '变更生效日期',
            selectFile: '选择文件',
            uploadFile: '上传文件',
            deleteSuccess: '删除成功',
            withdrawSuccess: '撤回成功',
            modiftSuccess: '变更成功',
            invalidSuccess: '作废成功',
            editsuccess: '编辑成功',
            saveSuccess: '保存成功',
            sendCodeSuccess: '企业邀请码 发送成功！',
            addSuccess: '新增成功',
            cancelSuccess: '取消成功',
            uploadSuccess: '上传成功',
            downgradeSuccess: '降级成功',
            location: '省份/城市/区县',
            provinceAndCity: '省份/城市',
            province: '省份',
            city: '城市',
            county: '区县',
            importAll: '导出全部',
            yes: '是',
            deny: '否',
            return: '返回',
            notreturned: '未返回',
            normal: '正常',
            abnormal: '异常',
            createTime: '创建时间',
            announcementTitle: '公告标题',
            subTitle: '副标题',
            isImportant: '是否重要',
            hits: '阅读次数',
            commerceOrganizationID: '审批人',
            commerceOrganizationName: '审批节点',
            approval: '审批',
            approvalConfirm: '确认',
            approvalConfirmAndEdit: '确认并编辑',
            approvalComments: '审批意见',
            modifyComments: '变更原因',
            refuse: '拒绝',
            submit: '提交',
            previous: '上一步',
            nextStep: '下一步',
            submitSuccess: '提交成功',
            all: '全部',
            select: '选择',
            replace: '替换',
            pleaseSelect: '请选择',
            selectMaterialGroup: '请选择分型',
            selectMaterielBatch: '请选择物料/批号',
            note: '备注',
            content: '内容',
            upload: '上传',
            browse: '浏览...',
            finalEditor: '修改人',
            creator: '创建人',
            finalEditTime: '修改时间',
            finalEditDateTime: '最后修改时间',
            annexName: '附件名称',
            uploadTime: '上传时间',
            uploadOperator: '上传人',
            download: '下载',
            templateDownload: '模板下载',
            approvalHistory: '审批历史',
            operator: '操作人',
            operatingTime: '操作时间',
            operatingType: '操作类型',
            whetherDelete: '是否确定要删除当前记录？',
            whetherDeleteSelection: '是否确定要删除所选记录？',
            batchImport: '批量导入',
            batchChange: '批量变更',
            batchDelete: '批量删除',
            batchDisable: '批量停止',
            exportIsTargetHospital: '同步目标医院',
            releaseSuccess: '发布成功',
            allConfirmedSuccess: '全部确认成功',
            confirmSuccess: '确认成功',
            rejectSuccess: '拒绝成功',
            replaceSuccess: '替换成功',
            allConfirmation: '全部确认',
            release: '发布',
            flow: '冲销',
            dictionaryManagement: '字典管理',
            dictionaryName: '字典名称',
            dictionaryCode: 'Code',
            parentDictionary: '父级字典',
            dictionaryDescription: '字典描述',
            dictionaryChildDescription: '子项描述',
            dictionaryChildName: '子项名',
            dictionaryNameIsNotNull: '字典名不能为空',
            dictionaryNameOverLength: '字典名称太长',
            dictionaryCodeOverLength: 'Code长度超过限制',
            dictionaryDescriptionOverLength: '字典描述长度超过限制',
            dictionaryItemNameIsNotNull: '子项名不能为空',
            dictionaryCodeIsNotNull: 'Code不能为空',
            dictionaryItemCodeIsNotNull: '子项Code不能为空',
            dictionaryItemNameIsNotRepeat: '子项名不能重复',
            dictionaryItemCodeIsNotRepeat: '子项Code不能重复',
            dictionaryNameOverlength: '字典名长度过长',
            dictionaryCodeOverlength: 'Code长度过长',
            dictionaryItemCodeOverlength: '子项Code长度过长',
            dictionaryItemNameOverlength: '子项名长度过长',
            dictionaryItemDescriptionOverlength: '子项描述长度过长',
            startDateNotExceedEtartDate: '开始时间不能大于结束时间',
            announcementTitleNull: '公告标题不能为空',
            announcementTitleOverLength: '公告标题长度超过限制',
            subTitleOverLength: '副标题长度超过限制',
            cycleManagement: '周期管理',
            announcementManagement: '公告管理',
            title: '标题',
            level: '级别',
            exceptionLevel: '异常级别',
            description: '描述',
            exceptionMessage: '异常消息',
            eventSource: '事件源',
            ipAddress: 'IP地址',
            exceptionSource: '异常源',
            exceptionType: '异常类型',
            stackTrace: '堆栈信息',
            throwTime: '发生时间',
            salesFlowCycleYear: '年份',
            salesFlowCycleMonth: '流向月份',
            startDate: '起始日期',
            endDate: '结束日期',
            startMonth: '起始月份',
            endMonth: '结束月份',
            distributor: '流向商业',
            preparatory: '可追溯商业',
            distributorReleaseDate: '流向商业发布时间',
            inventoryCalculateDate: '库存计算时间',
            preparatoryReleaseDate: '可追溯商业发布时间',
            frozenDate: '冻结时间',
            isGenerateSAPMonthly: '是否生成SAP月数据',
            sAPMonthlyData: 'SAP月数据',
            isCurrentCycle: '是否当前周期',
            remark: '备注',
            startDateIsNotNull: '开始日期不能为空',
            endDateIsNotNull: '结束日期不能为空',
            dateIsNotNull: '日期不能为空',
            whetherDeleteDctionary: '是否确认要删除此字典？',
            whetherDeleteAnnouncement: '是否确认要删除此公告',
            areYouSureDelete: '确认要删除该数据？',
            dictionarySort: '排序值',
            whenRejectionApprovalOpinionIsNotNull: '拒绝时，审批意见不能为空',
            unselectedColumn: '未选择任何列，请重新选择需要导出的列。',
            service403: '403-无权限访问服务器上文件或目录',
            service404: '404-所请求的页面不存在或已被删除',
            unableResources404: '无法找到该资源页面',
            possibleReasons: '可能原因：',
            networkSignalDifference404: '网络信号差',
            youCanTry: '您可以尝试：',
            inputSiteIncorrect404: '输入的网站不正确',
            backToHomePage404: '返回首页',
            noRightToVisit403: '无权访问目标页面',
            noaccessToThisPage403: '您所属的角色无权访问该页面',
            reLogin403: '重新登录',
            service500: '500服务端错误',
            freeze: '冻结',
            freezeSuccess: '冻结成功',
            copyNew: '复制新增',
            clear: '清除',
            retain: '保留',
            retainSuccess: '保留成功',
            clearSuccess: '清除成功',
            isReject: '是否拒绝',
            isConfirm: '是否确认',
            isSubmit: '是否提交',
            isSave: '是否保存',
            approvalSuccess: '审批成功',
            needUploadAttachment: '请上传附件',
            needUploadInventoryAttachment: '请上传库存附件',
            isApproval: '是否审批',
            isDataManagerConfirmRejection: '该提交人已离职，是否确认拒绝！',
            isConfirmRejection: '该提交人已离职，请联系内勤！',
            productChannelChangeTips: '无法提交，至少包含一条渠道信息',
            productChannelSaveTips: '无法保存，至少包含一条渠道信息',
            verificationInformation: '验证信息',
            batchImportPermission: '批量导入数据权限',
            configuringRolesAndDataPermissions: '配置角色与数据权限',
            dataPermissionsTemplateDownload: '数据权限模板下载',
            accountingPirceTemplateDownload: '价格模板下载',
            targetReceiverTemplateDownload: '目标客户模板下载',
            drugstoreOfHospitalTemplateDownload: '院外药店模板下载',
            cPASaleDataTemplateDownload: 'CPA数据模板下载',
            quotaTemplateDownload: '潜力模板下载',
            isDataManagerConfirm: '该提交人已离职，是否继续确认！',
            status: '状态',
            iInValid: '停用',
            valid: '启用',
            whetherStart: '是否启用？',
            whetherStop: '是否停用？',
            stopSuccess: '停用成功',
            startSuccess: '启用成功',
            messageHint: '消息提示',
            businessLicenseIsRequired: '营业执照为必传项',
            checkUserExportLimitMessage: '导出数据已超过12万条，请缩小查询范围。',
            channelChangeExport: '导出渠道变更记录',
            noFormType: '单据类型不存在。',
            batchMdification: '批量修改',
            feedbackCardinalNumber: '反馈基数',
            feedbackCardinalNumberNotNUll: '反馈基数不能为空',
            editFeedbackCardinalNumber: '编辑反馈基数设置',
            batcheditFeedbackCardinalNumber: '批量编辑反馈基数设置',
            allowOrder: '可以下单',
            unAllowOrder: '不可下单',
            notNull: '不能为空',
            uploadBeingCalculated: '正在计算中…… 请稍后',
            synchronizing: '正在同步中…… 请稍后',
            uploadFileProgressBar: '文件上传',
            uploadFileName: '文件名称：',
            uploadFileSize: '文件大小：',
            uploadFileByte: '字节',
            uploadCalculating: '计算库存报告',
            exportEmployeeRole: '导出人员权限',
            versionHistory: '版本历史记录',
            currentVersionNumber: '当前版本号',
            feedbackSuccess: '反馈成功',
            periodicState: '周期状态',
            completed: '已完成',
            uncompleted: '未完成',
            effectivePeriod: '生效周期',
            feedbackPrompt: '反馈后不可更改，是否确认反馈',
            synchronization: '目标医院同步日志',
            feedbackBaseline: '反馈基线',
            salesWarningBaseline: '预警基线',
            batcheditSalesQuantityBaseline: '批量编辑发货方销量预警基线',
            batcheditTargetHospitalBaseline: '批量编辑目标医院销量预警基线',
            editSalesQuantityBaseline: '编辑发货方销量预警基线',
            editTargetHospitalBaseline: '编辑目标医院销量预警基线',
            warningBaselineNotNUll: '预警基数不能为空',
            userIdentityExpired: '用户身份信息已过期。',
            targetHospitalSynchronization: '目标医院同步',
            isShow: '是否显示',
            display: '显示',
            hide: '隐藏',
            whetherDisplay: '是否显示？',
            whetherHide: '是否隐藏？',
            maxValue: '最大可输入999999',
            theContentCanNotBeBlank: '内容不能为空',
            notification: '消息名称',
            isIgnore: '是否忽略',
            loginStatus: '登录状态',
            loginUser: '登录人',
            loginTime: '登录时间',
            loginIdentity: '身份信息',
            send: '发送消息',
            sendSuccess: '云商反馈待办提醒发送成功',
            queryData: '报表正在查询',
            loadingData: '报表正在加载中…… 请稍后',
            showCity: '展示城市',
            hideCity: '不展示城市',
            channelChangedDate: '渠道变更所在周期：',
            dictionaryMoveUp: '上移',
            dictionaryMoveDown: '下移',
            dictionaryAdd: '新增子项',
            productSettingOfSuspectAlert: '注意：疑似调拨品种设置中包含的产品，在生成疑似调拨报表时将被过滤。',
            update: '更新',
            fileNameAlreadyExist: '文件名称已经存在',
            uploadLogs: '上传日志',
            rightCount: '成功条数',
            differentCount: '差异条数',
            repeatCount: '重复条数',
            totalCount: '总条数',
            errorFileName: '错误文件',
            businessLicenseAndDrugLicenseIsRequired: '营业执照和药品经营许可证为必传项',
            attachment: '附件',
            uploadFormatError: '上传文件格式不正确，格式支持',
            emptyFileError: '不能上传空文件',
            checkAll: '全选',
            viewDetail: '查看明细',
            whenApprovalMergeCannotHasValue: '审批通过时不能合并机构',
            whenRejectedMergeCannotHasValue: '请选择合并机构',
            generateQuota: '生成阶段潜力',
            reGenerateQuota: '重新生成',
            addReceiverQuotal: '添加潜力',
            hospitalProcurementTemplateDownload: '医院进药模板下载',
            splitQuota: '拆分潜力',
            regionQuota: '管理大区潜力',
            editRegion: '调整大区',
            exportStageQuota: '导出阶段潜力',
            exportRgionQuota: '导出大区潜力',
            hospitalNameRequired: '请选择医院'
        },
        // 自动服务运行状态查询
        taskStatus: {
            missionName: '任务名称',
            serviceName: '服务名称',
            serviceType: '服务类型',
            intervalPeriod: '执行间隔',
            planExecutionDay: '预计执行日期',
            planExecutionTime: '预计执行时间',
            lastExecutionTime: '最后执行时间',
            cdmsService: 'CDMS 服务',
            cloudService: '云商服务',
            minute: '分钟'
        },
        expiringQualification: {
            importTime: '上传时间',
            importFile: '导入文件',
            totalCount: '总数',
            rightCount: '正确数',
            wranningCount: '警告数',
            differentCount: '异常数',
            repeatCount: '重复数',
            ignoreCount: '忽略数',
            importAbnormalData: '错误数据地址'
        },
        syncSFELog: {
            sourceFile: '源文件',
            logtype: '类型',
            mode: '模式',
            sfestatus: '状态',
            cycle: '周期',
            startdatetime: '开始时间',
            enddatetime: '结束时间',
            creator: '执行人',
            createtime: '执行时间',
            reexecute: '重新执行',
            whetherReExecute: '确定要重新执行?'
        }
    },
    'enUS': {
        system: {
            accountNumber: 'Account',
            password: 'Password',
            resetPassword: 'Reset Password',
            logIn: 'Log In',
            accountCannotBeEmpty: 'Account can not be empty.',
            passwordBeEmpty: 'Password can not be empty.',
            home: 'Home',
            language: 'EN',
            search: 'Search',
            add: 'Add',
            insert: 'Add',
            save: 'Save',
            cancel: 'Cancel',
            close: 'Close',
            export: 'Export',
            edit: 'Edit',
            delete: 'Delete',
            withdraw: 'Withdraw',
            action: 'Action',
            no: 'No.',
            alter: 'Confirm',
            confirm: 'Confirm',
            import: 'Import',
            view: 'View',
            selectFile: 'Select File',
            uploadFile: 'Upload File',
            editsuccess: 'Edit Success',
            deleteSuccess: 'Delete Success',
            withdrawSuccess: 'Withdraw Success',
            invalidSuccess: 'Invalid Success',
            saveSuccess: 'Save Successfully',
            sendCodeSuccess: 'Send Successfully',
            addSuccess: 'Add Successfully',
            cancelSuccess: 'cancel Successfully',
            uploadSuccess: 'Upload Successfully',
            downgradeSuccess: 'Downgrade Successfully',
            location: 'Province/City/Contry',
            provinceAndCity: 'Province/City',
            province: 'Province',
            city: 'City',
            county: 'County',
            importAll: 'Import All',
            yes: 'YES',
            deny: 'NO',
            return: 'Return',
            notreturned: 'Not returned',
            normal: 'Normal',
            abnormal: 'Abnormal',
            createTime: 'Create Time',
            announcementTitle: 'Announcement Title',
            subTitle: 'Sub Title',
            isImportant: 'Is Important',
            hits: 'Hits',
            commerceOrganizationName: 'Approval Node',
            approval: 'Approval',
            approvalConfirm: 'Confirm',
            approvalConfirmAndEdit: 'Confirm and Edit',
            approvalComments: 'Comment',
            refuse: 'Reject',
            submit: 'Submit',
            finalEditor: 'Final Editor',
            creator: 'Creator',
            finalEditTime: 'Final EditTime',
            finalEditDateTime: 'Final EditTime',
            previous: 'Previous',
            nextStep: 'Next',
            submitSuccess: 'Success',
            all: 'All',
            select: 'Select',
            replace: 'Replace',
            pleaseSelect: 'Please Select',
            selectMaterialGroup: 'Material Group',
            selectMaterielBatch: 'Material/Batch',
            note: 'Remark',
            content: 'Content',
            upload: 'Upload',
            browse: 'Browse...',
            annexName: 'Attachment Name',
            uploadTime: 'Upload Time',
            uploadOperator: 'Upload User',
            download: 'Download',
            templateDownload: 'Download template',
            approvalHistory: 'History',
            operator: 'Operator',
            operatingTime: 'Operating Time',
            operatingType: 'Operating Type',
            whetherDelete: 'Are you sure to delete it?',
            whetherDeleteSelection: 'Are you sure to delete the selection records?',
            batchImport: 'Batch Import',
            batchChange: 'Batch Change',
            batchDelete: 'Batch Delete',
            batchDisable: 'Batch Stop',
            exportIsTargetHospital: 'Synchronized target hospital',
            releaseSuccess: 'Success',
            allConfirmedSuccess: 'Success',
            confirmSuccess: 'Confirm success',
            allConfirmation: 'Confirm All',
            rejectSuccess: 'Reject Successfully',
            replaceSuccess: 'Replace Successfully',
            release: 'Release',
            flow: 'Write-off',
            dictionaryManagement: 'Dictionary Management',
            dictionaryName: 'Dictionary Name',
            dictionaryCode: 'Dictionary Code',
            parentDictionary: 'Parent Dictionary',
            dictionaryDescription: 'Description',
            dictionaryChildDescription: 'Item Description',
            dictionaryChildName: 'Item Name',
            dictionaryNameIsNotNull: 'Required',
            dictionaryNameOverLength: 'Dictionary Name is too long.',
            dictionaryCodeOverLength: 'Dictionary Code is too long.',
            dictionaryDescriptionOverLength: 'Dictionary Description is too long',
            dictionaryItemNameIsNotNull: 'Required',
            dictionaryCodeIsNotNull: 'Required',
            dictionaryItemCodeIsNotNull: 'Required',
            dictionaryItemNameIsNotRepeat: 'Item Name can not be repeated.',
            dictionaryItemCodeIsNotRepeat: 'Item Code can not be repeated.',
            dictionaryItemCodeOverlength: 'Item Code is too long.',
            dictionaryItemNameOverlength: 'Item Name is too long.',
            dictionaryItemDescriptionOverlength: 'Item Description is too long.',
            startDateNotExceedEtartDate: 'The starting time should not be earlier than the end time.',
            announcementTitleNull: 'Announcement title cannot be empty',
            announcementTitleOverLength: 'Announcement title length exceeds limit',
            subTitleOverLength: 'Subtitle length exceeds limit',
            cycleManagement: 'Cycle Management',
            announcementManagement: 'Announcement Management',
            level: 'Level',
            exceptionLevel: 'Level',
            title: 'Title',
            description: 'Description',
            exceptionMessage: 'Exception Message',
            eventSource: 'Source',
            exceptionSource: 'Source',
            ipAddress: 'IP',
            exceptionType: 'Exception Type',
            stackTrace: 'StackTrace',
            throwTime: 'Throw Time',
            salesFlowCycleYear: 'Year',
            salesFlowCycleMonth: 'Month',
            startDate: 'Start Date',
            endDate: 'End Date',
            startMonth: 'Start Month',
            endMonth: 'End Month',
            distributor: 'Distributor',
            preparatory: 'Preparatory Distributor',
            distributorReleaseDate: 'Distributor SalesFlow Release Date',
            inventoryCalculateDate: 'inventory Calculate Date',
            preparatoryReleaseDate: 'Preparatory SalesFlow Release Date',
            frozenDate: 'Frozen Date',
            isGenerateSAPMonthly: 'SAP SalesFlow Generate Date',
            sAPMonthlyData: 'SAP Monthly Sales Flow',
            isCurrentCycle: 'Current Cycle',
            remark: 'Remark',
            startDateIsNotNull: 'StartDate can be not empty.',
            endDateIsNotNull: 'EndDate can be not empty.',
            whetherDeleteDctionary: 'Are you sure you want to delete it?',
            whetherDeleteAnnouncement: 'Are you sure you want to delete it?',
            areYouSureDelete: 'Are you sure you want to delete it',
            whetherStart: 'Is it enabled?',
            whetherStop: 'Is it stop?',
            stopSuccess: 'Successful outage',
            startSuccess: 'Successful Enablation',
            dictionarySort: 'Sort',
            whenRejectionApprovalOpinionIsNotNull: 'When rejection,approval opinion is not null.',
            unselectedColumn: 'Please select the columns that need to be exported.',
            service403: 'No permission to access files or directories on the server ',
            service404: 'The requested page does not exist or has been deleted. ',
            unableResources404: 'The resource page could not be found',
            possibleReasons: 'Possible reasons:',
            networkSignalDifference404: 'Network signal difference',
            youCanTry: 'You can try:',
            inputSiteIncorrect404: 'You can try:',
            backToHomePage404: 'Back to home page',
            noRightToVisit403: 'No access to the target page',
            noaccessToThisPage403: 'The role you belong to does not have access to this page',
            reLogin403: 'Re login',
            service500: '500 server error.',
            freeze: 'Frozen',
            freezeSuccess: 'Success',
            copyNew: 'Copy',
            clear: 'Clear',
            retain: 'Keep in',
            retainSuccess: 'Success',
            clearSuccess: 'Success',
            isReject: 'Do you reject',
            isConfirm: 'Do you confirm',
            isSubmit: 'Do you submit',
            isSave: 'Do you save',
            approvalSuccess: 'Approval success',
            needUploadAttachment: 'Please upload attachment',
            needUploadInventoryAttachment: 'Please upload Inventory attachment',
            isApproval: 'Do you approval',
            isDataManagerConfirmRejection: 'The author has left the company and confirmed the rejection!',
            isConfirmRejection: 'The author has left, please contact the office!',
            productChannelChangeTips: 'At least one product channel must be included.',
            productChannelSaveTips: 'Unable to save, at least one channel information',
            verificationInformation: 'verification Information',
            batchImportPermission: 'Import user authority',
            configuringRolesAndDataPermissions: 'Authorization',
            dataPermissionsTemplateDownload: 'Download template',
            accountingPirceTemplateDownload: '价格模板下载',
            targetReceiverTemplateDownload: '目标客户模板下载',
            drugstoreOfHospitalTemplateDownload: '院外药店模板下载',
            cPASaleDataTemplateDownload: 'CPA数据模板下载',
            quotaTemplateDownload: '潜力模板下载',
            hospitalProcurementTemplateDownload: '医院进药模板下载',
            isDataManagerConfirm: 'The author has left and will continue to confirm!',
            status: 'Status',
            iInValid: 'Disable',
            valid: 'Enable',
            checkUserExportLimitMessage: 'More than 120,000 data have been exported. Please narrow down the scope of the query.',
            messageHint: 'Message hint',
            businessLicenseIsRequired: 'Business license is required',
            channelChangeExport: 'Export channel change log',
            noFormType: 'Form type not exist.',
            batchMdification: 'Batch Mdification',
            feedbackCardinalNumber: 'Feedback Cardinal Number',
            feedbackCardinalNumberNotNUll: 'Feedback Cardinal Number Not Null',
            editFeedbackCardinalNumber: 'Edit Feedback Cardinal Number',
            batcheditFeedbackCardinalNumber: 'Batch Edit Feedback Cardinal Number',
            allowOrder: 'Can place an order',
            unAllowOrder: 'Cannot place an order',
            notNull: ' Is not null',
            uploadBeingCalculated: 'Please wait...',
            synchronizing: 'In sync... Please wait a moment.',
            uploadFileProgressBar: ' Upload files to server',
            uploadFileName: 'FileName：',
            uploadFileSize: 'Size：',
            uploadFileByte: 'B',
            uploadCalculating: 'Calculating',
            exportEmployeeRole: 'Export staff permissions',
            versionHistory: 'Version History',
            currentVersionNumber: 'Current version number',
            feedbackSuccess: 'FeedBack Success',
            periodicState: 'Periodic State',
            completed: 'Completed',
            uncompleted: 'Uncompleted',
            effectivePeriod: 'Effective period',
            feedbackPrompt: 'Cant change after feedback, confirm feedback',
            synchronization: 'Synchronized Log of Target Hospital',
            feedbackBaseline: 'Feedback Baseline',
            salesWarningBaseline: 'Warning Baseline',
            batcheditSalesQuantityBaseline: 'Batch Edit Sales Quantity Baseline',
            batcheditTargetHospitalBaseline: 'Batch Edit Target Hospital Baseline',
            editSalesQuantityBaseline: 'Edit Sales Quantity Baseline',
            editTargetHospitalBaseline: 'Edit Target Hospital Baseline',
            warningBaselineNotNUll: 'Warning baseline can not be empty',
            userIdentityExpired: 'User identity information has expired.',
            targetHospitalSynchronization: 'Target hospital synchronization',
            isShow: 'Need Display',
            display: 'display',
            hide: 'Hide',
            whetherDisplay: 'Is it display',
            whetherHide: 'Is it hide',
            maxValue: 'Max value is 999999',
            theContentCanNotBeBlank: 'the content can not be blank',
            notification: 'Notification',
            isIgnore: 'Is Ignore',
            loginStatus: 'Login Status',
            loginUser: 'Login User',
            loginTime: 'Login Time',
            loginIdentity: 'Identity',
            send: 'Send Notice',
            sendSuccess: 'Notice send success',
            queryData: 'Report is querying',
            loadingData: 'The report is loading... Please wait',
            showCity: 'Show City',
            hideCity: 'Hide City',
            channelChangedDate: 'Channel changed date',
            dictionaryMoveUp: 'Move Up',
            dictionaryMoveDown: 'Move Down',
            dictionaryAdd: 'Add child',
            productSettingOfSuspectAlert: 'Note: products included in the suspected allocation variety Settings will be filtered when generating the suspected allocation report.',
            update: 'Update',
            fileNameAlreadyExist: 'The file name already exist',
            uploadLogs: 'Upload logs',
            rightCount: 'Right Count',
            differentCount: 'Different Count',
            repeatCount: 'Repeat Count',
            totalCount: 'Total Count',
            errorFileName: 'Error File Name',
            businessLicenseAndDrugLicenseIsRequired: 'Business license and drug distribution license are required.',
            attachment: 'Attachment',
            uploadFormatError: 'Upload file format incorrect, format support ',
            emptyFileError: 'Upload empty file is forbidden',
            checkAll: 'Check ALL',
            viewDetail: '查看明细',
            generateQuota: '生成阶段潜力',
            reGenerateQuota: '重新生成',
            addReceiverQuotal: '添加潜力',            
            whenApprovalMergeCannotHasValue: 'Institutions cannot be merged upon approval',
            whenRejectedMergeCannotHasValue: 'Please select a merging institution',
            splitQuota: '拆分潜力',
            regionQuota: '管理大区潜力',            
            editRegion: '调整大区',
            exportStageQuota: '导出阶段潜力',
            exportRgionQuota: '导出大区潜力',
            hospitalNameRequired: '请选择医院'
        },
        // 自动服务运行状态查询
        taskStatus: {
            missionName: 'Mission Name',
            serviceName: 'Service name',
            serviceType: 'Service type',
            intervalPeriod: 'Interval period',
            planExecutionDay: 'Plan execution Day',
            planExecutionTime: 'Plan execution time',
            lastExecutionTime: 'Last execution time',
            cdmsService: 'CDMS Service',
            cloudService: 'Cloud service',
            minute: 'Minute'
        },
        expiringQualification: {
            importTime: 'import time',
            importFile: 'import file',
            totalCount: 'total',
            rightCount: 'right count',
            wranningCount: 'wranning Count',
            differentCount: 'different count',
            repeatCount: 'repeat count',
            importAbnormalData: 'Error Data'
        },
        syncSFELog: {
            sourceFile: 'source file',
            logtype: 'type',
            mode: 'mode',
            sfestatus: 'status',
            cycle: 'cycle',
            startdatetime: 'start time',
            enddatetime: 'end time',
            creator: 'creator',
            createtime: 'create time',
            reexecute: 'ReExecute',
            whetherReExecute: 'Are you sure you want to re execute?'
        }
    }
}
export default system
