<template>
  <div>
    <Row>
      <Col span="24">
      <div v-for="(todo,index) in columnData" class="divider">
        <Checkbox :indeterminate="todo.indeterminate" :value="todo.selectedAll"
          @click.prevent.native="handleCheckAll(index,columnData)">{{todo.label}}</Checkbox>
        <CheckboxGroup class="checkboxGroupLeft"
          v-model="todo.selectedChildren==null?defaultSelectedChildren:todo.selectedChildren"
          @on-change="checkAllGroupChange(index,todo.selectedChildren,columnData)">
          <Checkbox v-for="item in todo.childrenAll" :key="item.label" :label="item.label">{{item.label}}</Checkbox>
        </CheckboxGroup>
      </div>
      </Col>
    </Row>
    <div slot="footer">
      <Row class="ivu-row-Padding-5 ivu-row-Top">
        <Col span="24">
        <Row type="flex" justify="end" :gutter="10">
          <Col span="2.5">
          <Button @click="handleSubmit()" icon="ios-checkmark-outline">保存</Button>
          </Col>
          <Col span="2.5">
          <Button @click="handleCancel()" icon="ios-close-outline" style="margin-left: 8px">取消</Button>
          </Col>
        </Row>
        </Col>
      </Row>
    </div>
  </div>
</template>
<script>
    export default {
        props: ['column', 'setCheck'],
        computed: {
            columnData: function () {
                return this.column
            }
        },
        data () {
            return {
                defaultSelectedChildren: []
            }
        },
        methods: {
            // 区域--全选
            handleCheckAll (index, dataAll) {
                let childrenAll = [];
                dataAll[index].childrenAll.forEach(function (item) {
                    childrenAll.push(item.label);
                });
                if (dataAll[index].indeterminate) {
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].selectedAll = !dataAll[index].selectedAll;
                }
                dataAll[index].indeterminate = false;
                if (dataAll[index].selectedAll) {
                    dataAll[index].selectedChildren = childrenAll;
                } else {
                    dataAll[index].selectedChildren = [];
                }
            },
            // 区域--change
            checkAllGroupChange (index, data, dataAll) {
                let childrenAll = [];
                dataAll[index].childrenAll.forEach(function (item) {
                    childrenAll.push(item.label);
                });
                if (data.length == childrenAll.length) {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = true;
                } else if (data.length > 0) {
                    dataAll[index].indeterminate = true;
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = false;
                }
            },
            handleSubmit () {
                let childrenAll = [];
                this.columnData.forEach(function (item) {
                    if (item.selectedChildren) {
                        item.selectedChildren.forEach(function (itemAll) {
                            childrenAll.push(itemAll);
                        });
                    }
                });
                // 检查是否选中列
                if (childrenAll.length > 0) {
                    this.$emit('exportSubmitEvent', childrenAll);
                } else {
                    // 未选择任何列，请重新选择需要导出的列
                    this.$Message.error(this.$t('system.unselectedColumn'));
                }
            },
            handleCancel () {
                this.$emit('exportCancelEvent');
            }
        },
        watch: {
            column (val) {
                val.forEach(itemO => {
                    if (this.setCheck) {
                        if (itemO.selectedAll) {
                            itemO.childrenAll.forEach(function (item) {
                                itemO.selectedChildren.push(item.label);
                            });
                        }
                    } else {
                        itemO.indeterminate = false;
                        itemO.selectedAll = true;
                        itemO.childrenAll.forEach(function (item) {
                            itemO.selectedChildren.push(item.label);
                        });
                    }
                })
            }
        }
    };

</script>
<style scoped>
  .divider {
    border-bottom: 1px solid #e9e9e9;
    padding-bottom: 6px;
    margin-bottom: 6px;
  }

  .customexportUl li {
    list-style: none;
    width: 25%;
    float: left;
  }

  .checkboxGroupLeft {
    margin-left: 26px;
  }

</style>
