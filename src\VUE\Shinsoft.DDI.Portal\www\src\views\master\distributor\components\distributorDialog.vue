<template>
  <el-dialog :title="isEdit ? '编辑收货方' : '新增收货方'" v-model="dialogVisible" width="900px" :close-on-click-modal="false"
    @close="handleClose">
    <el-form ref="distributorFormRef" :model="distributorForm" :rules="formRules" label-width="140px">
      <!-- 基本信息 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="收货方名称" prop="Name">
            <el-input v-model="distributorForm.Name" placeholder="请输入收货方名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 授权码和收货方Code - 仅编辑时显示且不可编辑 -->
      <el-row :gutter="16" v-if="isEdit">
        <el-col :span="12">
          <el-form-item label="授权码" prop="SdrCode">
            <el-input v-model="distributorForm.SdrCode" placeholder="授权码" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收货方Code" prop="Code">
            <el-input v-model="distributorForm.Code" placeholder="收货方Code" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 收货方类型和省市县 - 放在一行 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="收货方类型" prop="receiverTypeCascader">
            <el-cascader v-model="distributorForm.receiverTypeCascader" :options="receiverTypeCascaderOptions"
              placeholder="请选择收货方类型" clearable @change="handleReceiverTypeChange" :props="{ expandTrigger: 'hover' }" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="省市县" prop="locationCascader">
            <el-cascader v-model="distributorForm.locationCascader" :options="locationCascaderOptions"
              placeholder="请选择省市县（可选择任意级别）" clearable @change="handleLocationChange" :props="{
                expandTrigger: 'hover',
                checkStrictly: true,
                emitPath: true
              }" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 联系信息 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="Telephone">
            <el-input v-model="distributorForm.Telephone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电子邮箱" prop="EMail">
            <el-input v-model="distributorForm.EMail" placeholder="请输入电子邮箱" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 其他信息 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="UnifiedSocialCreditCode">
            <el-input v-model="distributorForm.UnifiedSocialCreditCode" placeholder="请输入统一社会信用代码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮政编码" prop="PostalCode">
            <el-input v-model="distributorForm.PostalCode" placeholder="请输入邮政编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="网络地址" prop="NetAddress">
            <el-input v-model="distributorForm.NetAddress" placeholder="请输入网络地址" />
          </el-form-item>
        </el-col>
        <!-- 停用时间 - 仅编辑时显示且不可修改 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="停用时间" prop="StopTime">
            <el-date-picker v-model="distributorForm.StopTime" type="datetime" placeholder="停用时间"
              format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 医院等级和级别 - 仅当选择医疗机构类型时显示 -->
      <el-row :gutter="16" v-if="showHospitalFields">
        <el-col :span="12">
          <el-form-item label="医院等级" prop="HospitalGradeId">
            <el-select v-model="distributorForm.HospitalGradeId" placeholder="请选择医院等级" clearable>
              <el-option v-for="item in hospitalGradeList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="医院级别" prop="HospitalLevelId">
            <el-select v-model="distributorForm.HospitalLevelId" placeholder="请选择医院级别" clearable>
              <el-option v-for="item in hospitalLevelList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 详细地址 -->
      <el-form-item label="详细地址" prop="Address">
        <el-input v-model="distributorForm.Address" type="textarea" :rows="3" placeholder="请输入详细地址" />
      </el-form-item>

      <!-- 备注 -->
      <el-form-item label="备注" prop="Remark">
        <el-input v-model="distributorForm.Remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { receiverApi } from '@/api/receiverApi'
import { selectorApi } from '@/api/selectorApi'

export default {
  name: 'receiverDialog',
  props: {
    // 对话框显示状态
    visible: {
      type: Boolean,
      default: false
    },
    // 记录ID，传入则为编辑模式，不传入则为新增模式
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const distributorFormRef = ref(null)

    // 保存加载状态
    const saveLoading = ref(false)

    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)

    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 医院字段显示控制
    const showHospitalFields = ref(false)

    // 表单数据
    const distributorForm = reactive({
      SdrCode: '',
      Code: '',
      Name: '',
      ProvinceId: null,
      CityId: null,
      CountyId: null,
      ReceiverTypeId: null,
      UnifiedSocialCreditCode: '',
      Address: '',
      Telephone: '',
      EMail: '',
      PostalCode: '',
      NetAddress: '',
      StopTime: null,
      HospitalGradeId: null,
      HospitalLevelId: null,
      Remark: '',
      // 级联选择器辅助字段
      receiverTypeCascader: [],
      locationCascader: []
    })

    // 表单验证规则
    const formRules = reactive({
      Name: [
        { required: true, message: '请输入收货方名称', trigger: ['blur', 'change'] },
        { max: 100, message: '收货方名称不能超过100个字符', trigger: ['blur', 'change'] }
      ],
      receiverTypeCascader: [
        { required: true, message: '请选择收货方类型', trigger: ['blur', 'change'] }
      ],
      locationCascader: [
        { required: true, message: '请至少选择省份', trigger: ['blur', 'change'] }
      ],
      Telephone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: ['blur', 'change'] }
      ],
      EMail: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
      ],
      PostalCode: [
        { pattern: /^\d{6}$/, message: '请输入正确的邮政编码', trigger: ['blur', 'change'] }
      ],
      Remark: [
        { required: false, message: '请输入备注', trigger: ['blur', 'change'] },
        { max: 500, message: '备注不能超过500个字符', trigger: ['blur', 'change'] }
      ]
    })

    // 级联选择器数据
    const receiverTypeCascaderOptions = ref([])
    const locationCascaderOptions = ref([])
    // 医院等级和级别数据
    const hospitalGradeList = ref([])
    const hospitalLevelList = ref([])

    /**
     * 加载收货方类型选项
     */
    const loadReceiverTypeOptions = async () => {
      try {
        const response = await selectorApi.ReceiverTypeSelect()
        if (response.data) {
          receiverTypeCascaderOptions.value = response.data || []
        }
      } catch (error) {
        console.error('加载收货方类型选项失败:', error)
        // 确保在出错时也有默认值
        receiverTypeCascaderOptions.value = []
      }
    }

    /**
     * 加载省市县级联选项
     */
    const loadLocationOptions = async () => {
      try {
        const response = await selectorApi.ProvinceCityCountySelect()
        if (response.data) {
          locationCascaderOptions.value = response.data || []
        }
      } catch (error) {
        console.error('加载省市县选项失败:', error)
      }
    }

    /**
     * 加载医院等级选项
     */
    const loadHospitalGradeOptions = async () => {
      try {
        const response = await selectorApi.getDicts('HospitalGrade')
        if (response.data && response.data.success) {
          hospitalGradeList.value = response.data.data || []
        }
      } catch (error) {
        console.error('加载医院等级选项失败:', error)
      }
    }

    /**
     * 加载医院级别选项
     */
    const loadHospitalLevelOptions = async () => {
      try {
        const response = await selectorApi.getDicts('HospitalLevel')
        if (response.data && response.data.success) {
          hospitalLevelList.value = response.data.data || []
        }
      } catch (error) {
        console.error('加载医院级别选项失败:', error)
      }
    }

    /**
     * 收货方类型级联变化处理
     */
    const handleReceiverTypeChange = (value) => {

      if (value && value.length > 0) {

        if (receiverTypeCascaderOptions.value && Array.isArray(receiverTypeCascaderOptions.value)) {
          const receiverType = receiverTypeCascaderOptions.value.find(item => item.value === value[0])
          // 判断是否为医疗机构类型
          showHospitalFields.value = receiverType.code === '30'
        } else {
          showHospitalFields.value = false
        }
        // 设置收货方类型ID为最后一级的值
        distributorForm.ReceiverTypeId = value[value.length - 1]

        // 如果不是医疗机构，清空医院相关字段
        if (!showHospitalFields.value) {
          distributorForm.HospitalGradeId = null
          distributorForm.HospitalLevelId = null
        }
      } else {
        showHospitalFields.value = false
        distributorForm.ReceiverTypeId = null
        distributorForm.HospitalGradeId = null
        distributorForm.HospitalLevelId = null
      }
    }

    /**
     * 地理位置级联变化处理
     * 支持选择任意级别：一级（省份）、二级（城市）、三级（区县）
     */
    const handleLocationChange = (value) => {
      // 重置所有地理位置字段
      distributorForm.ProvinceId = null
      distributorForm.CityId = null
      distributorForm.CountyId = null

      if (value && value.length > 0) {
        // 根据选择的级别设置对应的ID
        if (value.length >= 1) {
          distributorForm.ProvinceId = value[0] // 省份
        }
        if (value.length >= 2) {
          distributorForm.CityId = value[1] // 城市
        }
        if (value.length >= 3) {
          distributorForm.CountyId = value[2] // 区县
        }
      }
    }

    /**
     * 重置表单
     */
    const resetForm = () => {
      // 重置表单数据
      Object.assign(distributorForm, {
        SdrCode: '',
        Code: '',
        Name: '',
        ProvinceId: null,
        CityId: null,
        CountyId: null,
        ReceiverTypeId: null,
        UnifiedSocialCreditCode: '',
        Address: '',
        Telephone: '',
        EMail: '',
        PostalCode: '',
        NetAddress: '',
        StopTime: null,
        HospitalGradeId: null,
        HospitalLevelId: null,
        Remark: '',
        receiverTypeCascader: [],
        locationCascader: []
      })

      // 重置显示状态
      showHospitalFields.value = false

      // 清除表单验证
      nextTick(() => {
        setTimeout(() => {
          distributorFormRef.value?.clearValidate()
        }, 100)
      })
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = async (id) => {
      try {
        const response = await receiverApi.getReceiver(id)
        if (response.data && response.data.success) {
          const data = response.data.data

          // 填充表单数据
          Object.assign(distributorForm, data)

          // 设置级联选择器的值（根据实际数据结构调整）
          if (data.ReceiverTypeId) {
            // 这里需要根据实际的收货方类型数据结构来设置级联值
            distributorForm.receiverTypeCascader = [data.ReceiverTypeId]
          }

          if (data.ProvinceId || data.CityId || data.CountyId) {
            const locationArray = []
            if (data.ProvinceId) locationArray.push(data.ProvinceId)
            if (data.CityId) locationArray.push(data.CityId)
            if (data.CountyId) locationArray.push(data.CountyId)
            distributorForm.locationCascader = locationArray
          }

          // 设置医院字段显示状态
          showHospitalFields.value = data.ReceiverTypeId && data.ReceiverTypeId.includes('medical')
        } else {
          ElMessage.error('加载数据失败')
        }
      } catch (error) {
        console.error('加载记录数据失败:', error)
        ElMessage.error('加载数据失败')
      }
    }

    /**
     * 保存数据
     */
    const handleSave = async () => {
      try {
        // 表单验证
        const valid = await distributorFormRef.value?.validate()
        if (!valid) return

        saveLoading.value = true

        // 准备保存数据
        const saveData = { ...distributorForm }

        // 移除级联选择器辅助字段
        delete saveData.receiverTypeCascader
        delete saveData.locationCascader

        console.log('保存数据:', saveData)

        // 调用API保存数据
        let response
        if (isEdit.value) {
          // 编辑模式
          response = await receiverApi.editReceiver(saveData)
        } else {
          // 新增模式
          response = await receiverApi.addReceiver(saveData)
        }

        if (!response.data.success) {
          throw new Error(response.data.message || '保存失败')
        }

        ElMessage.success(isEdit.value ? '更新成功' : '保存成功')

        // 触发成功事件
        emit('success')

        // 关闭对话框
        handleClose()

      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败')
      } finally {
        saveLoading.value = false
      }
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 组件挂载时加载选项数据
    onMounted(() => {
      loadReceiverTypeOptions()
      loadLocationOptions()
      loadHospitalGradeOptions()
      loadHospitalLevelOptions()
    })

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }

        // 延迟清除验证状态
        nextTick(() => {
          setTimeout(() => {
            distributorFormRef.value?.clearValidate()
          }, 100)
        })
      }
    })

    return {
      distributorFormRef,
      saveLoading,
      isEdit,
      dialogVisible,
      showHospitalFields,
      distributorForm,
      formRules,
      receiverTypeCascaderOptions,
      locationCascaderOptions,
      hospitalGradeList,
      hospitalLevelList,
      handleReceiverTypeChange,
      handleLocationChange,
      handleSave,
      handleClose,
      resetForm,
      loadReceiverTypeOptions,
      loadLocationOptions,
      loadHospitalGradeOptions,
      loadHospitalLevelOptions
    }
  }
}
</script>
