<template>
    <div>
      <Modal v-model="showParentHospital" :title="$t('distributorAll.parentHospital')" width="1000">
        <div slot="close">
          <Icon type="ios-close-empty" @click="closeChildUpstream"></Icon>
        </div>
        <Row class="ivu-row-Padding-5 ivu-row-Bottom">

            <Col span="4">
            <!-- 省份/城市 -->
            <Cascader :data="cityList" v-model="provinceAndCity" :placeholder="$t('system.provinceAndCity')" change-on-select @on-change="changeshowChildUpstream" clearable></Cascader>
            </Col>
            <Col span="4">
            <Input v-model="filter.DistributorName" :placeholder="$t('drugstoreNearHospital.hospital')" clearable />
            </Col>
            <!-- GBCode -->
            <Col span="4">
            <Input v-model="filter.GBCode" placeholder="Code" clearable />
            </Col>
            <Col span="6">
            <!-- 查询 -->
            <Button icon="ios-search-strong" @click="search" class="helpCircledBut">{{$t('system.search')}}</Button>
            <tips></tips>
            </Col>
        </Row>
        <Row>
          <Col span="24">
          <Table stripe size="small" :columns="distributorColumns" :data="distributorList" @on-sort-change="handleSortChange"></Table>
          </Col>
        </Row>

        <Row class="ivu-row-Top">
          <Col span="24">
          <Page :total="filter.totalCount" :current="filter.Page"  :page-size-opts=[10,20,50,100]  :page-size="filter.Per" size="small" show-total show-elevator show-sizer @on-change="changePage" @on-page-size-change="changeProductPageSize" class="primary_R"></Page>
          </Col>
        </Row>
        <div slot="footer">
          <!-- 关闭 -->
          <Button @click="closeChildUpstream">{{$t('system.close')}}</Button>
        </div>
      </Modal>
    </div>
  </template>
  <script>

    export default {
        props: {
            noSelectUpstreams: Array // 禁止选择流向商业列表
        },
        data () {
            return {
                showParentHospital: false,
                provinceAndCity: [],
                cityList: [],
                filter: {
                    Page: 1,
                    Per: 10
                },
                distributorColumns: [
                    {
                        title: this.$t('system.no'), // 序号
                        width: 70,
                        align: 'center',
                        render: (h, params) => {
                            return h(
                                'span',
                                params.index + (this.filter.Page - 1) * this.filter.Per + 1
                            );
                        }
                    },
                    {
                        title: this.$t('system.province'), // 省份
                        key: 'ProvinceName',
                        sortable: 'custom',
                        width: 100
                    },
                    {
                        title: this.$t('system.city'), // 城市
                        key: 'CityName',
                        sortable: 'custom',
                        width: 100
                    },
                    {
                        title: 'Code',
                        key: 'GBCode',
                        align: 'center',
                        sortable: 'custom'
                    },
                    {
                        title: this.$t('drugstoreNearHospital.hospital'),
                        key: 'Name',
                        align: 'left',
                        sortable: 'custom'
                    },
                    {
                        title: this.$t('system.action'), // 操作
                        width: 70,
                        render: (h, params) => {
                            return h('div', [
                                h(
                                    'Tooltip',
                                    {
                                        props: {
                                            transfer: true,
                                            placement: 'top',
                                            content: this.$t('system.select') // 选择
                                        }
                                    },
                                    [
                                        h('Icon', {
                                            props: {
                                                type: 'ios-checkmark-outline',
                                                size: '20'
                                            },
                                            style: {
                                                marginRight: '5px',
                                                cursor: 'pointer'
                                            },
                                            on: {
                                                click: () => {
                                                    this.selestReceiverContractor(params.row);
                                                }
                                            }
                                        })
                                    ]
                                )
                            ]);
                        }
                    }
                ],
                distributorList: []
            };
        },
        methods: {
            // 关闭弹框
            closeChildUpstream () {
                this.showParentHospital = false;
                this.clear();
            },
            // 清空查询条件
            clear () {
                this.filter.Page = 1
                this.filter.Per = 10
                this.filter.DistributorName = '';
                this.filter.GBCode = '';
                this.filter.provinceID = '';
                this.filter.cityID = '';
                this.provinceAndCity = [];
            },
            // 弹框显示
            parentHospitalShow () {
                this.showParentHospital = true;
                this.initCity();
                this.search();
            },
            // 初始化省市
            initCity () {
                this.$http
                    .get('/Location/QueryProvinceCityCascader')
                    .then(response => {
                        this.cityList = response.data;
                });
            },
            search () {
                this.filter.Page = 1;
                this.handleQueryDistributorList();
            },
            handleQueryDistributorList () {
                this.filter.ExcludeDistributor = this.noSelectUpstreams;
                this.$http
                    .get('/Receiver/QueryHospitalWithoutDataAuthority', { params: this.filter })
                    .then(response => {
                        this.distributorList = response.data.Models;
                        this.filter.totalCount = response.data.TotalCount;
                });
            },
            // 选择表格行
            selestReceiverContractor (row) {
                this.$emit('selestParent', row);
                this.closeChildUpstream();
            },
            // 城市处理查询条件
            changeshowChildUpstream (value, selectedData) {
                let [provinceID, cityID] = selectedData.map(o => o.value);
                this.filter.provinceID = provinceID;
                this.filter.cityID = cityID;
            },
            // 排序
            handleSortChange (column) {
                if (column.key === 'ProvinceName') {
                    this.filter.OrderBy = [`Province.NameCn ${column.order}`];
                } else if (column.key === 'CityName') {
                    this.filter.OrderBy = [`City.NameCn ${column.order}`];
                } else if (column.key === 'ReceiverTypeName') {
                    this.filter.OrderBy = [`ReceiverType.Name ${column.order}`];
                } else if (column.key === 'DistributorTypeDesc') {
                    this.filter.OrderBy = [`EnumDistributorType ${column.order}`];
                } else {
                    this.filter.OrderBy = [`${column.key} ${column.order}`];
                }
                this.handleQueryDistributorList();
            },
            changePage (value) {
                this.filter.Page = value;
                this.handleQueryDistributorList();
            },
            changeProductPageSize (value) {
                this.filter.Per = value;
                this.filter.Page = 1;
                this.handleQueryDistributorList();
            },
            clearForm () {
                this.search();
            }
        }
    };
  </script>
