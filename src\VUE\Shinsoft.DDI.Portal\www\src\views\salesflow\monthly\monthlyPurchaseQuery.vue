<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>数据查询</el-breadcrumb-item>
        <el-breadcrumb-item>月数据查询</el-breadcrumb-item>
        <el-breadcrumb-item>月采购查询</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="8" type="flex">
        <el-col :span="4">
          <el-date-picker
            v-model="filter.purchaseDate"
            type="date"
            placeholder="采购日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            placement="bottom-start"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.queryCode"
            placeholder="查询Code"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.senderProvinceCity"
            :options="cityList"
            placeholder="发货方省份/城市"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.senderName"
            placeholder="发货方"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-cascader
            v-model="filter.receiverProvinceCity"
            :options="cityList"
            placeholder="收货方省份/城市"
            clearable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.receiverName"
            placeholder="收货方名称"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.receiverPHCode"
            placeholder="收货方PHCode"
            clearable
          />
        </el-col>
        <el-col :span="8">
          <el-cascader
            v-model="filter.productCascader"
            :options="productOptions"
            :props="cascaderProps"
            placeholder="产品名称/规格"
            clearable
            filterable
            style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-input
            v-model="filter.batchNo"
            placeholder="批号"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="monthlyPurchaseList"
        border
        stripe
        size="small"
        v-loading="loading"
        element-loading-text="数据加载中..."
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" :index="getTableIndex" />
        <el-table-column prop="senderName" label="发货方名称" min-width="150" fixed="left" />
        <el-table-column prop="receiverName" label="收货方名称" min-width="150" fixed="left" />
        <el-table-column prop="productShortName" label="产品名称" width="120" fixed="left" />
        <el-table-column prop="categoryName" label="产品规格" width="120" fixed="left" />
        <el-table-column prop="senderCode" label="发货方Code" width="120" />
        <el-table-column prop="senderProvince" label="发货方省份" width="120" />
        <el-table-column prop="senderCity" label="发货方城市" width="120" />
        <el-table-column prop="receiverProvince" label="收货方省份" width="120" />
        <el-table-column prop="receiverCity" label="收货方城市" width="120" />
        <el-table-column prop="receiverCode" label="收货方Code" width="120" />
        <el-table-column prop="batchNo" label="批号" width="120" />
        <el-table-column prop="purchaseDate" label="采购日期" width="120" />
        <el-table-column prop="purchaseQuantity" label="采购数量" width="100" align="right" />
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'MonthlyPurchaseQuery',
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        purchaseDate: '',
        queryCode: '',
        senderProvinceCity: [],
        senderName: '',
        receiverProvinceCity: [],
        receiverName: '',
        receiverPHCode: '',
        productCascader: [],
        batchNo: ''
      },
      // 产品级联选择器配置
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        expandTrigger: 'hover'
      },
      // 产品选项数据
      productOptions: [
        {
          value: 'amoxicillin',
          label: '阿莫西林胶囊',
          children: [
            { value: '0.25g*24', label: '0.25g*24粒' },
            { value: '0.5g*12', label: '0.5g*12粒' }
          ]
        },
        {
          value: 'cephalexin',
          label: '头孢氨苄胶囊',
          children: [
            { value: '0.25g*24', label: '0.25g*24粒' },
            { value: '0.5g*12', label: '0.5g*12粒' }
          ]
        },
        {
          value: 'azithromycin',
          label: '阿奇霉素片',
          children: [
            { value: '0.25g*6', label: '0.25g*6片' },
            { value: '0.5g*3', label: '0.5g*3片' }
          ]
        }
      ],
      // 城市级联数据
      cityList: [
        {
          value: 'beijing',
          label: '北京市',
          children: [
            { value: 'dongcheng', label: '东城区' },
            { value: 'xicheng', label: '西城区' },
            { value: 'chaoyang', label: '朝阳区' },
            { value: 'fengtai', label: '丰台区' }
          ]
        },
        {
          value: 'shanghai',
          label: '上海市',
          children: [
            { value: 'huangpu', label: '黄浦区' },
            { value: 'xuhui', label: '徐汇区' },
            { value: 'changning', label: '长宁区' },
            { value: 'jingan', label: '静安区' }
          ]
        },
        {
          value: 'guangdong',
          label: '广东省',
          children: [
            { value: 'guangzhou', label: '广州市' },
            { value: 'shenzhen', label: '深圳市' },
            { value: 'dongguan', label: '东莞市' },
            { value: 'foshan', label: '佛山市' }
          ]
        }
      ],
      totalCount: 0,
      monthlyPurchaseList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          senderName: '华北制药集团',
          receiverName: '北京同仁堂',
          productShortName: '阿莫西林胶囊',
          categoryName: '0.25g*24粒',
          senderCode: 'HBZJ001',
          senderProvince: '河北省',
          senderCity: '石家庄市',
          receiverProvince: '北京市',
          receiverCity: '东城区',
          receiverCode: 'BJTR001',
          batchNo: 'B20240120001',
          purchaseDate: '2024-01-20',
          purchaseQuantity: 500
        },
        {
          id: 2,
          senderName: '石药集团',
          receiverName: '上海医药公司',
          productShortName: '头孢氨苄胶囊',
          categoryName: '0.25g*24粒',
          senderCode: 'SYZY001',
          senderProvince: '河北省',
          senderCity: '石家庄市',
          receiverProvince: '上海市',
          receiverCity: '黄浦区',
          receiverCode: 'SHYY001',
          batchNo: 'B20240120002',
          purchaseDate: '2024-01-21',
          purchaseQuantity: 200
        },
        {
          id: 3,
          senderName: '扬子江药业集团',
          receiverName: '广州医药有限公司',
          productShortName: '阿奇霉素片',
          categoryName: '0.25g*6片',
          senderCode: 'YZJY001',
          senderProvince: '江苏省',
          senderCity: '泰州市',
          receiverProvince: '广东省',
          receiverCity: '广州市',
          receiverCode: 'GZYY001',
          batchNo: 'B20240120003',
          purchaseDate: '2024-01-22',
          purchaseQuantity: 300
        }
      ]
    }
  },
  computed: {
    // 获取选中的产品名称
    selectedProductName() {
      if (this.filter.productCascader && this.filter.productCascader.length > 0) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        return productOption ? productOption.label : ''
      }
      return ''
    },
    // 获取选中的产品规格
    selectedProductSpec() {
      if (this.filter.productCascader && this.filter.productCascader.length > 1) {
        const productOption = this.productOptions.find(item => item.value === this.filter.productCascader[0])
        if (productOption && productOption.children) {
          const specOption = productOption.children.find(item => item.value === this.filter.productCascader[1])
          return specOption ? specOption.label : ''
        }
      }
      return ''
    }
  },
  mounted() {
    this.search();
  },
  methods: {
    // 计算表格序号（考虑分页）
    getTableIndex(index) {
      return (this.filter.page - 1) * this.filter.per + index + 1
    },

    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadMonthlyPurchaseList();
    },

    // 加载月采购列表数据
    loadMonthlyPurchaseList() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.monthlyPurchaseList;

        // 根据产品名称过滤
        if (this.filter.productName) {
          filteredList = filteredList.filter(item =>
            item.productName.includes(this.filter.productName)
          );
        }

        // 根据供应商名称过滤
        if (this.filter.supplierName) {
          filteredList = filteredList.filter(item =>
            item.supplierName.includes(this.filter.supplierName)
          );
        }

        // 根据采购日期过滤
        if (this.filter.purchaseDate) {
          filteredList = filteredList.filter(item =>
            item.purchaseDate === this.filter.purchaseDate
          );
        }

        // 根据发货方名称过滤
        if (this.filter.senderName) {
          filteredList = filteredList.filter(item =>
            item.senderName.includes(this.filter.senderName)
          );
        }

        // 根据收货方名称过滤
        if (this.filter.receiverName) {
          filteredList = filteredList.filter(item =>
            item.receiverName.includes(this.filter.receiverName)
          );
        }

        // 根据产品级联选择器过滤
        if (this.filter.productCascader && this.filter.productCascader.length > 0) {
          const productName = this.selectedProductName;
          const productSpec = this.selectedProductSpec;

          filteredList = filteredList.filter(item => {
            let match = true;
            if (productName && item.productShortName) {
              match = match && item.productShortName.includes(productName);
            }
            if (productSpec && item.categoryName) {
              match = match && item.categoryName.includes(productSpec);
            }
            return match;
          });
        }

        // 根据批号过滤
        if (this.filter.batchNo) {
          filteredList = filteredList.filter(item =>
            item.batchNo.includes(this.filter.batchNo)
          );
        }

        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.monthlyPurchaseList = filteredList.slice(start, end);

        this.loading = false;
      }, 500);
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadMonthlyPurchaseList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.page = page;
      this.loadMonthlyPurchaseList();
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    }
  }
}
</script>
