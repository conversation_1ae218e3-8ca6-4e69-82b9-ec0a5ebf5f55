<!--
/**
 * 沟通日志对话框组件
 * 用于DDI监控界面的沟通日志管理
 * 包含日志列表展示和新增日志功能
 */
-->
<template>
  <el-dialog
    title="沟通日志"
    v-model="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 沟通日志列表 -->
    <div class="communication-log-list">
      <el-table 
        :data="communicationLogRecords" 
        border 
        size="default" 
        class="communication-log-table"
        max-height="300"
      >
        <el-table-column prop="logTime" label="日志时间" width="180" />
        <el-table-column prop="content" label="内容" min-width="400" />
        <el-table-column label="操作" width="80" align="center">
          <template #default="scope">
            <el-tooltip content="置顶" placement="top">
              <el-button
                icon="Top"
                circle
                size="small"
                @click="topCommunicationLog(scope.row)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加沟通日志表单 -->
    <div class="communication-log-form">
      <el-form :model="communicationLogForm" label-width="100px" size="default">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约时间">
              <el-date-picker
                v-model="communicationLogForm.appointmentTime"
                type="datetime"
                placeholder="选择预约时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="沟通日期">
              <el-date-picker
                v-model="communicationLogForm.communicationDate"
                type="datetime"
                placeholder="选择沟通日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="内容">
              <el-input
                v-model="communicationLogForm.content"
                type="textarea"
                :rows="4"
                placeholder="请输入沟通内容"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item>
              <el-button
                type="primary"
                @click="saveCommunicationLog"
                icon="Check"
                size="small"
              >
                保存
              </el-button>
              <el-button @click="resetForm" size="small">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="small">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'CommunicationLogDialog',
  props: {
    // 控制对话框显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 经销商信息
    distributorInfo: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  data() {
    return {
      // 沟通日志表单
      communicationLogForm: {
        appointmentTime: '',
        communicationDate: '',
        content: ''
      },
      // 沟通日志记录
      communicationLogRecords: [
        // 模拟数据
        {
          logTime: '2024-01-20 15:30:00',
          content: '与经销商沟通DDI系统部署进展，确认服务器配置要求。',
          appointmentTime: '2024-01-20 14:00:00',
          communicationDate: '2024-01-20 15:30:00'
        },
        {
          logTime: '2024-01-19 10:15:00',
          content: '讨论数据同步频率和异常处理机制，经销商提出每小时同步一次的需求。',
          appointmentTime: '2024-01-19 09:30:00',
          communicationDate: '2024-01-19 10:15:00'
        },
        {
          logTime: '2024-01-18 16:45:00',
          content: '培训经销商技术人员使用DDI监控界面，解答相关操作问题。',
          appointmentTime: '2024-01-18 16:00:00',
          communicationDate: '2024-01-18 16:45:00'
        }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      }
    }
  },
  methods: {
    // 保存沟通日志
    saveCommunicationLog() {
      if (!this.communicationLogForm.content.trim()) {
        this.$message.warning('请输入沟通内容')
        return
      }

      const logRecord = {
        logTime: new Date().toLocaleString(),
        content: this.communicationLogForm.content,
        appointmentTime: this.communicationLogForm.appointmentTime,
        communicationDate: this.communicationLogForm.communicationDate
      }

      this.communicationLogRecords.unshift(logRecord)

      // 清空表单
      this.resetForm()

      this.$message.success('沟通日志保存成功')
    },

    // 置顶沟通日志
    topCommunicationLog(row) {
      const index = this.communicationLogRecords.findIndex(item => item === row)
      if (index > 0) {
        this.communicationLogRecords.splice(index, 1)
        this.communicationLogRecords.unshift(row)
        this.$message.success('置顶成功')
      }
    },

    // 重置表单
    resetForm() {
      this.communicationLogForm = {
        appointmentTime: '',
        communicationDate: '',
        content: ''
      }
    },

    // 关闭对话框
    handleClose() {
      this.resetForm()
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
/* 沟通日志列表样式 */
.communication-log-list {
  margin-bottom: 20px;
}

.communication-log-table {
  width: 100%;
}

/* 沟通日志表单样式 */
.communication-log-form {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

/* 对话框底部样式 */
.dialog-footer {
  text-align: right;
}

/* 表格样式优化 */
.communication-log-table :deep(.el-table__header) {
  background-color: #fafafa;
}

.communication-log-table :deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

/* 表单样式优化 */
.communication-log-form .el-form-item {
  margin-bottom: 18px;
}

.communication-log-form .el-form-item:last-child {
  margin-bottom: 0;
}

/* 输入框样式符合系统风格 */
.communication-log-form .el-input__wrapper {
  border-radius: 4px;
}

.communication-log-form .el-textarea__inner {
  border-radius: 4px;
  resize: vertical;
  min-height: 100px;
}

/* 按钮样式 */
.communication-log-form .el-button + .el-button {
  margin-left: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .communication-log-form {
    padding: 15px;
  }
  
  .communication-log-form .el-col {
    margin-bottom: 10px;
  }
}
</style>
