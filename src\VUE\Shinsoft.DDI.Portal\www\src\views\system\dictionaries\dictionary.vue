<!--字典管理 - 已转换为Element Plus组件-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>{{ $t('main.mainSystemManagement') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('main.systemSetting') }}</el-breadcrumb-item>
        <el-breadcrumb-item>{{ $t('main.mainDictionaryManagement') }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="4">
          <el-input
            v-model="filter.name"
            :placeholder="$t('system.dictionaryName')"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-button icon="Search" @click="search" :loading="loading">{{ $t('system.search') }}</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="handleDictionaryAdd()">新增字典</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="parentDictionaryList"
        stripe
        size="small"
        @sort-change="handleSortChange"
        @expand-change="expandChange"
        ref="refTable"
        :expand-row-keys="expandedRows"
        row-key="id"
        style="width: 100%"
      >
        <el-table-column type="expand" width="55" fixed>
          <template #default="{ row }">
            <div style="margin-left: 40px;">
              <el-table
                :data="row.Children"
                size="small"
                style="width: 100%;"
                :show-header="true"
              >
                <el-table-column prop="name" label="字典项名称" min-width="200" show-overflow-tooltip />
                <el-table-column prop="code" label="字典项编码" min-width="150" show-overflow-tooltip />
                <el-table-column label="操作" width="120" align="center">
                  <template #default="{ row: childRow }">
                    <el-tooltip content="编辑" placement="top">
                      <el-button icon="Edit" circle size="small" @click="handleDictionaryEdit(childRow)" />
                    </el-tooltip>
                    <el-tooltip content="删除" placement="top">
                      <el-button icon="Delete" circle size="small" @click="handleDictionaryDelete(childRow)" />
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="字典名称" min-width="200" sortable show-overflow-tooltip />
        <el-table-column prop="code" label="字典编码" min-width="150" sortable show-overflow-tooltip />
        <el-table-column label="子项数量" width="100" align="center">
          <template #default="{ row }">
            {{ row.Children ? row.Children.length : 0 }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="编辑" placement="top">
              <el-button icon="Edit" circle size="small" @click="handleDictionaryEdit(row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 字典编辑弹窗组件 -->
    <dictionary-dialog ref="dictionaryDialog" @refresh="search" />
  </div>
</template>

<script>
import dictionaryDialog from './components/dictionaryDialog.vue';

export default {
  name: 'Dictionary',
  components: {
    dictionaryDialog
  },
  data() {
    return {
      loading: false,
      totalCount: 0,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        order: [],
        name: ''
      },
      dictionaryList: [],
      parentDictionaryList: [], // 父级字典列表
      expandedRows: [], // 展开的行
      expands: []
    };
  },
  mounted() {
    // TODO: 原有的权限初始化已临时注释，正式环境需要恢复
    // this.behaviors.behaviorsSession(localStorage.behaviors, "dictionaryAdministration");
    this.search();
  },
  methods: {
    // 表格行样式
    getRowClass({ row, rowIndex }) {
      let res = [];
      // 暂时注释掉，因为后端没有返回HasChildren字段
      // if (row.HasChildren == 0) {
      //   res.push("row-expand-cover");
      // }
      return res;
    },
    // 搜索
    search() {
      this.filter.page = 1;
      this.queryDictionaryList();
    },
    // 查询字典列表（只查询父级字典）
    queryDictionaryList() {
      this.loading = true;

      // 构建查询参数，不传递ParentId参数以查询父级字典
      const queryParams = {
        Page: this.filter.page,
        Per: this.filter.per,
        Name: this.filter.name,
        Description: this.filter.description
      };

      // 将order数组转换为Order字符串
      if (this.filter.order && this.filter.order.length > 0) {
        queryParams.order = this.filter.order.join(', ');
      }


      this.$http
        .get("/BizMasterData/QueryBizDict", { params: queryParams })
        .then(response => {
          // axios拦截器已经处理了嵌套的data结构
          const success = response.data?.success;
          const datas = response.data?.datas;
          const total = response.data?.total;
          const messages = response.data?.messages;

          if (success) {
            // 为每个父级字典初始化Children数组
            const dataList = datas || [];
            dataList.forEach(item => {
              item.Children = [];
            });
            this.parentDictionaryList = dataList;
            this.dictionaryList = dataList; // 保持兼容性
            this.totalCount = total || 0;

            if (dataList.length === 0) {
              console.log('查询结果为空，没有找到父级字典');
            }
          } else {
            // 显示具体的错误信息
            let errorMessage = '查询字典列表失败';
            if (messages && messages.length > 0) {
              errorMessage = messages.join(', ');
            } else if (response.data?.message) {
              errorMessage = response.data.message;
            }
            this.$message.error(errorMessage);
          }
        })
        .catch(error => {
          let errorMessage = '查询字典列表失败';
          if (error.response?.data?.messages?.length > 0) {
            errorMessage = error.response.data.messages.join(', ');
          } else if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error.message) {
            errorMessage = error.message;
          }
          this.$message.error(errorMessage);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 改变页面大小
    changePageSize(value) {
      this.filter.per = value;
      this.filter.page = 1;
      this.queryDictionaryList();
    },
    // 改变页码
    changePage(value) {
      this.filter.page = value;
      this.queryDictionaryList();
    },
    // 排序变化
    handleSortChange(column) {
      if (column.prop != null) {
        const order = column.order === 'ascending' ? 'ASC' : 'DESC';
        switch (column.prop) {
          case "ParentName":
            this.filter.order = [`Parent.Name ${order}`];
            break;
          default:
            this.filter.order = [`${column.prop} ${order}`];
        }
      } else {
        this.filter.order = [];
      }
      this.queryDictionaryList();
    },
    // 展开收起
    expandChange(row, expandedRows) {
      this.$http
        .get("/BizMasterData/QueryBizDict", {
          params: { ParentId: row.id }
        })
        .then(response => {
          console.log('获取子字典响应:', response.data);

          // axios拦截器已经处理了嵌套的data结构
          const success = response.data?.success;
          const datas = response.data?.datas;
          const messages = response.data?.messages;

          if (success) {
            const childList = datas || [];

            // 更新dictionaryList
            this.dictionaryList.forEach(item => {
              if (item.id == row.id) {
                item.Children = childList;
              }
            });

            // 同时更新parentDictionaryList
            this.parentDictionaryList.forEach(item => {
              if (item.id == row.id) {
                item.Children = childList;
              }
            });

            if (childList.length === 0) {
              console.log(`父字典 ${row.name} 没有子字典`);
            }

            // 确保只展开一行
            if (expandedRows.length > 1) {
              this.expands = [];
              if (row) {
                this.expands.push(row);
              }
              this.$refs.refTable.toggleRowExpansion(expandedRows[0]);
            } else {
              this.expands = [];
            }
          } else {
            // 显示具体的错误信息
            let errorMessage = '获取子字典失败';
            if (messages && messages.length > 0) {
              errorMessage = messages.join(', ');
            }
            console.error('获取子字典失败:', response.data);
            this.$message.error(errorMessage);
          }
        })
        .catch(error => {
          console.error('获取子字典出错:', error);
          let errorMessage = '获取子字典失败';
          if (error.response?.data?.messages?.length > 0) {
            errorMessage = error.response.data.messages.join(', ');
          } else if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error.message) {
            errorMessage = error.message;
          }
          this.$message.error(errorMessage);
        });
    },
    // 新增字典
    handleDictionaryAdd(row) {
      // row为null表示新增父级字典，否则新增子字典
      this.$refs.dictionaryDialog.showAddDictionary(row);
    },
    // 编辑字典
    handleDictionaryEdit(row) {
      this.$refs.dictionaryDialog.showEditDictionary(row);
    },
    // 删除字典
    handleDictionaryDelete(row) {
      // 检查是否为父级字典
      if (!row.parentId) {
        this.$message.warning('父级字典不能删除，只能删除字典项');
        return;
      }

      this.$confirm(
        this.$t("system.whetherDeleteDctionary"),
        this.$t("system.alter"),
        {
          confirmButtonText: this.$t("system.confirm"),
          cancelButtonText: this.$t("system.cancel"),
          type: "warning"
        }
      ).then(() => {
        // 删除字典项并重新排序
        this.$http
          .post("/BizMasterData/DeleteDictItem", null, { params: { id: row.id } })
          .then(response => {
            const success = response.data?.success;
            const messages = response.data?.messages;

            if (success) {
              this.$message.success(this.$t("system.deleteSuccess"));
              this.search();
            } else {
              // 显示具体的错误信息
              let errorMessage = '删除失败';
              if (messages && messages.length > 0) {
                errorMessage = messages.join(', ');
              } else if (response.data?.message) {
                errorMessage = response.data.message;
              }
              this.$message.error(errorMessage);
            }
          })
          .catch(error => {
            let errorMessage = '删除字典失败';
            if (error.response?.data?.messages?.length > 0) {
              errorMessage = error.response.data.messages.join(', ');
            } else if (error.response?.data?.message) {
              errorMessage = error.response.data.message;
            } else if (error.message) {
              errorMessage = error.message;
            }
            this.$message.error(errorMessage);
          });
      });
    }
  }
};
</script>
