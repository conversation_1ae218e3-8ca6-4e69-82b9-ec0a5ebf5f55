// 进销存管理--中英文
export const inventory = {
    'zhCN': {
        // 进销存管理
        inventoryMain: {
            mainInventory: '库存管理',
            mainInventoryDaysCalculation: '库存天数计算',
            mainDistributorInventoryDay: '流向商业库存天数',
            mainInventoryReport: '库存报告',
            mainInventoryManagement: '库存管理',
            mainInventoryGenerate: '计算报告',
            mainProductInventoryDay: '产品库存天数',
            mainInventoryEstimateMonth: '当月库存预估',
            mainDistributorProductReport: '按产品经销商统计',
            mainFlowNegativeStock: '负库存反馈',
            compare: '最小安全库存不能大于最大安全库存',
            recalculationSuccess: '确认是否重新计算所选周期的数据？',
            calculateInventoryByRecordMonth: '按记录月份计算库存',
            calculatedBySalesDate: '按销售日期计算库存',
            submitDate: '提交日期',
            updateDate: '更新日期',
            inventoryRecalculationSuccess: '此操作会重新计算所有品规，本月现有调整将会覆盖',
            yearAndMonthNotNull: '年月不能为空',
            dataIntegrity: '请填写完整的数据',
            InventoryNumNotNull: '库存数不能为空',
            inputNumber: '请输入数字',
            inputGreaterThanZero: '请输入大于零的数',
            inputMaximum: '库存最多能输入16位',
            whetherSave: '该操作会影响本月及后续月份的库存数据，确认保存该月库存数据吗？',
            negativeInventory: '本月库存存在负数，需调平后才可以进行保存'
        },
        // 库存天数计算
        distributorInventoryDay: {
            dateMonth: '记录月份',
            distributorActualType: '流向商业实际类型',
            isSafe: '是否安全',
            productDetailName: '产品简称',
            partingName: '分型名称',
            inventoryThisMonth: '当月库存',
            realtimeInventoryThisMonth: '当月实时库存',
            inventoryEstimateThisMonth: '当月库存预估',
            averageXSales: '近X月平均销量',
            inventoryDay: '库存天数',
            state: '状态',
            calculator: '计算库存报告',
            selectTime: '请选择年月',
            inventoryMonth: '库存月份',
            lastXTotalSales: '前X月总销量',
            startMonth: '起始时间',
            endMonth: '结束时间',
            averageSales: '平均销量',
            thisDDISales: '本月DDI销量',
            thisDDIPurchase: '本月DDI采购',
            statisticalProduct: '按产品统计',
            statisticalMaterialGroup: '按分型统计',
            dateYear: '年份',
            purchase: '进货',
            sales: '销货',
            inventory: '库存',
            january: '1月',
            february: '2月',
            march: '3月',
            april: '4月',
            may: '5月',
            june: '6月',
            july: '7月',
            august: '8月',
            september: '9月',
            october: '10月',
            november: '11月',
            december: '12月',
            selectMonth: '请选择月份！',
            recalculate: '重新计算',
            noLeveling: '未调平',
            yesLeveling: '已调平',
            whetherLeveling: '是否调平',
            inventoryUpdateTime: '实时库存更新日期',
            calculateSuccess: '计算库存报告成功！',
            national: '全国',
            productChannelTier: '渠道级别',
            receiverTypeName: '收货方类型',
            isFeedBack: '是否反馈',
            feedbackStatus: '反馈状态',
            roleLabel: '角色标签',
            feedBackUser: '反馈人',
            feedBackTime: '反馈日期',
            feedBackReason: '反馈原因',
            negativeStockFeedBackReason: '负数库存专用反馈',
            feedBackDesc: '反馈描述',
            feedBackRemark: '备注',
            feedBack: '反馈',
            feedBackReasonNotNull: '请选择反馈原因',
            feedBackRemarkNotNull: '请填写备注',
            feedBackRemarkTooLong: '备注长度超长',
            hasFeedBack: '已反馈',
            notFeedBack: '未反馈',
            cliskCountInventoryGenerate: '点击计算库存报告按钮后',
            cliskCountInventoryGenerateContent: '外勤手工填写的内容及生成的反馈报表将会被覆盖。',
            calculatingDate: '最新库存日期：',
            inventoryEstimateMonthQuantity: '当月库存预估合计：'
        },
        // 日库存差异核查
        inventoryInspect: {
            salesFlowCycle: '记录月份',
            productName: '产品简称',
            distributorName: '流向商业名称',
            productChannelTier: '渠道级别',
            monthInventory: '计算库存',
            theoryInventory: '理论库存',
            changedMonthInventory: '月底库存',
            DDIDailyInventory: 'DDI库存',
            DDIDailyInventoryDate: 'DDI日期',
            differentInventory: '原始差异',
            feedBackDifferent: '反馈后差异',
            responsibilityer: '负责人',
            inventoryEditTime: '库存更新日期',
            purchaseData: '进货数量',
            purchaseQuantity: '进货一线数据',
            purchaseChangeQuantity: '实际发货',
            purchaseDifferenceQuantity: '进货数量差异',
            salesData: '销货数量',
            salesFlowQuantity: '销货一线数据',
            salesFlowChangeQuantity: '实际销售',
            salesFlowDifferenceQuantity: '销货数量差异',
            updateTime: '更改日期',
            differenceRate: '原始差异率',
            chooseDirection: '选择在途流向',
            doesItExceedTheDifferenceRate: '是否超过差异率',
            doesItExceedTheDifferenceRateYes: '超过差异率百分比',
            doesItExceedTheDifferenceRateDeny: '未超过差异率百分比',
            numberOfRoutes: '在途数量',
            selectedDirectionOfFlow: '已选流向',
            distributorSalesFlowMonthlyMessage: '当前反馈原因为在途，至少要选择一条在途流向',
            noChooseFlowingInTheWay: '请选择在途流向'
        },
        inventoryList: {
            salesFlowCycleMonth: '记录月份',
            editor: '修改',
            productName: '产品简称',
            materialGroupName: '分型名称',
            lastCycleInventoryNum: '上月库存',
            lastCycleRealtimeInventory: '上月实时库存',
            deliveryThisMonth: '本月进货',
            sellGoods: '本月销货',
            stocktheMonth: '本月库存',
            stocktheMonthRealtimeInventory: '本月实时库存',
            normalPurchase: '正常购进',
            giveaway: '赠样',
            other: '其它',
            total: '合计',
            otherTypes: '其它类型',
            otherDescriptions: '其它描述',
            sales: '纯销',
            transfer: '调拨',
            validNameOrGBCodeRequired: '至少选择一个流向商业名称作为查询条件', // 或输入一个GBCode
            inventoryByBatchNumberReport: '批号库存报告',
            batchnumber: '批号',
            SumPurchaseQuantity: '本月进货合计',
            SumSaleQuantity: '本月销货合计'
        },
        inventoryReport: {
            allSelect: '全选',
            inventoryYear: '库存年份',
            inventoryMonth: '月份',
            inventoryLockInfo: '库存锁信息',
            inventoryMonthXSetUp: '库存月份X设定',
            month: '月份',
            xSetUp: 'X设定',
            january: '1月',
            february: '2月',
            march: '3月',
            april: '4月',
            may: '5月',
            june: '6月',
            july: '7月',
            august: '8月',
            september: '9月',
            october: '10月',
            november: '11月',
            december: '12月',
            provinceNameCn: '省份',
            cityNameCn: '城市',
            distributorName: '流向商业',
            employeeNameCn: '负责人',
            productReferred: '产品简称',
            materialGroupName: '分型名称',
            salesFlowCycleMonth: '记录月份',
            inventoryItemTypeName: '库存类型',
            quantity: '数量',
            changedQuantity: '数量',
            otherTypeName: '其他类型',
            otherDescription: '其他描述',
            distributorProvinceCity: '流向商业省份/城市',
            distributor: '流向商业',
            inventoryDate: '库存日期',
            productShortName: '产品简称',
            materialGroup: '分型',
            batchNo: '批号',
            distributorPhCode: 'Code',
            businessCompanyType: '商业公司类型',
            channelLevel: '渠道级别',
            distributorLevel: '流向商业级别',
            isAuthorized: '是否流向产品',
            authorized: '流向产品',
            distributorProvince: '流向商业省份',
            distributorCity: '流向商业城市',
            distributorType: '商业公司类型',
            queryCode: '流向ID',
            material: '物料',
            expiryDate: '有效期',
            EmployeeNameCn: '负责人',
            montnDataNotNull: '导出的月份不能为空',
            inventoryYearIsNotNull: '库存年份不能为空',
            inventoryMonthNotExist: '当前年份下缺少月份数据',
            xSetUpIsNotNull: '库存月份X设定不能为空',
            minusInventoryFeedBackTitle: '负库存反馈',
            recalculationCompleted: '重新计算完成。',
            inventoryEstimateTotal: '当月库存预估合计：',
            submitTime: '提交时间',
            lastEditTime: '更新时间',
            finalEditorName: '最后操作人',
            type: '类型',
            salesReturnPercentSetUpIsNotNull: '库存月份X设定不能为空'
        },
        safetyStockRange: {
            safetyStockRangeSetting: '安全库存范围设置',
            minimum: '最低',
            maximum: '最高',
            safeStockRangeTemplateDownload: '安全库存范围模板下载',
            receiverProductMaterialGroupNull: '产品/分型是必填项',
            receiverIDRequired: '请选择流向商业名称',
            minimumValueIncorrect: '最低安全值输入不正确',
            maximumValueIncorrect: '最高安全值输入不正确',
            inventoryAlarmSetting: '库存报警设置'
        },
        discrepancyRateSetting: {
            discrepancyRateNotNull: '日库存差异率不能为空'
        },
        inventoryEstimateSetting: {
            provinceName: '省份',
            materialGroupName: '分型',
            materialGroupNotNull: '请选择分型',
            provinceNotNull: '请选择省份',
            inventoryEstimateSettingAlert: '当前页面可对不同省份的品种进行忽略，忽略后的品种在计算库存预估时不会参与计算。'
        },
        inventoryBatchNumber: {
            dprovincename: '流向商业省份', //
            dcityname: '流向商业城市', //
            distributorname: '流向商业名称',
            productbatchnumber: '批号名称',
            purchaseOther: '采购其他',
            saleOther: '销售其它'
        }
    },
    'enUS': {
        // 进销存管理
        inventoryMain: {
            mainInventory: 'Inventory',
            mainInventoryDaysCalculation: 'Inventory Days',
            mainDistributorInventoryDay: 'Distributor Inventory Day',
            mainInventoryReport: 'Inventory Management',
            mainInventoryManagement: 'Inventory Management',
            mainInventoryGenerate: 'Inventory Generate',
            mainProductInventoryDay: 'Product Inventory Day',
            mainInventoryEstimateMonth: 'Estimate Inventory',
            mainDistributorProductReport: 'Distributor Product Report',
            mainFlowNegativeStock: 'NegativeStockFeedBack',
            compare: 'Minimum safety stock should not be greater than maximum safety stock',
            recalculationSuccess: 'Are you sure you want to recalculate the data of the current cycle?',
            calculateInventoryByRecordMonth: 'Calculate inventory by record month',
            calculatedBySalesDate: 'Calculate inventory by sales date',
            submitDate: 'Submit Date',
            updateDate: 'Update Date',
            inventoryRecalculationSuccess: 'This operation will recalculate all data, and all existing data adjustments will be covered. Do you want to continue?',
            yearAndMonthNotNull: 'Required',
            dataIntegrity: 'Please confirm that data has been completed.',
            InventoryNumNotNull: 'Required',
            inputNumber: 'Please input the number format',
            inputGreaterThanZero: 'Please enter a quantity greater than zero',
            inputMaximum: 'Maximum support for 16 digit number',
            whetherSave: 'This operation will affect the purchase, sale and storage data for this month and the following months. Do you confirm that the purchase, sale and storage data for that month are saved? ',
            negativeInventory: 'The inventory of this month has a negative number, which can only be saved after leveling'
        },
        // 库存天数计算
        distributorInventoryDay: {
            dateMonth: 'Cycle Month',
            distributorActualType: 'Distributor Actual Type',
            isSafe: 'Is Safe',
            productDetailName: 'Product Referred',
            partingName: 'Material Group',
            inventoryThisMonth: 'Current Month Inventory',
            realtimeInventoryThisMonth: 'Current Month Real - Time Inventory',
            inventoryEstimateThisMonth: 'Inventory Forecast for this Month',
            averageXSales: 'Average sales volume in recent X months',
            inventoryDay: 'Inventory Day',
            state: 'Status',
            calculator: 'Generate',
            selectTime: 'Please select year',
            inventoryMonth: 'Inventory Month',
            lastXTotalSales: 'Total sales volume in recent X months',
            startMonth: 'Start Time',
            endMonth: 'End Time',
            averageSales: 'Average Sale',
            thisDDISales: 'DDI Sale',
            thisDDIPurchase: 'DDI Purchase',
            statisticalProduct: 'Repory By Product',
            statisticalMaterialGroup: 'Report By Material Group',
            dateYear: 'Year',
            purchase: 'Purchase',
            sales: 'Sales',
            inventory: 'Inventory',
            january: 'Jan',
            february: 'Feb',
            march: 'Mar',
            april: 'Apr',
            may: 'May',
            june: 'Jun',
            july: 'Jul',
            august: 'Aug',
            september: 'Sep',
            october: 'Oct',
            november: 'Nov',
            december: 'Dec',
            selectMonth: 'Please select month',
            recalculate: 'Recalculate',
            noLeveling: 'No Leveling',
            yesLeveling: 'Already Leveling',
            whetherLeveling: 'Is Leveling',
            inventoryUpdateTime: 'Inventory EditTime',
            calculateSuccess: 'Success',
            national: 'National',
            productChannelTier: 'Tier',
            receiverTypeName: 'Receiver Type',
            isFeedBack: 'FeedBack',
            feedbackStatus: 'FeedBack Status',
            roleLabel: 'Role Label',
            feedBackUser: 'FeedBack User',
            feedBackTime: 'FeedBack Time',
            feedBackReason: 'FeedBack Reason',
            negativeStockFeedBackReason: 'Negative stock feedBack reason',
            feedBackDesc: 'FeedBack Description',
            feedBackRemark: 'Remark',
            feedBack: 'FeedBack',
            feedBackReasonNotNull: 'FeedBack Reason can not be empty',
            feedBackRemarkNotNull: 'Remark can not be empty',
            feedBackRemarkTooLong: 'Remark is too long',
            hasFeedBack: 'Has FeedBack',
            notFeedBack: 'Not FeedBack',
            cliskCountInventoryGenerate: 'After clicking the Calculate Inventory Report button',
            cliskCountInventoryGenerateContent: 'The field manual content and feedback reports generated will be covered.',
            calculatingDate: 'The latest inventory date:',
            inventoryEstimateMonthQuantity: 'Total inventory estimates for the month:'
        },
        // 库存差异核查
        inventoryInspect: {
            salesFlowCycle: 'Cycle Month',
            productName: 'Product Referred',
            distributorName: 'Distributor Name',
            productChannelTier: 'Channel Tier',
            monthInventory: 'Month Inventory',
            theoryInventory: 'theory Inventory',
            changedMonthInventory: 'Inventory at the end of month',
            DDIDailyInventory: 'DDI Daily Inventory',
            DDIDailyInventoryDate: 'DDI Inventory Date',
            differentInventory: 'Original Different',
            feedBackDifferent: 'Feed Back Different',
            responsibilityer: 'Representative',
            purchaseData: 'Purchase',
            purchaseQuantity: 'Calculate Purchase',
            purchaseChangeQuantity: 'Actual Purchase',
            purchaseDifferenceQuantity: 'Different',
            salesData: 'Sale',
            salesFlowQuantity: 'Calculate Sale',
            salesFlowChangeQuantity: 'Actual Sale',
            salesFlowDifferenceQuantity: 'Different',
            updateTime: 'FinalEditTime',
            differenceRate: 'Original Difference Rate',
            distributorSalesFlowMonthlyMessage: 'The reason for the current feedback is in the way, at least choose one in the way.',
            selectedDirectionOfFlow: 'Selected direction of flow',
            numberOfRoutes: 'Number of routes',
            doesItExceedTheDifferenceRate: 'Does it exceed the difference rate?',
            doesItExceedTheDifferenceRateYes: 'Percentage over difference rate',
            doesItExceedTheDifferenceRateDeny: 'Percentage of difference rate not exceeded',
            chooseDirection: 'Choose on-the-way direction',
            noChooseFlowingInTheWay: 'Please choose the direction on the way.'
        },
        inventoryList: {
            salesFlowCycleMonth: 'Cycle Month',
            editor: 'Edit',
            productName: 'Product Referred',
            materialGroupName: 'Material Group',
            lastCycleInventoryNum: 'Last Month Inventory',
            lastCycleRealtimeInventory: 'Last Month Real - time Inventory',
            deliveryThisMonth: 'Current Month Purchase',
            sellGoods: 'Current Month Sale',
            stocktheMonth: 'Current Month Inventory',
            stocktheMonthRealtimeInventory: 'Current Month Real - time Inventory',
            normalPurchase: 'Normal Purchase',
            giveaway: 'Gift samples',
            other: 'Other',
            total: 'Total',
            otherTypes: 'Other Type',
            otherDescriptions: 'Other Description',
            sales: 'Sales',
            transfer: 'Allocation',
            validNameOrGBCodeRequired: 'Select at least one flow to the business name or enter a Code',
            inventoryByBatchNumberReport: 'ProductBatch Inventory Report',
            batchnumber: 'Product Batch',
            SumPurchaseQuantity: 'Purchase Total',
            SumSaleQuantity: 'Sale Total'
        },
        inventoryReport: {
            allSelect: 'Select All',
            inventoryYear: 'Inventory Year',
            inventoryMonth: 'Inventory Month',
            inventoryLockInfo: 'Inventory Lock Information',
            inventoryMonthXSetUp: 'Inventory Month X Setting',
            january: 'Jan',
            february: 'Feb',
            march: 'Mar',
            april: 'Apr',
            may: 'May',
            june: 'Jun',
            july: 'Jul',
            august: 'Aug',
            september: 'Sep',
            october: 'Oct',
            november: 'Nov',
            december: 'Dec',
            provinceNameCn: 'Province',
            cityNameCn: 'City',
            distributorName: 'Distributor Name',
            employeeNameCn: 'Representative',
            productReferred: 'Product Code',
            materialGroupName: 'Material Group',
            salesFlowCycleMonth: 'Month',
            inventoryItemTypeName: 'Type',
            quantity: 'Quantity',
            changedQuantity: 'Quantity',
            otherTypeName: 'Other Type',
            otherDescription: 'Other Description',
            distributorProvinceCity: 'Distributor Province/City',
            distributor: 'Distributor',
            inventoryDate: 'Inventory Date',
            productShortName: 'Product Code',
            materialGroup: 'Material Group',
            batchNo: 'Batch Number',
            distributorPhCode: 'Code',
            businessCompanyType: 'Distributor Type',
            distributorLevel: 'Tier',
            channelLevel: 'Channel Tier',
            isAuthorized: 'Is Salesflow Product',
            authorized: 'Salesflow Product',
            distributorProvince: 'Distributor Province',
            distributorCity: 'Distributor City',
            distributorType: 'Distributor Type',
            queryCode: 'Query Id',
            material: 'Material',
            expiryDate: 'Expiry Date',
            EmployeeNameCn: 'Representative',
            montnDataNotNull: 'Required',
            inventoryYearIsNotNull: 'Required',
            inventoryMonthNotExist: 'Lack of monthly data in current year.',
            xSetUpIsNotNull: 'Required',
            minusInventoryFeedBackTitle: 'FeedBack',
            recalculationCompleted: 'Recalculation completed.',
            inventoryEstimateTotal: 'Total inventory estimates for the month : ',
            submitTime: 'Submit Time',
            lastEditTime: 'Last Edit Time',
            finalEditorName: 'Final Editor Name',
            type: 'Type'
        },
        safetyStockRange: {
            safetyStockRangeSetting: 'Safety Stock Range',
            minimum: 'Lowest',
            maximum: 'Highest',
            safeStockRangeTemplateDownload: 'Download Template',
            receiverProductMaterialGroupNull: 'Required',
            receiverIDRequired: 'Required',
            minimumValueIncorrect: 'The minimum safe stock format is incorrect.',
            maximumValueIncorrect: 'The maximum safe stock format is incorrect.',
            inventoryAlarmSetting: 'Inventory alarm setting'
        },
        discrepancyRateSetting: {
            discrepancyRateNotNull: 'Discrepancy rate can not be empty.'
        },
        inventoryEstimateSetting: {
            provinceName: 'Province',
            materialGroupName: 'Material Group',
            materialGroupNotNull: 'The materialgroup can not be empty.',
            provinceNotNull: 'The province can not be empty.',
            inventoryEstimateSettingAlert: 'Current pages can ignore the varieties of different provinces, and the neglected varieties will not participate in the calculation of inventory estimates.'
        },
        inventoryBatchNumber: {
            dprovincename: 'province',
            dcityname: 'city',
            distributorname: 'distributor name',
            productbatchnumber: 'batch number',
            purchaseOther: 'purchase other',
            saleOther: 'sale other'
        }
    }
}
export default inventory
