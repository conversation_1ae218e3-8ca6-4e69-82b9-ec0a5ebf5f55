<!--人员管理 作者：武祥-->
<template>
  <div>
    <Row class="ivu-row-Padding-55 ivu-row-BG ivu-row-Top">
      <Col span="24">
        <Breadcrumb>
          <!-- 系统管理 -->
          <BreadcrumbItem>{{ $t("main.mainSystemManagement") }}</BreadcrumbItem>
          <BreadcrumbItem>{{
            $t("main.mainAuthorityManagement")
          }}</BreadcrumbItem>
          <!-- 人员管理 -->
          <BreadcrumbItem>{{ $t("main.mainPersonne") }}</BreadcrumbItem>
        </Breadcrumb>
      </Col>
    </Row>
    <div class="ivu-row-BG">
      <Row class="ivu-row-Padding ivu-row-Top">
        <Col span="4">
          <Input
            v-model="filter.MUID"
            clearable
            :placeholder="$t('userlist.muid')"
          />
        </Col>
        <Col span="4">
          <Input
            v-model="filter.LoginName"
            clearable
            :placeholder="$t('userlist.loginName')"
          />
        </Col>
        <Col span="4">
          <!-- 姓名 -->
          <Input
            v-model="filter.Name"
            clearable
            :placeholder="$t('userlist.searchname')"
          />
        </Col>
        <Col span="4">
          <!-- 部门 -->
          <Select
                  v-model="filter.DepartmentID"
                  :placeholder="$t('userlist.department')"
                  clearable
                  readonly
                >
                  <Option
                    v-for="item in deptList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
              </Select>
        </Col>
        <Col span="4">
          <!-- 邮箱 -->
          <Input
            v-model="filter.Email"
            clearable
            :placeholder="$t('userlist.email')"
          />
        </Col>
        <Col span="4">
          <Input
            v-model="filter.Title"
            clearable
            :placeholder="$t('userlist.position')"
          />
        </Col>
      </Row>
        <Row class="ivu-row-Padding ivu-row-BG ivu-row-Top">
        <Col span="4">
          <!-- 员工状态 -->
          <Select
            v-model="filter.EnumEntityStatus"
            clearable
            :placeholder="$t('userlist.employeeStatus')"
          >
            <Option
              v-for="item in employeeStatus"
              :value="item.value"
              :key="item.value"
            >
              {{ item.label }}
            </Option>
          </Select>
        </Col>
        <Col span="4" v-if="behaviors.serverSrc.userlist.userlist_Search">
          <!-- 查询 -->
          <Button
            type="ghost"
            icon="search"
            @click="search"
            class="helpCircledBut"
          >
            {{ $t("system.search") }}
          </Button>
          <tips></tips>
        </Col>
      </Row>
      <Row
        class="ivu-row-Padding-5 ivu-row-Top ivu-row-Bottom"
        type="flex"
        justify="end"
        :gutter="10"
      >
        <Col span="2.5"  v-if="behaviors.serverSrc.userlist.userlist_Add">
          <Button
            class="primary_R"
            icon="ios-plus-outline"
            @click="handleAdd"
            >{{ $t("system.add") }}</Button
          >
        </Col>
        <Col span="2.5">
          <Button
            @click="handleShowExportModal"
            class="primary_R"
            icon="ios-download-outline"
            style="margin-right: 10px"
          >
            {{ $t("system.export") }}
          </Button>
        </Col>
      </Row>
    </div>
    <Row>
      <Col span="24">
        <Table
          stripe
          :columns="columns"
          :data="$store.state.user.userlist"
          size="small"
          @on-sort-change="handleSortChange"
        ></Table>
      </Col>
    </Row>
    <Row class="ivu-row-Top pageStyle">
      <Col span="24">
        <Page
          :total="$store.state.user.totalcount"
          @on-page-size-change="changePageSize"
          :page-size-opts="pageSizeOpts"
          :current="filter.Page"
          :page-size="filter.Per"
          size="small"
          show-total
          show-sizer
          show-elevator
          @on-change="changePage"
          class="primary_R"
        ></Page>
      </Col>
    </Row>
    <Modal
      v-model="importuserlistShow"
      width="1024"
      :mask-closable="false"
      :title="$t('system.import')"
    >
      <div slot="close">
        <Icon
          type="close"
          @click="handleCancelImport()"
          style="color: #666"
        ></Icon>
      </div>
      <div>
        <Row class="ivu-row-Padding-5 ivu-row-Bottom">
          <Col span="5">
            <DatePicker
              v-model="importLogFilter.ImportTime"
              placement="bottom-end"
              type="daterange"
              split-panels
              :placeholder="this.$t('expiringQualification.importTime')"
            ></DatePicker>
          </Col>
          <Col span="2">
            <Button icon="ios-search-strong" @click="handleSearch">{{
              $t("system.search")
            }}</Button>
          </Col>
          <Col span="3" offset="5">
            <Upload
              ref="uploadDataAuthority"
              :show-upload-list="false"
              :on-success="handleSuccess"
              :format="['xls', 'xlsx', 'csv']"
              :max-size="this.$constDefinition.uploadSize.uploadSizeM20"
              :on-format-error="handleFormatError"
              :on-error="handleUploadError"
              :on-exceeded-size="handleMaxSize"
              :before-upload="handleBeforeUpload"
              type="select"
              :action="uploadAction"
              :headers="uploadHeaders"
              class="primary_L"
            >
              <Button icon="ios-cloud-upload-outline">{{
                $t("system.selectFile")
              }}</Button>
            </Upload>
          </Col>
          <Col span="5" offset="4">
            <!-- 数据权限模板下载 -->
            <a :href="tempname" target="_blank" class="uploadListA">{{
              $t("system.dataPermissionsTemplateDownload")
            }}</a>
          </Col>
        </Row>
      </div>
      <Row>
        <Col span="24">
          <Table
            stripe
            :columns="showImportLogColumns"
            size="small"
            :data="importLogList"
            @on-sort-change="handleImportLogSortChange"
          ></Table>
        </Col>
      </Row>
      <Row class="ivu-row-Top">
        <Col span="24">
          <Page
            :total="importLogTotalCount"
            :current="importLogFilter.Page"
            :page-size="importLogFilter.Per"
            :page-size-opts="pageSizeOpts"
            size="small"
            @on-page-size-change="changeImportLogPageSize"
            show-total
            show-sizer
            show-elevator
            @on-change="changeImportLogPage"
            class="primary_R"
          ></Page>
        </Col>
      </Row>
      <div slot="footer">
        <Button
          icon="ios-close-outline"
          @click="handleCancelImport()"
          style="margin-left: 8px"
          >{{ $t("system.close") }}</Button
        >
      </div>
    </Modal>
    <Modal
      v-model="configurationRoleShow"
      :closable="false"
      :mask-closable="false"
      :title="$t('system.configuringRolesAndDataPermissions')"
      width="1000"
    >
      <div slot="close">
        <Icon
          type="close"
          @click="handleconfigurationRoleClose"
          style="color: #666"
        ></Icon>
      </div>
      <configurationRole
        :employeeID="employeeID"
        :isShow="configurationRoleShow"
        @configurationRoleClose="handleconfigurationRoleClose"
      ></configurationRole>
      <div slot="footer"></div>
    </Modal>
    <Modal
      v-model="showEmployeeAddModal"
      :closable="false"
      :mask-closable="false"
      width="800"
      :title="$t('userlist.viewUerInformation')"
    >
      <i-form :label-width="100">
        <Row>
          <Col span="11">
            <!-- 姓名 -->
            <Form-item :label="$t('userlist.muid')">
              <i-input readonly v-model="EmployeeAddModal.MUID"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <Form-item :label="$t('userlist.loginName')">
              <i-input readonly v-model="EmployeeAddModal.LoginName"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <!-- 姓名 -->
            <Form-item :label="$t('userlist.searchname')">
              <i-input readonly v-model="EmployeeAddModal.NameCn"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <!-- 性别 -->
            <Form-item :label="$t('userlist.gender')">
              <i-input readonly v-model="EmployeeAddModal.SexDesc"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <!-- 部门 -->
            <Form-item :label="$t('userlist.department')">
              <i-input readonly v-model="EmployeeAddModal.DepartmentName"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <!-- 职务 -->
            <Form-item :label="$t('userlist.position')">
              <i-input readonly v-model="EmployeeAddModal.Title"></i-input>
            </Form-item>
          </Col>
           <Col span="11">
            <Form-item :label="$t('userlist.telephone')">
              <i-input readonly v-model="EmployeeAddModal.Mobile"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <Form-item :label="$t('userlist.email')">
              <i-input readonly v-model="EmployeeAddModal.Email"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <Form-item :label="$t('userlist.entryDate')">
              <i-input readonly v-model="EmployeeAddModal.HireDate"></i-input>
            </Form-item>
          </Col>
          <Col span="11">
            <!-- 状态 -->
            <Form-item :label="$t('userlist.status')">
              <i-input readonly v-model="EmployeeAddModal.StatusDesc"></i-input>
            </Form-item>
          </Col>
        </Row>
      </i-form>
      <div slot="footer">
        <!-- 取消 -->
        <Button type="ghost" @click="cancelUserListModal('formItem')">{{
          $t("system.close")
        }}</Button>
      </div>
    </Modal>
    <editUser ref="editUserModal" @on-success="search"/>
    <!-- 导出 -->
    <Modal
      v-model="showExportModal"
      :mask-closable="false"
      :title="$t('system.export')"
      width="800"
    >
      <customexport
        :column="columnDictionary"
        @exportSubmitEvent="handleExport"
        @exportCancelEvent="handleExportCancel"
      ></customexport>
      <div slot="close">
        <Icon type="ios-close-empty" @click="handleExportCancel()"></Icon>
      </div>
      <div slot="footer"></div>
    </Modal>
    <div class="progressBarBox" v-show="visible">
      <div class="progressBarCon">
        <!-- 文件上传 -->
        <p>
          <b>{{ $t("system.uploadFileProgressBar") }}</b>
        </p>
        <div id="progressCss">
          <span></span>
        </div>
        <!-- 文件名称 文件大小 字节 -->
        <p :title="fileAllData.name">
          {{ $t("system.uploadFileName") }}{{ fileAllData.name }}
        </p>
        <p>
          {{ $t("system.uploadFileSize") }}{{ fileAllData.size
          }}{{ $t("system.uploadFileByte") }}
        </p>
      </div>
    </div>
  </div>
</template>
<script>
    import { mapActions } from 'vuex';
    // 配置角色
    import configurationRole from './components/configurationRole.vue';
    import moment from 'moment';
    import customexport from '../../components/customexport.vue';
    import editUser from './components/editUser.vue';
    export default {
        // 组件
        components: {
            configurationRole,
            customexport,
            editUser
        },
        data () {
            return {
                fileAllData: {},
                visible: false,
                importLogFilter: {
                    Page: 1,
                    Per: 10
                },
                columnDictionary: [], // 导出列名
                showExportModal: false,
                pageSizeOpts: this.$constDefinition.pageSizeOpts,
                employeeStatus: [],
                employeeType: [],
                deptList: [],
                isCDMS: [],
                importLogTotalCount: 0,
                importLogList: [],
                showImportLogColumns: [
                    {
                        width: 70,
                        align: 'center',
                        title: this.$t('system.no'),
                        render: (h, params) => {
                            return h(
                                'span',
                                params.index + (this.filter.Page - 1) * this.filter.Per + 1
                            );
                        }
                    },
                    {
                        title: this.$t('expiringQualification.importTime'),
                        width: 130,
                        key: 'ImportTime',
                        sortable: 'custom',
                        render: function (h, params) {
                            if (params.row.ImportTime !== null) {
                                return h(
                                    'div',
                                    moment(params.row.ImportTime).format('YYYY-MM-DD HH:mm:ss')
                                );
                            }
                        }
                    },
                    {
                        title: this.$t('expiringQualification.importFile'),
                        key: 'FileName',
                        sortable: 'custom',
                        minWidth: 180,
                        render: (h, params) => {
                            return h('div', [
                                h(
                                    'a',
                                    {
                                        on: {
                                            click: () => {
                                                this.publicJS.DownloadFile(params.row.FullSourceFilePath);
                                            }
                                        }
                                    },
                                    params.row.FileName
                                )
                            ]);
                        }
                    },
                    {
                        title: this.$t('expiringQualification.totalCount'),
                        key: 'TotalCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 80
                    },
                    {
                        title: this.$t('expiringQualification.rightCount'),
                        key: 'RightCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 80
                    },
                    {
                        title: this.$t('expiringQualification.differentCount'),
                        key: 'DifferentCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 80
                    },
                    {
                        title: this.$t('expiringQualification.ignoreCount'),
                        key: 'IgnoreCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 80
                    },
                    {
                        title: this.$t('expiringQualification.repeatCount'),
                        key: 'RepeatCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 80
                    },
                    {
                        title: this.$t('expiringQualification.importAbnormalData'),
                        minWidth: 180,
                        key: 'ErrorFileName',
                        sortable: 'custom',
                        render: (h, params) => {
                            return h(
                                'a',
                                {
                                    on: {
                                        click: () => {
                                            this.publicJS.DownloadFile(params.row.FullErrorFilePath);
                                        }
                                    }
                                },
                                params.row.ErrorFileName
                            );
                        }
                    }
                ],
                tempname: '',
                uploadList: [],
                uploadAction: '',
                uploadHeaders: {
                    'Accept-Language':
                        localStorage.getItem('currentLanguage') === null
                            ? 'zh-CN'
                            : localStorage.getItem('currentLanguage') === 'mergeZH'
                                ? 'zh-CN'
                                : 'en-US',
                    Authorization: 'SperogenixAuthentication ' + localStorage.token,
                    route: sessionStorage.route
                },
                importuserlistShow: false, // 批量导入数据权限
                configurationRoleShow: false, // 配置角色
                filter: {
                    Page: 1,
                    Per: 10
                },
                employeeID: 0,
                cityList: [],
                showEmployeeAddModal: false,
                EmployeeAddModal: {},
                columns: [
                    {
                        width: 70,
                        align: 'center',
                        fixed: 'left',
                        title: this.$t('system.no'),
                        type: 'index'
                    },
                    {
                        title: this.$t('userlist.muid'),
                        key: 'MUID',
                        align: 'center',
                        fixed: 'left',
                        sortable: 'custom',
                        width: 100
                    },
                    {
                        title: this.$t('userlist.loginName'),
                        key: 'LoginName',
                        align: 'center',
                        fixed: 'left',
                        sortable: 'custom',
                        width: 120
                    },
                    {
                        title: this.$t('userlist.searchname'), // 姓名
                        key: 'NameCn',
                        sortable: 'custom',
                        minWidth: 100
                    },
                    {
                        title: this.$t('userlist.gender'), // 性别
                        key: 'SexDesc',
                        sortable: 'custom',
                        width: 60
                    },
                    {
                        title: this.$t('userlist.roleDesc'), // 所属角色
                        key: 'RoleNames',
                        sortable: false,
                        width: 90
                    },
                    {
                        title: this.$t('userlist.department'), // 部门
                        key: 'DepartmentName',
                        sortable: 'custom',
                        minWidth: 106
                    },
                    {
                        title: this.$t('userlist.position'), // 职务
                        key: 'Title',
                        sortable: 'custom',
                        minWidth: 140
                    },
                    {
                        title: this.$t('userlist.status'), // 状态
                        key: 'StatusDesc',
                        sortable: 'custom',
                        align: 'center',
                        width: 60
                    },
                    {
                        title: this.$t('userlist.email'), // 邮箱
                        key: 'Email',
                        sortable: 'custom',
                        minWidth: 220
                    },
                    {
                        title: this.$t('userlist.telephone'), // 电话
                        key: 'Mobile',
                        sortable: 'custom',
                        width: 120
                    },
                    {
                        title: this.$t('userlist.entryDate'), // 入职日期
                        key: 'HireDate',
                        sortable: 'custom',
                        align: 'center',
                        width: 85,
                        render: function (h, params) {
                            if (params.row.HireDate) {
                                return h(
                                    'span',
                                    moment(params.row.HireDate).format('YYYY-MM-DD')
                                );
                            }
                        }
                    },
                    {
                        title: this.$t('system.action'), // 操作
                        key: '',
                        fixed: 'right',
                        width: 120,
                        render: (h, params) => {
                            return h('div', [
                                h(
                                    'Tooltip',
                                    {
                                        props: {
                                            transfer: true,
                                            placement: 'top',
                                            content: this.$t('productGroup.configurationRole')
                                        }
                                    },
                                    [
                                        h('Icon', {
                                            props: {
                                                size: '20',
                                                type: 'person-add'
                                            },
                                            style: {
                                                marginRight: '5px',
                                                cursor: 'pointer',
                                                display: this.behaviors.serverSrc.userlist
                                                    .userlist_ToConfigure
                                                    ? params.row.EnumEntityStatus === this.$constDefinition.employeeStatusValue.INSERVICEOne
                                                        ? 'block'
                                                        : 'none'
                                                    : 'none'
                                            },
                                            on: {
                                                click: () => {
                                                    // eslint-disable-next-line no-unused-expressions
                                                    this.behaviors.serverSrc.userlist.userlist_ToConfigure
                                                        ? this.handleconfigurationRoleOpen(params.row)
                                                        : '';
                                                }
                                            }
                                        })
                                    ]
                                ), h(
                                    'Tooltip',
                                    {
                                        props: {
                                            transfer: true,
                                            placement: 'top',
                                            content: this.$t('system.edit')
                                        }
                                    },
                                    [
                                        h('Icon', {
                                            props: {
                                                size: '20',
                                                type: 'ios-compose-outline'
                                            },
                                            style: {
                                                marginRight: '5px',
                                                cursor: 'pointer',
                                                display: this.behaviors.serverSrc.userlist
                                                    .userlist_Edit
                                                    ? params.row.EnumEntityStatus === this.$constDefinition.employeeStatusValue.INSERVICEOne
                                                        ? 'block'
                                                        : 'none'
                                                    : 'none'
                                            },
                                            on: {
                                                click: () => {
                                                    // eslint-disable-next-line no-unused-expressions
                                                    this.behaviors.serverSrc.userlist.userlist_Edit
                                                        ? this.handleEditUser(params.row)
                                                        : '';
                                                }
                                            }
                                        })
                                    ]
                                ),
                                h(
                                    'Tooltip',
                                    {
                                        props: {
                                            transfer: true,
                                            placement: 'top',
                                            content: params.row.EnumEntityStatus ===
                                                this.$constDefinition.employeeStatusValue.INSERVICEOne
                                                ? this.$t('userlist.depart') : this.$t('userlist.entry')
                                        }
                                    },
                                    [
                                        h('Icon', {
                                            props: {
                                                size: '20',
                                                type: params.row.EnumEntityStatus ===
                                                    this.$constDefinition.employeeStatusValue.INSERVICEOne
                                                    ? 'ios-close-outline'
                                                    : 'ios-checkmark-outline'
                                            },
                                            style: {
                                                marginRight: '5px',
                                                cursor: 'pointer',
                                                display: this.behaviors.serverSrc.userlist.userlist_EntryDepart
                                                    ? 'block'
                                                    : 'none'
                                            },
                                            on: {
                                                click: () => {
                                                    // eslint-disable-next-line no-unused-expressions
                                                    this.behaviors.serverSrc.userlist.userlist_EntryDepart
                                                        ? this.handleSetEmployeeStatus(params.row)
                                                        : '';
                                                }
                                            }
                                        })
                                    ]
                                ),
                                h(
                                    'Tooltip',
                                    {
                                        props: {
                                            transfer: true,
                                            placement: 'top',
                                            content: this.$t('system.resetPassword')
                                        }
                                    },
                                    [
                                        h('Icon', {
                                            props: {
                                                size: '20',
                                                type: 'key'
                                            },
                                            style: {
                                                marginRight: '5px',
                                                cursor: 'pointer',
                                                display: this.behaviors.serverSrc.userlist
                                                    .userlist_ResetPassword
                                                    ? params.row.EnumEntityStatus === this.$constDefinition.employeeStatusValue.INSERVICEOne
                                                        ? 'block'
                                                        : 'none'
                                                    : 'none'
                                            },
                                            on: {
                                                click: () => {
                                                    // eslint-disable-next-line no-unused-expressions
                                                    this.behaviors.serverSrc.userlist.userlist_ResetPassword
                                                        ? this.handleRestPassword(params.row)
                                                        : '';
                                                }
                                            }
                                        })
                                    ]
                                ),
                                h(
                                    'Tooltip',
                                    {
                                        props: {
                                            transfer: true,
                                            placement: 'top',
                                            content: this.$t('system.view')
                                        }
                                    },
                                    [
                                        h('Icon', {
                                            props: {
                                                size: '20',
                                                type: 'ios-paper-outline'
                                            },
                                            style: {
                                                cursor: 'pointer',
                                                display: this.behaviors.serverSrc.userlist.userlist_Detail
                                                    ? 'block'
                                                    : 'none'
                                            },
                                            on: {
                                                click: () => {
                                                    // eslint-disable-next-line no-unused-expressions
                                                    this.behaviors.serverSrc.userlist.userlist_Detail
                                                        ? this.TableView(params.index)
                                                        : '';
                                                }
                                            }
                                        })
                                    ]
                                )
                            ]);
                        }
                    }
                ]
            };
        },
        created () {
            this.behaviors.behaviorsSession(localStorage.behaviors, 'userlist');
            this.tempname = '/UploadFile/Template/CDMS_UserDataRights.xlsx';
            this.uploadAction =
                this.$http.defaults.baseURL + '/Permission/ImportEmployeeDataAuthority';
            this.$http.get('/Employee/QueryAllDepartmentSelect').then((response) => {
                this.deptList = response.data;
            });
            this.employeeStatus = [
                {
                    value: this.$constDefinition.employeeStatusValue.INSERVICEOne, // 1
                    label: this.$constDefinition.employeeStatus.INSERVICE // "在职"
                },
                {
                    value: this.$constDefinition.employeeStatusValue.LEAVETWO, // 2
                    label: this.$constDefinition.employeeStatus.LEAVE // "离职"
                }
            ];

            this.$http.get('/Employee/GetEmployeeType').then((response) => {
                this.employeeType = response.data;
            });
            this.search();
        },
        methods: {
            ...mapActions(['queryUsers']),
            search () {
                this.filter.Page = 1;
                this.queryUsers(this.filter);
            },
            getFilterCreateTime (time) {
                this.filter.CreateTime = time;
            },
            changePage (value) {
                this.filter.Page = value;
                this.queryUsers(this.filter);
            },
            changePageSize (value) {
                this.filter.Per = value;
                this.filter.Page = 1;
                this.queryUsers(this.filter);
            },
            show (index) {
                this.$Modal.info({});
            },
            handleExportCancel () {
                this.showExportModal = false;
            },
            // 显示导出model
            handleShowExportModal () {
                this.$http
                    .get('/Employee/QueryUserlistDictionary', null)
                    .then((response) => {
                        this.columnDictionary = response.data;
                        this.showExportModal = true;
                });
            },
            handleAdd () {
                this.$refs.editUserModal.initPage(null);
            },
            handleEditUser (row) {
                this.$refs.editUserModal.initPage(row);
            },
            handleRestPassword (row) {
                this.$confirm(
                    this.$t('userlist.resetPassword'),
                    this.$t('system.alter'),
                    {
                        cancelButtonClass: 'btn-custom-cancel',
                        confirmButtonText: this.$t('system.confirm'),
                        cancelButtonText: this.$t('system.cancel'),
                        type: 'warning'
                    }
                )
                    .then(() => {
                        this.$http.post('/Employee/SetDefaultPassword', row).then((response) => {
                            if (response.data.Message) {
                                this.$alert(response.data.Message, this.$t('system.alter'), {
                                    dangerouslyUseHTMLString: true
                                });
                            } else {
                                this.$Message.success(this.$t('system.submitSuccess'));
                                this.handlecancel()
                                this.$emit('on-success');
                            }
                        });
                    })
            },
            handleSetEmployeeStatus (row) {
                if (row.EnumEntityStatus === this.$constDefinition.employeeStatusValue.INSERVICEOne) {
                    this.$confirm(
                        this.$t('userlist.setEmployeeDepart'),
                        this.$t('system.alter'),
                        {
                            cancelButtonClass: 'btn-custom-cancel',
                            confirmButtonText: this.$t('system.confirm'),
                            cancelButtonText: this.$t('system.cancel'),
                            type: 'warning'
                        }
                    )
                        .then(() => {
                            this.$http.post('/Employee/SetEmployeeResignation', row).then((response) => {
                                if (response.data.Message) {
                                    this.$alert(response.data.Message, this.$t('system.alter'), {
                                        dangerouslyUseHTMLString: true
                                    });
                                } else {
                                    this.$Message.success(this.$t('system.submitSuccess'));
                                    this.queryUsers(this.filter)
                                }
                            });
                        })
                } else {
                    this.$confirm(
                        this.$t('userlist.setEmployeeNormal'),
                        this.$t('system.alter'),
                        {
                            cancelButtonClass: 'btn-custom-cancel',
                            confirmButtonText: this.$t('system.confirm'),
                            cancelButtonText: this.$t('system.cancel'),
                            type: 'warning'
                        }
                    )
                        .then(() => {
                            this.$http.post('/Employee/SetEmployeeNormal', row).then((response) => {
                                if (response.data.Message) {
                                    this.$alert(response.data.Message, this.$t('system.alter'), {
                                        dangerouslyUseHTMLString: true
                                    });
                                } else {
                                    this.$Message.success(this.$t('system.submitSuccess'));
                                    this.queryUsers(this.filter)
                                }
                            });
                        })
                }
            },
            // 确认导出
            handleExport (checkColumns) {
                this.showExportModal = false;
                this.filter.CheckedColumns = checkColumns;
                this.$http
                    .post('/Employee/ExportUserList', this.filter, {
                        responseType: 'arraybuffer'
                    })
                    .then((response) => {
                        let fileDownload = require('js-file-download');
                        let fileName = 'User.xlsx';
                        fileDownload(response.data, fileName);
                });
            },
            handleExportEmployeeRole () {
                this.$http
                    .post('/Employee/ExportEmployeePermission', this.filter, {
                        responseType: 'arraybuffer'
                    })
                    .then((response) => {
                        let fileDownload = require('js-file-download');
                        let fileName = 'UserPermission.xlsx';
                        fileDownload(response.data, fileName);
                });
            },
            handleSortChange (column) {
                if (column.key === 'SexDesc') {
                    this.filter.OrderBy = [`Sex ${column.order}`];
                } else if (column.key === 'IsCDMSDesc') {
                    this.filter.OrderBy = [`IsCDMS ${column.order}`];
                } else if (column.key === 'DepartmentName') {
                    this.filter.OrderBy = [`Department.Name ${column.order}`];
                } else if (column.key === 'StatusDesc') {
                    this.filter.OrderBy = [`EnumEntityStatus ${column.order}`];
                } else {
                    this.filter.OrderBy = [`${column.key} ${column.order}`];
                }
                this.queryUsers(this.filter);
            },
            // 批量导入数据权限---按钮
            onimportuserlistOpen () {
                this.importLogFilter.Page = 1;
                this.importLogFilter.Per = 10;
                this.importuserlistShow = true;
                this.handleSearch();
            },
            // 批量导入数据权限---关闭
            handleimportuserlistClose () {
                this.importuserlistShow = false;
            },
            // 配置角色---按钮
            handleconfigurationRoleOpen (row) {
                this.employeeID = row.ID;
                this.configurationRoleShow = true;
            },
            // 配置角色---关闭
            handleconfigurationRoleClose () {
                this.configurationRoleShow = false;
                this.queryUsers(this.filter);
            },
            // 查看
            TableView (index) {
                this.showEmployeeAddModal = true;
                let TableID = this.$store.state.user.userlist[index];
                // eslint-disable-next-line no-unused-expressions, no-sequences
                (this.EmployeeAddModal.CreateTime = TableID.CreateTime),
                (this.EmployeeAddModal.Creator = TableID.Creator),
                (this.EmployeeAddModal.DepartmentName = TableID.DepartmentName),
                (this.EmployeeAddModal.Email = TableID.Email),
                (this.EmployeeAddModal.EnumEmployeeType = TableID.EnumEmployeeType),
                (this.EmployeeAddModal.EnumEntityStatus = TableID.EnumEntityStatus),
                (this.EmployeeAddModal.FinalEditTime = TableID.FinalEditTime),
                (this.EmployeeAddModal.FinalEditor = TableID.FinalEditor),
                (this.EmployeeAddModal.ID = TableID.ID),
                (this.EmployeeAddModal.IsCDMSDesc = TableID.IsCDMSDesc),
                (this.EmployeeAddModal.JoinTime = TableID.JoinTime),
                (this.EmployeeAddModal.LastWorkDay = TableID.LastWorkDay),
                (this.EmployeeAddModal.LeaveTime = TableID.LeaveTime),
                (this.EmployeeAddModal.LoginName = TableID.LoginName),
                (this.EmployeeAddModal.MUID = TableID.MUID),
                (this.EmployeeAddModal.Mobile = TableID.Mobile),
                (this.EmployeeAddModal.NameCn = TableID.NameCn),
                (this.EmployeeAddModal.NameEn = TableID.NameEn),
                (this.EmployeeAddModal.Order = TableID.Order),
                (this.EmployeeAddModal.Password = TableID.Password),
                (this.EmployeeAddModal.Remark = TableID.Remark),
                (this.EmployeeAddModal.HireDate = moment(TableID.HireDate).format('YYYY-MM-DD')),
                (this.EmployeeAddModal.Sex = TableID.Sex),
                (this.EmployeeAddModal.SexDesc = TableID.SexDesc),
                (this.EmployeeAddModal.StatusDesc = TableID.StatusDesc),
                (this.EmployeeAddModal.Telephone = TableID.Telephone),
                (this.EmployeeAddModal.Title = TableID.Title);
            },
            // 取消--弹出框
            cancelUserListModal () {
                this.showEmployeeAddModal = false;
            },
            // 导入按钮--显示弹出框
            handleShowImportModal () {
                this.handleSearch();
                this.importuserlistShow = true; // 导入弹出框显示
            },
            // 上传记录查询
            handleSearch () {
                this.importLogFilter.Page = 1;
                this.queryImportEmployeeDataAuthority(this.importLogFilter);
            },
            queryImportEmployeeDataAuthority (filter) {
                this.$http
                    .get('/Permission/QueryImportDataAuthorityLog', {
                        params: filter
                    })
                    .then((response) => {
                        this.importLogList = response.data.Models;
                        this.importLogTotalCount = response.data.TotalCount;
                });
            },
            // 上传记录翻页
            changeImportLogPage (value) {
                this.importLogFilter.Page = value;
                this.queryImportEmployeeDataAuthority(this.importLogFilter);
            }, // 上传记录调整单页显示数
            changeImportLogPageSize (value) {
                this.importLogFilter.Per = value;
                this.importLogFilter.Page = 1;
                this.queryImportEmployeeDataAuthority(this.importLogFilter);
            }, // 上传记录排序
            handleImportLogSortChange (column) {
                this.importLogFilter.OrderBy = [`${column.key} ${column.order}`];
                this.queryImportEmployeeDataAuthority(this.importLogFilter);
            }, // 附件上传成功
            handleSuccess (res, file, fileList) {
                this.visible = false;
                this.$Message.success(this.$t('system.uploadSuccess'));
                this.$refs.uploadDataAuthority.clearFiles();
                this.uploadList = this.$refs.uploadDataAuthority.fileList;
                this.importLogMasterTemp = [];
                this.handleSearch();
            }, // 附件格式错误
            // eslint-disable-next-line handle-callback-err
            handleFormatError (error, file, fileList) {
                this.visible = false;
                this.$Message.warning(this.$t('batchImport.uploadFormatError'));
            }, // 附件大小验证
            handleMaxSize (file, fileList) {
                this.visible = false;
                this.$Message.warning(this.$t('batchImport.uploadMaxSizeError'));
            }, // 附件上传返回错误不走统一拦截处理
            // eslint-disable-next-line handle-callback-err
            handleUploadError (error, file, fileList) {
                this.visible = false;
                this.publicJS.handleUploadErrorPublic(
                    file,
                    this.$t('batchImport.uploadError')
                );
            }, // 附件上传前
            handleBeforeUpload (file) {
                this.fileAllData.name = file.name;
                this.fileAllData.size = file.size;
                this.visible = true;
            }, // 移除附件
            handleRemove (file) {
                this.$refs.uploadDataAuthority.clearFiles();
                this.uploadList = this.$refs.uploadDataAuthority.fileList;
                this.importLogMasterTemp = [];
            }, /// 取消上传
            handleCancelImport () {
                this.$refs.uploadDataAuthority.clearFiles();
                this.uploadList = this.$refs.uploadDataAuthority.fileList;
                this.importLogMasterTemp = [];
                this.importuserlistShow = false;
                this.search()
            }
        }
    };
</script>
<style scoped>
.ivu-date-picker {
  width: 100%;
}
.uploadListA {
  font-size: 14px;
}
</style>
