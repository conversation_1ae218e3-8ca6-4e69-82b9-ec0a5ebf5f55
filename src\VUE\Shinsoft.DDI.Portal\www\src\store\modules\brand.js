import http from '../../utils/axios'
const brand = {
    state: {
        brandList: [],
        totalCount: 1,
        brandModel: {},
        brandSelectList: []
    },
    mutations: {
        InitStateBrands (state, data) {
            state.brandList = data.Models
            state.totalCount = data.TotalCount
        },
        InitBrandModel (state, data) {
            state.brandModel = data
        },
        QueryBrandSelectList (state, data) {
            state.brandSelectList = data
        }
    },
    actions: {
        queryBrandsAction ({
            commit
        }, params) {
            http.get('/Product/QueryBrand', {
                params: params
            }).then(function (response) {
                commit('InitStateBrands', response.data)
            })
        },
        getBrandAction ({
            commit
        }, brandID) {
            http.get('/Product/GetBrand', {
                params: { 'brandID': brandID }
            }).then(function (response) {
                commit('InitBrandModel', response.data)
            })
        },
        queryBrandSelectListBy ({commit}, params) {
            http.get('/Product/QueryBrandSelectList', {
                params: params
            }).then(function (response) {
                commit('QueryBrandSelectList', response.data)
            })
        }
    }
}
export default brand
