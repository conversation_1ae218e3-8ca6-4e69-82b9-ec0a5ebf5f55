<template>
<el-dialog v-model="showChoiceDirection" :title="$t('home.announcementdetails')" width="800px" :close-on-click-modal="false">
  <el-row>
    <el-col :span="24">
    <article style="margin:0 10px;">
      <hgroup>
        <h1 class="headingStyle">{{filter.Title}}</h1>
        <h2 class="headingStyle">{{filter.SubTitle}}</h2>
      </hgroup>
      <!-- 发布时间 -->
      <p class="primary_C"><span class="primary_R">{{$t('home.releaseTime')}}:{{formatDate(filter.CreateTime)}}</span></p>
      <div class="announcementContentStyle">
        <div v-html="filter.Content" class="ql-editor"></div>
      </div>
    </article>
    </el-col>
  </el-row>
  <!--附件区域-->
        <el-row
          class="ivu-row-Top"
        >
          <el-col :span="24">
            <span :label="$t('system.attachment')">

            </span>
          </el-col>
          <el-col :span="24">
              <!-- Table组件暂时注释，需要替换为Element Plus对应组件
              <Table size="small" :columns="fileColumns" :data="filter.Attachment"></Table>
              -->
              <div>附件列表功能暂时不可用</div>
          </el-col>
        </el-row>
  <template #footer>
    <el-row type="flex" justify="end" :gutter="10">
      <el-col :span="6">
        <el-button @click="handleCancel">{{$t('system.close')}}</el-button>
      </el-col>
    </el-row>
  </template>
</el-dialog>
</template>
<script>
    import moment from 'moment';
    export default {
        data () {
            return {
                showChoiceDirection: false,
                filter: {},
                fileColumns: [
                    {
                        title: this.$t('system.no'),
                        width: 70,
                        align: 'center',
                        render: (h, params) => {
                            return h(
                                'span',
                                params.index + 1
                            );
                        }
                    },
                    {
                        title: this.$t('system.annexName'), // 附件名称
                        key: 'Name',
                        render: (h, params) => {
                            return h('div', [
                                h(
                                    'a',
                                    {
                                        on: {
                                            click: () => {
                                                this.publicJS.DownloadFileByID(params.row.ID, params.row.Name);
                                            }
                                        }
                                    },
                                    params.row.Name
                                )
                            ]);
                        }
                    },
                    {
                        title: this.$t('system.uploadTime'), // 上传时间
                        key: 'CreateTime',
                        render: function (h, params) {
                            if (params.row.CreateTime !== null) {
                                return h(
                                    'div',
                                    moment(params.row.CreateTime).format('YYYY-MM-DD HH:mm:ss')
                                );
                            }
                        }
                    }

                ]
            }
        },
        methods: {
            init (item) {
                let params = {
                    announcementID: item.ID
                }
                this.$http
                    .get('/Announcement/GetAnnouncement', {
                        params: params
                    })
                    .then(response => {
                        this.showChoiceDirection = true
                        this.filter = response.data
                });
            },
            handleCancel () {
                this.filter = {}
                this.showChoiceDirection = false
            },
            // 格式化日期方法，替代Vue 3中移除的过滤器
            formatDate(dateString) {
                if (!dateString) return '';
                return moment(dateString).format('YYYY-MM-DD');
            }
        }
    };
</script>
<style>
.announcementContentStyle{
  width: 100%!important;
}
.announcementContentStyle p{
  width: 100%!important;
}
.announcementContentStyle img{
  display: block;
  width: 100%!important;
}
</style>

<style scoped>
.primary_C{
  clear: both;
  height: 30px;
}
.headingStyle{
  text-align: center;
}
</style>
