import axios from '@/utils/axios'

/**
 * 配置相关API
 */
export const configApi = {
  /**
   * 查询经销商客户端配置
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.code 编码
   * @param {string} params.name 名称
   * @param {string} params.targetType 采集方式
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryReceiverClient(params) {
    return axios.get('/Config/QueryReceiverClient', { params })
  },

  /**
   * 获取经销商客户端配置详情
   * @param {string} id 配置ID
   * @returns {Promise} API响应
   */
  getReceiverClient(id) {
    return axios.get(`/Config/GetReceiverClient`, { params: { id } })
  },

  /**
   * 新增经销商客户端配置
   * @param {Object} data 配置数据
   * @returns {Promise} API响应
   */
  addReceiverClient(data) {
    return axios.post('/Config/AddReceiverClient', data)
  },

  /**
   * 更新经销商客户端配置
   * @param {Object} data 配置数据
   * @returns {Promise} API响应
   */
  updateReceiverClient(data) {
    return axios.post('/Config/EditReceiverClient', data)
  },

  /**
   * 删除经销商客户端配置
   * @param {Object} data 包含ID的数据对象
   * @returns {Promise} API响应
   */
  deleteReceiverClient(data) {
    return axios.post('/Config/DeleteReceiverClient', data)
  },

  /**
   * 删除经销商客户端配置
   * @param {string} id 配置ID
   * @returns {Promise} API响应
   */
  deleteReceiverClient(id) {
    return axios.post('/Config/DeleteReceiverClient', { id })
  }
}

export default configApi
