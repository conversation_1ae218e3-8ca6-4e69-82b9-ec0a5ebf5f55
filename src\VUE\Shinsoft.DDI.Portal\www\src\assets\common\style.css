@font-face {
  font-family: 'ionicons';
  src:
    url('fonts/ionicons.ttf?h46gp2') format('truetype'),
    url('fonts/ionicons.woff?h46gp2') format('woff'),
    url('fonts/ionicons.svg?h46gp2#ionicons') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="ivu-icon-"], [class*=" ivu-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'ionicons' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  /* line-height: 1; */

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ivu-icon-dn:before {
  content: "\e904";
}
.ivu-icon-batchReturn:before {
  content: "\e900";
}
.ivu-icon-contract:before {
  content: "\e901";
}
.ivu-icon-invoice:before {
  content: "\e902";
}
.ivu-icon-stamp:before {
  content: "\e903";
}
.ivu-icon-profile:before {
  content: "\e923";
}
.ivu-icon-history:before {
  content: "\e94d";
}
.ivu-icon-info:before {
  content: "\ea0c";
}
.ivu-icon-loop:before {
  content: "\ea2d";
}
.ivu-icon-loop2:before {
  content: "\ea2e";
}
.ivu-icon-Invitation:before {
  content: "\905";
}
.ivu-icon-remove_user:before {
  content: "\e906";
}
.ivu-icon-weixin:before {
  content: "\e907";
}

