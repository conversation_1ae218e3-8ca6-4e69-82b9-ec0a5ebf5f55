<!--字典编辑弹窗 - Element Plus版本-->
<template>
  <el-dialog
    v-model="showDialog"
    :title="dialogTitle"
    width="700px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item :label="$t('system.dictionaryName')" prop="Name">
        <el-input
          v-model="formData.Name"
          :placeholder="$t('system.dictionaryName')"
          clearable
        />
      </el-form-item>
      
      <el-form-item :label="$t('system.dictionaryCode')" prop="Code">
        <el-input
          v-model="formData.Code"
          :placeholder="$t('system.dictionaryCode')"
          :disabled="isEdit"
          clearable
        />
      </el-form-item>
      
      <el-form-item :label="$t('system.parentDictionary')" prop="ParentId" v-if="showParentSelect">
        <el-select
          v-model="formData.ParentId"
          :placeholder="$t('system.parentDictionary')"
          filterable
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="item in parentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <!-- 字典项列表（仅在新增父级字典时显示） -->
    <div v-if="showChildrenList" style="margin-top: 20px;">
      <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 10px;">
        <el-button
          icon="CirclePlus"
          @click="showChildFormDialog"
          :disabled="!formData.Code"
        >
          新增字典项
        </el-button>
      </div>

      <el-row :gutter="20">
        <el-col :span="4" style="text-align: right; line-height: 32px;">
          <label style="font-weight: 500; color: #606266;">字典项列表</label>
        </el-col>
        <el-col :span="20">
          <el-table :data="childrenList" border size="small" style="width: 100%">
            <el-table-column prop="Name" label="字典项名称" width="200"></el-table-column>
            <el-table-column label="字典项编码" width="100">
              <template #default="{ row }">
                {{ getDisplayCode(row.Code) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="{ row, $index }">
                <el-tooltip content="上移" placement="top">
                  <el-button
                    icon="ArrowUp"
                    circle
                    size="small"
                    @click="moveChildUp($index)"
                    v-if="childrenList.length > 1 && $index > 0"
                  />
                </el-tooltip>
                <el-tooltip content="编辑" placement="top">
                  <el-button
                    icon="Edit"
                    circle
                    size="small"
                    @click="editChildItem($index)"
                  />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button
                    icon="Delete"
                    circle
                    size="small"
                    @click="deleteChildItem($index)"
                  />
                </el-tooltip>
                <el-tooltip content="下移" placement="top">
                  <el-button
                    icon="ArrowDown"
                    circle
                    size="small"
                    @click="moveChildDown($index)"
                    v-if="childrenList.length > 1 && $index < childrenList.length - 1"
                  />
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ $t('system.cancel') }}</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ $t('system.save') }}
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 子项表单弹窗 -->
  <el-dialog
    v-model="showChildForm"
    :title="editingChildIndex === -1 ? '新增字典项' : '编辑字典项'"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="childFormRef"
      :model="childFormData"
      :rules="childFormRules"
      label-width="100px"
      size="small"
    >
      <el-form-item label="字典项名称" prop="Name">
        <el-input v-model="childFormData.Name" placeholder="请输入字典项名称" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item label="字典项编码" prop="Code">
        <el-input
          v-model="childFormData.Code"
          placeholder="请输入字典项编码"
          maxlength="32"
          :disabled="editingChildIndex !== -1"
        >
          <template #prepend v-if="formData.Code">
            <span style="color: #606266;">{{ formData.Code }}_</span>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="resetChildForm">取消</el-button>
        <el-button type="primary" @click="saveChildItem">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'DictionaryDialog',
  data() {
    return {
      showDialog: false,
      loading: false,
      dialogTitle: '',
      isEdit: false,
      showParentSelect: false,
      formData: {
        ID: '',
        Name: '',
        Code: '',
        ParentId: ''
      },
      parentOptions: [],
      // 字典项管理相关数据
      showChildrenList: false, // 是否显示字典项列表
      childrenList: [], // 字典项列表（临时保存在客户端）
      childFormData: { // 字典项表单数据
        Name: '',
        Code: ''
      },
      showChildForm: false, // 是否显示字典项表单
      editingChildIndex: -1, // 正在编辑的字典项索引（-1表示新增）
      rules: {
        Name: [
          {
            required: true,
            message: this.$t('system.dictionaryNameIsNotNull'),
            trigger: 'blur'
          },
          {
            max: 32,
            message: this.$t('system.dictionaryNameOverLength'),
            trigger: 'blur'
          }
        ],
        Code: [
          {
            required: true,
            message: this.$t('system.dictionaryCodeIsNotNull'),
            trigger: 'blur'
          },
          {
            max: 32,
            message: this.$t('system.dictionaryCodeOverLength'),
            trigger: 'blur'
          }
        ]
      },
      // 子项表单验证规则
      childFormRules: {
        Name: [
          {
            required: true,
            message: this.$t('system.dictionaryNameIsNotNull'),
            trigger: 'blur'
          },
          {
            max: 32,
            message: this.$t('system.dictionaryNameOverLength'),
            trigger: 'blur'
          }
        ],
        Code: [
          {
            required: true,
            message: this.$t('system.dictionaryCodeIsNotNull'),
            trigger: 'blur'
          },
          {
            max: 32,
            message: this.$t('system.dictionaryCodeOverLength'),
            trigger: 'blur'
          }
        ]
      }
    };
  },
  watch: {
    // 监听字典编码变化，自动更新字典项编码前缀
    'formData.Code': {
      handler(newCode, oldCode) {
        if (this.childrenList && this.childrenList.length > 0 && oldCode && newCode !== oldCode) {
          // 更新所有字典项的编码前缀
          this.childrenList.forEach(item => {
            if (item.Code && item.Code.startsWith(oldCode + '_')) {
              // 移除旧前缀，保留后缀部分
              const suffix = item.Code.substring((oldCode + '_').length);
              // 添加新前缀
              item.Code = newCode + '_' + suffix;
            }
          });
        }
      },
      deep: false
    }
  },
  methods: {
    // 显示新增字典弹窗
    showAddDictionary(parentRow) {
      this.isEdit = false;

      if (parentRow) {
        // 新增子字典
        this.showParentSelect = true;
        this.showChildrenList = false;
        this.dialogTitle = '新增子字典';
        this.formData = {
          ID: '',
          Name: '',
          Code: '',
          ParentId: parentRow.ID
        };
        this.loadParentOptions();
      } else {
        // 新增父级字典
        this.showParentSelect = false;
        this.showChildrenList = true;
        this.dialogTitle = '新增字典';
        this.formData = {
          ID: '',
          Name: '',
          Code: '',
          ParentId: ''
        };
        // 初始化字典项列表
        this.childrenList = [];
        this.resetChildForm();
      }

      this.showDialog = true;
      this.$nextTick(() => {
        this.$refs.formRef?.clearValidate();
      });
    },
    
    // 显示编辑字典弹窗
    showEditDictionary(row) {
      this.isEdit = true;
      this.showParentSelect = false; // 编辑时不显示父字典选择
      this.showChildrenList = !row.parentId; // 只有父级字典才显示子项列表
      this.dialogTitle = row.parentId ? '编辑字典项' : '编辑字典';

      // 直接使用传入的row数据，避免API调用问题
      this.formData = {
        ID: row.id || row.ID,
        Name: row.name || row.Name || '',
        Code: row.code || row.Code || '',
        ParentId: row.parentId || row.ParentId || ''
      };

      // 如果是父级字典，加载子项数据
      if (!row.parentId) {
        this.loading = true;
        this.$http
          .get('/BizMasterData/QueryBizDict', {
            params: {
              ParentId: row.id || row.ID,
              page: 1,
              per: 10
            }
          })
          .then(response => {
            console.log('获取子项数据响应:', response.data);

            // axios拦截器已经处理了嵌套的data结构
            const success = response.data?.success;
            const datas = response.data?.datas;

            if (success && datas) {
              // 转换子项数据格式，按Ordinal排序
              this.childrenList = datas
                .sort((a, b) => (a.ordinal || a.Ordinal || 0) - (b.ordinal || b.Ordinal || 0))
                .map(child => ({
                  ID: child.id || child.ID,
                  Name: child.name || child.Name || '',
                  Code: child.code || child.Code || '',
                  ParentId: child.parentId || child.ParentId || '',
                  Ordinal: child.ordinal || child.Ordinal || 0
                }));
            } else {
              this.childrenList = [];
            }
          })
          .catch(error => {
            console.error('获取子项数据出错:', error);
            this.childrenList = [];
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        // 如果是子字典，加载父字典选项
        this.loadParentOptions();
      }

      this.showDialog = true;
      this.$nextTick(() => {
        this.$refs.formRef?.clearValidate();
      });
    },
    
    // 加载父字典选项
    loadParentOptions() {
      this.$http
        .get('/BizMasterData/QueryBizDict', { params: { Page: 1, Per: 1000 } })
        .then(response => {
          console.log('加载父字典选项响应:', response.data);

          // axios拦截器已经处理了嵌套的data结构
          const success = response.data?.success;
          const datas = response.data?.datas;

          if (success && datas) {
            this.parentOptions = datas.map(item => ({
              value: item.id || item.ID,
              label: item.name || item.Name
            }));
          }
        })
        .catch(error => {
          console.error('加载父字典选项出错:', error);
        });
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 判断是否为父级字典创建模式
          if (this.showChildrenList && !this.isEdit) {
            // 父级字典批量创建
            const submitData = {
              ParentDict: {
                Name: this.formData.Name,
                Code: this.formData.Code
              },
              Children: this.childrenList.map(child => ({
                Name: child.Name,
                Code: child.Code
              }))
            };

            this.$http
              .post('/BizMasterData/AddDictWithChildren', submitData)
              .then(response => {
                const success = response.data?.success;
                const messages = response.data?.messages;

                if (success) {
                  this.$message.success('新增字典成功');
                  this.handleCancel();
                  this.$emit('refresh');
                } else {
                  // 显示具体的错误信息
                  let errorMessage = '保存失败';
                  if (messages && messages.length > 0) {
                    errorMessage = messages.join(', ');
                  } else if (response.data?.message) {
                    errorMessage = response.data.message;
                  }
                  this.$message.error(errorMessage);
                }
              })
              .catch(error => {               
                let errorMessage = '保存失败';
                if (error.response?.data?.messages?.length > 0) {
                  errorMessage = error.response.data.messages.join(', ');
                } else if (error.response?.data?.message) {
                  errorMessage = error.response.data.message;
                } else if (error.message) {
                  errorMessage = error.message;
                }
                this.$message.error(errorMessage);
              })
              .finally(() => {
                this.loading = false;
              });
          } else if (this.isEdit && this.showChildrenList) {
            // 编辑父级字典及其子项
            const submitData = {
              ParentDict: {
                ID: this.formData.ID,
                Name: this.formData.Name,
                Code: this.formData.Code
              },
              Children: this.childrenList.map((child, index) => ({
                ID: child.ID,
                Name: child.Name,
                Code: child.Code,
                Ordinal: child.Ordinal
              }))
            };

            this.$http
              .post('/BizMasterData/UpdateDictWithChildren', submitData)
              .then(response => {
                const success = response.data?.success;
                const messages = response.data?.messages;

                if (success) {
                  this.$message.success('编辑字典成功');
                  this.handleCancel();
                  this.$emit('refresh');
                } else {
                  // 显示具体的错误信息
                  let errorMessage = '保存失败';
                  if (messages && messages.length > 0) {
                    errorMessage = messages.join(', ');
                  } else if (response.data?.message) {
                    errorMessage = response.data.message;
                  }
                  this.$message.error(errorMessage);
                }
              })
              .catch(error => {
                let errorMessage = '保存失败';
                if (error.response?.data?.messages?.length > 0) {
                  errorMessage = error.response.data.messages.join(', ');
                } else if (error.response?.data?.message) {
                  errorMessage = error.response.data.message;
                } else if (error.message) {
                  errorMessage = error.message;
                }
                this.$message.error(errorMessage);
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            // 字典项操作（编辑或新增子项）
            const apiUrl = this.isEdit ? '/BizMasterData/UpdateBizDict' : '/BizMasterData/AddBizDict';
            const submitData = { ...this.formData };

            this.$http
              .post(apiUrl, submitData)
              .then(response => {
                const success = response.data?.success;
                const messages = response.data?.messages;

                if (success) {
                  this.$message.success(this.isEdit ? '编辑成功' : '新增成功');
                  this.handleCancel();
                  this.$emit('refresh');
                } else {
                  // 显示具体的错误信息
                  let errorMessage = '保存失败';
                  if (messages && messages.length > 0) {
                    errorMessage = messages.join(', ');
                  } else if (response.data?.message) {
                    errorMessage = response.data.message;
                  }
                  this.$message.error(errorMessage);
                }
              })
              .catch(error => {
                let errorMessage = '保存失败';
                if (error.response?.data?.messages?.length > 0) {
                  errorMessage = error.response.data.messages.join(', ');
                } else if (error.response?.data?.message) {
                  errorMessage = error.response.data.message;
                } else if (error.message) {
                  errorMessage = error.message;
                }
                this.$message.error(errorMessage);
              })
              .finally(() => {
                this.loading = false;
              });
          }
        }
      });
    },
    
    // 取消操作
    handleCancel() {
      this.showDialog = false;
      this.formData = {
        ID: '',
        Name: '',
        Code: '',
        ParentId: ''
      };
      this.$refs.formRef?.resetFields();
    },

    // 重置字典项表单
    resetChildForm() {
      this.childFormData = {
        Name: '',
        Code: ''
      };
      this.showChildForm = false;
      this.editingChildIndex = -1;
      this.$nextTick(() => {
        this.$refs.childFormRef?.clearValidate();
      });
    },

    // 显示字典项表单
    showChildFormDialog() {
      if (!this.formData.Code) {
        this.$message.warning('请先填写字典编码');
        return;
      }
      this.resetChildForm();
      this.showChildForm = true;
    },

    // 编辑字典项
    editChildItem(index) {
      const item = this.childrenList[index];
      // 移除前缀显示给用户编辑
      const prefix = this.formData.Code + '_';
      const codeWithoutPrefix = item.Code && item.Code.startsWith(prefix)
        ? item.Code.substring(prefix.length)
        : item.Code;

      this.childFormData = {
        ...item,
        Code: codeWithoutPrefix // 显示不带前缀的编码
      };
      this.editingChildIndex = index;
      this.showChildForm = true;
    },

    // 保存字典项
    saveChildItem() {
      this.$refs.childFormRef.validate((valid) => {
        if (valid) {
          // 构建完整的编码（包含前缀）
          const fullCode = this.formData.Code + '_' + this.childFormData.Code;

          // 检查编码是否重复
          const existingIndex = this.childrenList.findIndex((item, index) =>
            item.Code === fullCode && index !== this.editingChildIndex
          );

          if (existingIndex !== -1) {
            this.$message.error('字典项编码不能重复');
            return;
          }

          const itemData = {
            ...this.childFormData,
            Code: fullCode // 保存完整编码
          };

          if (this.editingChildIndex === -1) {
            // 新增字典项
            this.childrenList.push({
              ...itemData,
              Ordinal: this.childrenList.length + 1
            });
          } else {
            // 编辑字典项
            this.childrenList[this.editingChildIndex] = {
              ...this.childrenList[this.editingChildIndex],
              ...itemData
            };
          }

          this.resetChildForm();
          this.$message.success(this.editingChildIndex === -1 ? '新增字典项成功' : '编辑字典项成功');
        }
      });
    },

    // 删除字典项
    deleteChildItem(index) {
      this.$confirm('确定要删除该字典项吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.childrenList.splice(index, 1);
        // 重新排序
        this.childrenList.forEach((item, idx) => {
          item.Order = idx + 1;
        });
        this.$message.success('删除字典项成功');
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 上移字典项
    moveChildUp(index) {
      if (index > 0) {
        const temp = this.childrenList[index];
        this.childrenList[index] = this.childrenList[index - 1];
        this.childrenList[index - 1] = temp;

        // 更新排序
        this.childrenList.forEach((item, idx) => {
          item.Ordinal = idx + 1;
        });
      }
    },

    // 下移字典项
    moveChildDown(index) {
      if (index < this.childrenList.length - 1) {
        const temp = this.childrenList[index];
        this.childrenList[index] = this.childrenList[index + 1];
        this.childrenList[index + 1] = temp;

        // 更新排序
        this.childrenList.forEach((item, idx) => {
          item.Ordinal = idx + 1;
        });
      }
    },

    // 获取显示用的编码（移除前缀）
    getDisplayCode(fullCode) {
      if (!fullCode || !this.formData.Code) {
        return fullCode;
      }
      const prefix = this.formData.Code + '_';
      return fullCode.startsWith(prefix) ? fullCode.substring(prefix.length) : fullCode;
    }
  }
};
</script>
