<!--产品简称弹框  作者：程瑞杰 -->
<template>
  <div style="width: 100%">
    <Poptip
      placement="bottom-start"
      @on-popper-hide="allMateriallBlur"
      trigger="hover"
    >
      <Input
        v-model="addressIndex"
        readonly
        :placeholder="$t('public.productReferred')"
        @on-focus="allMateriallFocus"
      ></Input>
      <div slot="content" class="CheckboxGroupTemplate">
        <div class="CheckboxGroupHide" style="white-space: pre-wrap">
          <ul
            v-for="(todo, index) in queryProductReferredCascaderAttr"
            :key="index"
          >
            <li>
              <div class="left">
                <Checkbox
                  :indeterminate="todo.indeterminate"
                  :value="todo.selectedAll"
                  @click.prevent.native="
                    handleCheckAll(index, queryProductReferredCascaderAttr)
                  "
                  ><b>{{ todo.label }}</b></Checkbox
                >
              </div>
              <div class="right">
                <CheckboxGroup
                  v-model="todo.selectedChildren"
                  @on-change="
                    checkAllGroupChange(
                      index,
                      todo.selectedChildren,
                      queryProductReferredCascaderAttr
                    )
                  "
                >
                  <div
                    v-for="(itemOne, indexOne) in todo.children"
                    class="leftAndRightOne"
                    :key="indexOne"
                  >
                    <div class="leftOne">{{ itemOne.label }}</div>
                    <div class="rightOne">
                      <Checkbox
                        v-for="itemTwo in itemOne.children"
                        :key="itemTwo.label"
                        :label="itemTwo.label"
                        >{{ itemTwo.label }}</Checkbox
                      >
                    </div>
                  </div>
                </CheckboxGroup>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </Poptip>
  </div>
</template>
<script>
    export default {
        data () {
            return {
                addressAttrHide: false,
                addressAttr: [],
                queryProductReferredCascaderAttr: []
            };
        },
        created () {
            this.initAllMateriall()
        },
        methods: {
            // 区域--全选
            handleCheckAll (index, dataAll) {
                let childrenAll = [];
                dataAll[index].children.forEach(item => {
                    item.children.forEach(itemOne => {
                        childrenAll.push(itemOne.label);
                    })
                });
                if (dataAll[index].indeterminate) {
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].selectedAll = !dataAll[index].selectedAll;
                }
                dataAll[index].indeterminate = false;
                if (dataAll[index].selectedAll) {
                    dataAll[index].selectedChildren = childrenAll;
                    childrenAll.forEach(val => {
                        this.addressAttr.push(val)
                    })
                } else {
                    dataAll[index].selectedChildren = [];
                    let c = new Set(childrenAll);
                    let d = new Set(this.addressAttr);
                    this.addressAttr = [...new Set([...d].filter(x => !c.has(x)))];
                }
            },
            // 区域--change
            checkAllGroupChange (index, data, dataAll) {
                let selectedChildrenData = [];
                dataAll.forEach(val => {
                    if (val.selectedChildren.length > 0) {
                        val.selectedChildren.forEach(value => {
                            selectedChildrenData.push(value);
                        })
                    }
                })
                this.addressAttr = selectedChildrenData;
                let childrenAll = [];
                dataAll[index].children.forEach(item => {
                    item.children.forEach(itemOne => {
                        childrenAll.push(itemOne.label);
                    })
                });
                if (data.length == childrenAll.length) {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = true;
                } else if (data.length > 0) {
                    dataAll[index].indeterminate = true;
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = false;
                }
            },
            initAllMateriall () {
                sessionStorage.demoSpinIconLoad = 'false';
                this.$http
                    .get('/Product/QueryProductReferredCascader')
                    .then(response => {
                        let dataAll = response.data;
                        response.data.forEach(element => {
                            element.selectedAll = false;
                            element.indeterminate = false;
                            element.selectedChildren = [];
                        })
                        this.queryProductReferredCascaderAttr = dataAll;
                });
            },
            // 得到焦点
            allMateriallFocus () {
                this.$emit('input', '');
                this.addressAttr = [];
                this.queryProductReferredCascaderAttr.forEach(element => {
                    element.selectedAll = false;
                    element.indeterminate = false;
                    element.selectedChildren = [];
                })
                this.addressAttrHide = true;
            },
            allMateriallBlur () {
                let addressIndexSY = [];
                var aa = this.addressIndex.replace(/(.)(?=[^$])/g, '$1').split(',');
                for (
                    let index = 0;
                    index < this.queryProductReferredCascaderAttr.length;
                    index++
                ) {
                    for (
                        let num = 0;
                        num < this.queryProductReferredCascaderAttr[index].children.length;
                        num++
                    ) {
                        for (
                            let n = 0;
                            n <
                            this.queryProductReferredCascaderAttr[index].children[num].children
                                .length;
                            n++
                        ) {
                            for (let y = 0; y < aa.length; y++) {
                                if (
                                    this.queryProductReferredCascaderAttr[index].children[num]
                                    .children[n].label == aa[y]
                                ) {
                                    addressIndexSY.push(
                                        this.queryProductReferredCascaderAttr[index].children[num]
                                            .children[n].value
                                    );
                                }
                            }
                        }
                    }
                }
                this.$emit('input', addressIndexSY);
                this.$emit('onProductsAndTyping', addressIndexSY)
            }
        },
        computed: {
            addressIndex: function () {
                return this.addressAttr.join(',');
            }
        }
    };
</script>
<style>
.ivu-poptip-rel {
  width: 100% !important;
}
</style>

<style scoped>
.CheckboxGroupTemplate {
  max-height: 200px;
  overflow: auto;
}
.CheckboxGroupTemplate::-webkit-scrollbar {
  width: 0 !important;
}

.CheckboxGroupHide {
  width: 400px;
  background: #fff;
}
.CheckboxGroupHide ul {
  margin-top: 5px;
  margin-bottom: 5px;
  clear: both;
}
.CheckboxGroupHide ul li {
  clear: both;
  list-style-type: none;
  border-bottom: 1px solid #dddee1;
  padding-bottom: 5px;
}
.CheckboxGroupHide ul li .left {
  width: 70px;
  display: inline-block;
  vertical-align: top;
}
.CheckboxGroupHide ul li .right {
  display: inline-block;
  width: 325px;
  word-wrap: break-word;
}
.CheckboxGroupHide ul:last-child li {
  border-bottom: none;
}
.addressStyle {
  float: right;
  margin-top: 5px;
  position: absolute;
  right: 20px;
  top: 3px;
  color: #666;
  cursor: pointer;
}
.leftAndRightOne {
  border-bottom: 1px solid #dddee1;
}
.leftAndRightOne:last-child {
  border-bottom: none;
}
.leftOne {
  display: inline-block;
  width: 55px;
  word-wrap: break-word;
  vertical-align: top;
}
.rightOne {
  display: inline-block;
  width: 255px;
  word-wrap: break-word;
}
.ivu-poptip-body {
  padding-right: 0 !important;
}
div.ivu-poptip {
  width: 100% !important;
}
</style>
