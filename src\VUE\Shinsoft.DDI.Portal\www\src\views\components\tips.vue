<template>
<span class="helpCircled">
    <Tooltip placement="top">
        <Icon type="help-circled" size="28" style="color:#fd9e00"></Icon>
        <div slot="content" style="white-space: pre-wrap;"> 
            查询条件可以采用“空格”进行分割，分割后的多个关键字会进行联合模糊匹配查询。
            查询条件可以采用“半角逗号”进行分割，分割后的多个关键字分别进行完全匹配查询。
            查询条件不采用任何分隔符的情况下默认进行模糊匹配检索，以“#”开头或结尾则检索以关键字开头或结尾的信息。
        </div>
    </Tooltip>
</span>   
</template>
<style scoped>
.help
{
  margin-top: 10px;
}
.helpCircled{
  height: 32px;
  line-height: 32px;
  width: 30px;
  float: left!important;
  margin-left: 5px;
  cursor: pointer;
}

</style>
