<template>
  <div class="distributor-selector">
    <!-- 搜索区域 -->
    <div class="filter-container">
      <el-form ref="filterRef" :model="filter" inline>
        <el-form-item>
          <el-input
            v-model="filter.keywords"
            placeholder="请输入经销商编码或名称"
            clearable
            @keyup.enter="handleSearch"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch" :loading="loading">
            搜索
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        ref="tableRef"
        :data="distributorList"
        v-loading="loading"
        stripe
        size="small"
        :height="height"
        row-key="id"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <!-- 选择列 -->
        <el-table-column
          v-if="multiple"
          type="selection"
          width="50"
          align="center"
        />
        <el-table-column
          v-else
          width="50"
          align="center"
        >
          <template #default="{ row }">
            <el-radio
              :model-value="selectedSingle?.id"
              :label="row.id"
              @change="handleSingleSelect(row)"
            >
              &nbsp;
            </el-radio>
          </template>
        </el-table-column>

        <!-- 序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center" />

        <!-- 经销商编码 -->
        <el-table-column prop="code" label="经销商编码" min-width="120" />

        <!-- 经销商名称 -->
        <el-table-column prop="name" label="经销商名称" min-width="200" />

        <!-- 邮箱 -->
        <el-table-column prop="eMail" label="邮箱" width="100" />

        <!-- 电话 -->
        <el-table-column prop="telephone" label="电话" width="100" />
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.pageIndex"
        v-model:page-size="filter.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { selectorApi } from '@/api/selectorApi'

export default {
  name: 'DistributorSelector',
  props: {
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 表格高度
    height: {
      type: [String, Number],
      default: 400
    },
    // 已选中的数据（用于回显）
    modelValue: {
      type: [Array, Object],
      default: () => null
    }
  },
  emits: ['update:modelValue', 'change', 'select'],
  data() {
    return {
      loading: false,
      distributorList: [],
      totalCount: 0,
      filter: {
        pageIndex: 1,
        pageSize: 20,
        keywords: ''
      },
      selectedMultiple: [], // 多选选中的数据
      selectedSingle: null   // 单选选中的数据
    }
  },
  watch: {
    modelValue: {
      handler(newVal) {
        this.initSelectedData(newVal)
      },
      immediate: true
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    // 初始化选中数据
    initSelectedData(value) {
      if (this.multiple) {
        this.selectedMultiple = Array.isArray(value) ? value : []
      } else {
        this.selectedSingle = value || null
      }
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageIndex: this.filter.pageIndex,
          pageSize: this.filter.pageSize
        }

        // 添加关键字搜索
        if (this.filter.keywords) {
          params.code = this.filter.keywords
          params.name = this.filter.keywords
        }

        const response = await selectorApi.QueryReceiverSelectors(params)
        
        if (response.data) {
          this.distributorList = response.data.datas || []
          this.totalCount = response.data.total || 0
          
          // 恢复选中状态
          this.$nextTick(() => {
            this.restoreSelection()
          })
        }
      } catch (error) {
        console.error('加载经销商数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 恢复选中状态
    restoreSelection() {
      if (this.multiple && this.selectedMultiple.length > 0) {
        this.distributorList.forEach(row => {
          const isSelected = this.selectedMultiple.some(item => item.id === row.id)
          if (isSelected) {
            this.$refs.tableRef.toggleRowSelection(row, true)
          }
        })
      }
    },

    // 搜索
    handleSearch() {
      this.filter.pageIndex = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.filter = {
        pageIndex: 1,
        pageSize: 20,
        keywords: ''
      }
      this.loadData()
    },

    // 多选变化
    handleSelectionChange(selection) {
      this.selectedMultiple = selection
      this.$emit('update:modelValue', selection)
      this.$emit('change', selection)
      this.$emit('select', selection)
    },

    // 单选
    handleSingleSelect(row) {
      this.selectedSingle = row
      this.$emit('update:modelValue', row)
      this.$emit('change', row)
      this.$emit('select', row)
    },

    // 行点击（单选模式）
    handleRowClick(row) {
      if (!this.multiple) {
        this.handleSingleSelect(row)
      }
    },

    // 页大小变化
    handleSizeChange(size) {
      this.filter.pageSize = size
      this.filter.pageIndex = 1
      this.loadData()
    },

    // 页码变化
    handleCurrentChange(page) {
      this.filter.pageIndex = page
      this.loadData()
    },

    // 获取选中的数据
    getSelectedData() {
      return this.multiple ? this.selectedMultiple : this.selectedSingle
    },

    // 清空选择
    clearSelection() {
      if (this.multiple) {
        this.$refs.tableRef.clearSelection()
        this.selectedMultiple = []
      } else {
        this.selectedSingle = null
      }
      this.$emit('update:modelValue', this.multiple ? [] : null)
    }
  }
}
</script>
