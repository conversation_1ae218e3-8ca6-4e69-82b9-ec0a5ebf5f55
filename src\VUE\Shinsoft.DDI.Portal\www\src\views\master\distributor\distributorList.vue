<!--收货方管理页面 - 基于Element Plus组件-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>主数据管理</el-breadcrumb-item>
        <el-breadcrumb-item>收货方管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="4">
          <el-cascader
            v-model="selectedCity"
            :options="cityList"
            placeholder="省份/城市"
            clearable
            @change="handleCascaderChange"
            :props="{ 
                expandTrigger: 'hover',
                checkStrictly: true,
                emitPath: true
              }"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.name"
            placeholder="收货方名称"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.code"
            placeholder="收货方编码"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addReceiver">新增收货方</el-button>
        <el-button icon="Download" @click="exportReceiverList">导出</el-button>
      </div>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="receiverList" stripe size="small" v-loading="loading">
        <el-table-column label="序号" width="60">
          <template #default="{ $index }">
            {{ (filter.pageIndex - 1) * filter.pageIndexSize + $index + 1 }}
          </template>
        </el-table-column>        
        <el-table-column prop="code" label="收货方编码" width="120" />      
        <el-table-column prop="name" label="收货方名称" min-width="200" />
        <el-table-column prop="receiverTypeName" label="收货方类型" width="200">
          <template #default="{ row }">
            {{ row.receiverTypeLevelOneName}}-{{row.receiverTypeLevelTwoName}}-{{row.receiverTypeLevelThreeName}}
          </template>
        </el-table-column>
        <el-table-column prop="medicineGroupName" label="上级单位" min-width="150" />
        <el-table-column prop="provinceName" label="省份" width="100" />
        <el-table-column prop="cityName" label="城市" width="100" />
        <el-table-column prop="unifiedSocialCreditCode" label="统一社会信用代码" min-width="180" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="telephone" label="电话" width="130" />
        <el-table-column prop="enumStatusDesc" label="状态" width="80" />
        <el-table-column prop="stopTime" label="停用日期" width="150">
          <template #default="{ row }">
            {{ row.stopTime ? new Date(row.stopTime).toLocaleDateString('zh-CN') : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="targetTerminalName" label="目标终端" min-width="150" />       
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editReceiver(row)" />   
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" @click="removeReceiver(row)" />   
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.pageIndex"
        v-model:page-size="filter.pageIndexSize"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 收货方对话框组件 -->
    <DistributorDialog
      v-model:visible="showAddDialog"
      :record-id="currentEditId"
      @success="handleDialogSuccess"
    />

  </div>
</template>

<script>
import DistributorDialog from './components/distributorDialog.vue'
import selectorApi from '@/api/selectorApi'
import fileDownload from 'js-file-download'
import { receiverApi } from '@/api/receiverApi'

export default {
  name: 'ReceiverList',
  components: {
    DistributorDialog
  },
  data() {
    return {
      loading: false,
      selectedCity: [],
      pageSizeOpts: [10, 20, 50, 100],
      // 对话框相关
      showAddDialog: false,
      currentEditId: null,
      cityList: [],             
      filter: {
        pageIndex: 1,
        pageIndexSize: 10,
        name: '',
        code: '',
        provinceId: '',
        cityId: '',
        order: "LastEditTime desc"
      },
      totalCount: 0,
      receiverList: []               
    };
  },
  mounted() {
    this.loadReceiverList();
    this.initData();
  },
  methods: {
    /**
     * 加载收货方列表数据
     */
    loadReceiverList() {
      this.loading = true;
      receiverApi.queryReceiver(this.filter)
        .then((response) => {
          if (response.data && response.data.success) {
            // axios拦截器已经处理了嵌套的data结构，直接访问datas即可
            this.receiverList = response.data.datas || [];
            this.totalCount = response.data.total || 0;
          } else {
            this.receiverList = [];
            this.totalCount = 0;
          }
        })
        .catch((error) => {
          this.receiverList = [];
          this.totalCount = 0;
          this.$message.error("查询收货方失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * 初始化数据
     */
    initData() {
      selectorApi.ProvinceCitySelect().then(response => {
        if (response.data) {
          this.cityList = response.data || []
        }  
      })
    },

    /**
     * 查询按钮点击事件
     */
    search() {
      this.filter.pageIndex = 1;
      this.loadReceiverList();
    },

    /**
      * 分页大小改变事件
      * @param {number} size - 新的分页大小
      */
     changePageSize(size) {
       this.filter.pageIndexSize = size;
       this.filter.pageIndex = 1;
       this.loadReceiverList();
     },

    /**
     * 页码改变事件
     * @param {number} page - 新的页码
     */
    changePage(page) {
      this.filter.pageIndex = page;
      this.loadReceiverList();
    },

    /**
     * 新增收货方
     */
    addReceiver() {
      this.currentEditId = null
      this.showAddDialog = true
    },

    /**
     * 编辑收货方
     * @param {Object} row - 收货方数据行
     */
    editReceiver(row) {
      this.currentEditId = row.id
      this.showAddDialog = true
    },

    /**
     * 对话框成功事件处理
     */
    handleDialogSuccess() {
      // 刷新列表数据
      this.loadReceiverList()
      console.log('收货方保存成功，刷新列表')
    },

    /**
     * 级联选择器变化处理
     */
    handleCascaderChange(value) {
      if (value && value.length > 0) {
        this.filter.provinceId = value[0] || '';
        this.filter.cityId = value[1] || '';
      } else {
        this.filter.provinceId = '';
        this.filter.cityId = '';
      }
    },

    /**
     * 删除收货方
     * @param {Object} row - 收货方数据行
     */
    removeReceiver(row) {
      this.$confirm('确定删除该收货方吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        receiverApi.deleteReceiver({ id: row.id }).then((response) => {
          if (response.data.success) {
            this.$message.success('删除成功');
            this.loadReceiverList();
          } else {
            this.$message.error('删除失败');
          }
        });
      }).catch(() => {
        // 取消删除操作
      });
    },

    /**
     * 导出收货方列表
     */
    exportReceiverList() {
      receiverApi.exportReceiver(this.filter).then((result) => {
        var filename = '收货方信息.xlsx'
        fileDownload(result.data, filename)
      })
    }
  }
};
</script>
