import axios from '@/utils/axios'

/**
 * 产品管理相关API
 * 提供产品的增删改查等功能
 */
export const productApi = {
  /**
   * 分页查询产品列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.nameCn 产品中文名称
   * @param {string} params.commonName 通用名称
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryProduct(params) {
    return axios.get('/Product/QueryProduct', { params })
  },

  /**
   * 获取产品详情
   * @param {string} id 产品ID
   * @returns {Promise} API响应
   */
  getProduct(id) {
    return axios.get('/Product/Get', { params: { id } })
  },

  /**
   * 新增产品
   * @param {Object} data 产品数据
   * @param {string} data.manufacturerId 药企ID
   * @param {string} data.nameCn 产品中文名称
   * @param {string} data.nameEn 产品英文名称
   * @param {string} data.commonName 通用名称
   * @returns {Promise} API响应
   */
  addProduct(data) {
    return axios.post('/Product/Add', data)
  },

  /**
   * 编辑产品
   * @param {Object} data 产品数据
   * @param {string} data.id 产品ID
   * @param {string} data.manufacturerId 药企ID
   * @param {string} data.nameCn 产品中文名称
   * @param {string} data.nameEn 产品英文名称
   * @param {string} data.commonName 通用名称
   * @returns {Promise} API响应
   */
  editProduct(data) {
    return axios.post('/Product/Edit', data)
  },

  /**
   * 删除产品
   * @param {string} id 产品ID
   * @returns {Promise} API响应
   */
  deleteProduct(id) {
    return axios.post('/Product/Delete', { ID: id })
  },

  /**
   * 获取产品级联选择器数据（药企\产品\规格）
   * @returns {Promise} API响应
   */
  getProductCascader() {
    return axios.get('/Product/GetProductCascader')
  },

  /**
   * 分页查询产品规格列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 页大小
   * @param {string} params.commonName 通用名
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryProductSpec(params) {
    return axios.get('/ProductSpec/QueryProductSpec', { params })
  },

  /**
   * 删除产品规格
   * @param {string} id 产品规格ID
   * @returns {Promise} API响应
   */
  deleteProductSpec(id) {
    return axios.post('/ProductSpec/Delete', { ID: id })
  }
}

export default productApi
