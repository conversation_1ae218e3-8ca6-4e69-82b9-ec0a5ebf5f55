<!--经销商详细配置弹窗组件-->
<template>
  <el-dialog
    v-model="showDialog"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
    class="distributor-config-dialog"
    v-loading="detailLoading"
    element-loading-text="加载配置数据中..."
  >
    <el-tabs v-model="activeTab" type="card">
      <!-- 基本配置 -->
      <el-tab-pane label="基本配置" name="basic">
        <el-form :model="detailForm" label-width="140px" class="detail-tab-content" style="margin-top: 20px;margin-right: 20px;">
          <!-- 第一行：货主名称 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="货主名称">
                <el-input v-model="detailForm.ownerName" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第二行：经销商选择（新增模式）或经销商信息（编辑模式） -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经销商" required  v-if="isNewMode">
                <div class="distributor-selector-input">
                  <el-input
                    :model-value="selectedDistributorDisplayText"
                    placeholder="请选择经销商"
                    readonly
                    @click="openDistributorSelector"
                    style="cursor: pointer;"
                  >
                    <template #suffix>
                      <div class="input-suffix">
                        <el-icon @click.stop="openDistributorSelector" style="cursor: pointer;">
                          <Search />
                        </el-icon>
                        <el-icon
                          v-if="selectedDistributor"
                          @click.stop="clearSelectedDistributor"
                          style="cursor: pointer; margin-left: 5px;"
                        >
                          <CircleClose />
                        </el-icon>
                      </div>
                    </template>
                  </el-input>
                </div>
              </el-form-item>
              
              <el-form-item label="经销商名称" v-if="!isNewMode">
                <el-input v-model="detailForm.name" :disabled="!isNewMode" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="经销商编号">
                <el-input v-model="detailForm.code" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第三行：经销商电话、邮箱 -->
          <el-row :gutter="20">
            <el-col :span="12">              
              <el-form-item label="邮箱">
                <el-input v-model="detailForm.eMail" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电话">
                <el-input v-model="detailForm.telephone" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第四行：经销商地址 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="经销商地址">
                <el-input v-model="detailForm.distributorAdress" type="textarea" :rows="2" disabled />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第五行：状态、文件格式 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="状态">
                <el-select
                  v-model="detailForm.status"
                  placeholder="请选择"
                  :loading="statusLoading"
                >
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件格式">
                <el-select
                  v-model="detailForm.fileFormat"
                  placeholder="请选择"
                  :loading="fileFormatLoading"
                >
                  <el-option
                    v-for="item in fileFormatOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第六行：采集方式、文件存放目录名 -->
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="采集方式">
                <el-select
                  v-model="detailForm.collectMethod"
                  placeholder="请选择"
                  :loading="gatherMethodLoading"
                >
                  <el-option
                    v-for="item in gatherMethodOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="文件存放目录名">
                <el-input v-model="detailForm.fileDirectory" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第七行：授权码 -->
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="授权码">
                <el-input v-model="detailForm.authCode" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>

      <!-- 客户端配置 -->
      <el-tab-pane label="客户端配置" name="customerCode">
        <el-form :model="detailForm" label-width="200px" class="detail-tab-content">
          <!-- 基本运行配置 -->
          <div class="config-section">
            <h4 class="section-title">基本运行配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="运行模式">
                  <el-select
                    v-model="detailForm.runMode"
                    placeholder="请选择"
                    :loading="operatingModeLoading"
                  >
                    <el-option
                      v-for="item in operatingModeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="服务端检测频率(分)">
                  <el-input-number v-model="detailForm.serverCheckFrequency" :min="1" :max="1440" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户端当前版本">
                  <el-input v-model="detailForm.currentVersion" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="程序是否自动升级">
                  <el-select v-model="detailForm.autoUpgrade" placeholder="请选择">
                    <el-option label="是" value="true" />
                    <el-option label="否" value="false" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="需升级到的版本号">
                  <el-input v-model="detailForm.targetVersion" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="升级程序的下载地址">
                  <el-input v-model="detailForm.upgradeDownloadUrl" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 运行时间配置 -->
          <div class="config-section">
            <h4 class="section-title">运行时间配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="重复方式">
                  <el-select
                    v-model="detailForm.repeatMode"
                    placeholder="请选择"
                    :loading="repeatMethodLoading"
                  >
                    <el-option
                      v-for="item in repeatMethodOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="运行的时间点">
                  <el-time-picker v-model="detailForm.runTime" format="HH:mm" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="程序重启时间">
                  <el-time-picker v-model="detailForm.restartTime" format="HH:mm" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 数据源配置 -->
          <div class="config-section">
            <h4 class="section-title">数据源配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="数据源获取方式">
                  <el-select
                    v-model="detailForm.dataSourceType"
                    placeholder="请选择"
                    :loading="sourceTypeLoading"
                  >
                    <el-option
                      v-for="item in sourceTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="DB数据源的连接方式">
                  <el-select
                    v-model="detailForm.dbConnectionType"
                    placeholder="请选择"
                    :loading="dbConnectTypeLoading"
                  >
                    <el-option
                      v-for="item in dbConnectTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源的数据库连接">
                  <el-input v-model="detailForm.dbConnectionString" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源买进的SQL">
                  <el-input v-model="detailForm.dbBuyInSql" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源卖出的SQL">
                  <el-input v-model="detailForm.dbSellOutSql" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="DB数据源库存的SQL">
                  <el-input v-model="detailForm.dbInventorySql" type="textarea" :rows="3" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 目标上传配置 -->
          <div class="config-section">
            <h4 class="section-title">目标上传配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="目标上传方式">
                  <el-select
                    v-model="detailForm.uploadMethod"
                    placeholder="请选择"
                    :loading="uploadMethodLoading"
                  >
                    <el-option
                      v-for="item in uploadMethodOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="HTTP上传地址">
                  <el-input v-model="detailForm.httpUploadUrl" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="HTTP上传用户名">
                  <el-input v-model="detailForm.httpUploadUsername" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="HTTP上传密码">
                  <el-input v-model="detailForm.httpUploadPassword" type="password" show-password />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 日志记录配置 -->
          <div class="config-section">
            <h4 class="section-title">日志记录配置</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="日志记录方式">
                  <el-select
                    v-model="detailForm.logRecordMethod"
                    placeholder="请选择"
                    :loading="logTypeLoading"
                  >
                    <el-option
                      v-for="item in logTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="记录日志的WebService地址">
                  <el-input v-model="detailForm.logWebServiceUrl" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="记录日志的WebService用户名">
                  <el-input v-model="detailForm.logWebServiceUsername" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="记录日志的WebService密码">
                  <el-input v-model="detailForm.logWebServicePassword" type="password" show-password />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 其它参数配置 -->
          <div class="config-section">
            <h4 class="section-title">其它参数配置</h4>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="其它参数">
                  <el-input v-model="detailForm.otherParameters" type="textarea" :rows="4" placeholder="请输入其它参数配置，每行一个参数" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-tab-pane>

      <!-- 规则配置 -->
      <el-tab-pane label="规则配置" name="rules">
        <div class="detail-tab-content">
          <!-- 业务类型选项卡 -->
          <el-tabs v-model="activeRuleTab" type="card" class="business-tabs">
            <!-- 销售规则 -->
            <el-tab-pane label="销售" name="sales">
              <div class="rule-section">
                <div>
                  <div class="rule-options">
                    <el-button
                      :type="detailForm.salesPreRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.salesPreRuleCheck = !detailForm.salesPreRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      入库前规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.salesFileValidation" class="validation-checkbox">
                      文件校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesFileValidationExecution" class="validation-checkbox">
                      文件校验执行
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesFileValidationSplicing" class="validation-checkbox">
                      文件校验拼接
                    </el-checkbox>
                  </div>

                  <div class="rule-options">
                    <el-button
                      :type="detailForm.salesCleanRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.salesCleanRuleCheck = !detailForm.salesCleanRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      清洗中规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.salesStoreNameValidation" class="validation-checkbox">
                      门店名称校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesProductValidation" class="validation-checkbox">
                      产品校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesQuantityValidation" class="validation-checkbox">
                      数量校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.salesTerminalNameValidation" class="validation-checkbox">
                      终端名称校验
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 库存规则 -->
            <el-tab-pane label="库存" name="inventory">
              <div class="rule-section">
                <div class="rule-group">
                  <div class="rule-options">
                    <el-button
                      :type="detailForm.inventoryPreRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.inventoryPreRuleCheck = !detailForm.inventoryPreRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      入库前规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.inventoryFileValidation" class="validation-checkbox">
                      文件校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryFileValidationExecution" class="validation-checkbox">
                      文件校验执行
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryFileValidationSplicing" class="validation-checkbox">
                      文件校验拼接
                    </el-checkbox>
                  </div>

                  <div class="rule-options">
                    <el-button
                      :type="detailForm.inventoryCleanRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.inventoryCleanRuleCheck = !detailForm.inventoryCleanRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      清洗中规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.inventoryStoreNameValidation" class="validation-checkbox">
                      门店名称校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryProductValidation" class="validation-checkbox">
                      产品校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryQuantityValidation" class="validation-checkbox">
                      数量校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.inventoryTerminalNameValidation" class="validation-checkbox">
                      终端名称校验
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 购进规则 -->
            <el-tab-pane label="购进" name="purchase">
              <div class="rule-section">
                <div class="rule-group">
                  <div class="rule-options">
                    <el-button
                      :type="detailForm.purchasePreRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.purchasePreRuleCheck = !detailForm.purchasePreRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      入库前规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.purchaseFileValidation" class="validation-checkbox">
                      文件校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseFileValidationExecution" class="validation-checkbox">
                      文件校验执行
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseFileValidationSplicing" class="validation-checkbox">
                      文件校验拼接
                    </el-checkbox>
                  </div>

                  <div class="rule-options">
                    <el-button
                      :type="detailForm.purchaseCleanRuleCheck ? 'primary' : 'default'"
                      @click="detailForm.purchaseCleanRuleCheck = !detailForm.purchaseCleanRuleCheck"
                      size="small"
                      class="rule-button"
                    >
                      清洗中规则校验
                    </el-button>
                  </div>

                  <div class="validation-options">
                    <el-checkbox v-model="detailForm.purchaseStoreNameValidation" class="validation-checkbox">
                      门店名称校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseProductValidation" class="validation-checkbox">
                      产品校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseQuantityValidation" class="validation-checkbox">
                      数量校验
                    </el-checkbox>
                    <el-checkbox v-model="detailForm.purchaseTerminalNameValidation" class="validation-checkbox">
                      终端名称校验
                    </el-checkbox>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>

      <!-- 列映射配置 -->
      <el-tab-pane label="列映射配置" name="columnMapping">
        <div class="detail-tab-content">
          <!-- 业务类型选项卡 -->
          <el-tabs v-model="activeColumnMappingTab" type="card" class="business-tabs">
            <!-- 销售列映射 -->
            <el-tab-pane label="销售" name="sales">
              <div>
                <div class="mapping-table">
                  <el-table :data="salesColumnMapping" border size="small" class="column-table">
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="fieldName" label="数据库表列名称" min-width="200" />
                    <el-table-column prop="excelColumn" label="EXCEL列名" min-width="150" />
                    <el-table-column prop="format" label="格式" min-width="120" />
                    <el-table-column prop="required" label="必填" width="80" align="center">
                      <template #default="{ row }">
                        <el-switch v-model="row.required" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="{ row, $index }">
                        <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" class="edit-mapping-btn" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- 库存列映射 -->
            <el-tab-pane label="库存" name="inventory">
              <div>
                <div class="mapping-table">
                  <el-table :data="inventoryColumnMapping" border size="small" class="column-table">
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="fieldName" label="数据库表列名称" min-width="200" />
                    <el-table-column prop="excelColumn" label="EXCEL列名" min-width="150" />
                    <el-table-column prop="format" label="格式" min-width="120" />
                    <el-table-column prop="required" label="必填" width="80" align="center">
                      <template #default="{ row }">
                        <el-switch v-model="row.required" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="{ row, $index }">
                        <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" class="edit-mapping-btn" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <!-- 购进列映射 -->
            <el-tab-pane label="购进" name="purchase">
              <div>
                <div class="mapping-table">
                  <el-table :data="purchaseColumnMapping" border size="small" class="column-table">
                    <el-table-column type="index" label="序号" width="60" align="center" />
                    <el-table-column prop="fieldName" label="数据库表列名称" min-width="200" />
                    <el-table-column prop="excelColumn" label="EXCEL列名" min-width="150" />
                    <el-table-column prop="format" label="格式" min-width="120" />
                    <el-table-column prop="required" label="必填" width="80" align="center">
                      <template #default="{ row }">
                        <el-switch v-model="row.required" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" align="center">
                      <template #default="{ row, $index }">
                        <el-button icon="Edit" circle size="small" @click="editMapping(row, $index)" class="edit-mapping-btn" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          保存
        </el-button>
      </div>
    </template>

    <!-- 经销商选择器对话框 -->
    <el-dialog
      v-model="showDistributorSelectorDialog"
      title="选择经销商"
      width="50%"
      :close-on-click-modal="false"
      append-to-body
    >
      <DistributorSelector
        ref="distributorSelectorRef"
        :multiple="false"
        :height="260"
        v-model="selectedDistributor"
        @change="handleDistributorSelectorChange"
      />

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDistributorSelectorDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmDistributorSelection">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { configApi } from '@/api/configApi'
import { selectorApi } from '@/api/selectorApi'
import { Search, CircleClose } from '@element-plus/icons-vue'
import DistributorSelector from '@/components/distributorSelector.vue'

export default {
  name: 'DistributorConfigDialog',
  components: {
    DistributorSelector,
    Search,
    CircleClose
  },
  props: {
    // 控制对话框显示/隐藏
    modelValue: {
      type: Boolean,
      default: false
    },
    // 对话框标题
    dialogTitle: {
      type: String,
      default: '经销商配置'
    },
    // 表单数据
    detailForm: {
      type: Object,
      default: () => ({})
    },
    // 保存加载状态
    saveLoading: {
      type: Boolean,
      default: false
    },
    // 当前经销商ID
    currentDistributorId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:modelValue', 'save', 'close', 'edit-mapping', 'detail-loaded'],
  data() {
    return {
      activeTab: 'basic',
      activeRuleTab: 'sales',
      activeColumnMappingTab: 'sales',
      detailLoading: false, // 详细数据加载状态
      // 新增模式相关数据
      selectedDistributorId: null, // 选择的经销商ID
      selectedDistributor: null, // 选择的经销商对象
      showDistributorSelectorDialog: false, // 经销商选择器对话框显示状态
      distributorOptions: [], // 经销商选项（保留兼容性）
      distributorLoading: false, // 经销商搜索加载状态（保留兼容性）
      // 状态选项数据
      statusOptions: [], // 状态选项列表
      statusLoading: false, // 状态数据加载状态
      // 文件格式选项数据
      fileFormatOptions: [], // 文件格式选项列表
      fileFormatLoading: false, // 文件格式数据加载状态
      // 采集方式选项数据
      gatherMethodOptions: [], // 采集方式选项列表
      gatherMethodLoading: false, // 采集方式数据加载状态
      // 运行模式选项数据
      operatingModeOptions: [], // 运行模式选项列表
      operatingModeLoading: false, // 运行模式数据加载状态
      // 重复方式选项数据
      repeatMethodOptions: [], // 重复方式选项列表
      repeatMethodLoading: false, // 重复方式数据加载状态
      // 数据源类型选项数据
      sourceTypeOptions: [], // 数据源类型选项列表
      sourceTypeLoading: false, // 数据源类型数据加载状态
      // DB连接类型选项数据
      dbConnectTypeOptions: [], // DB连接类型选项列表
      dbConnectTypeLoading: false, // DB连接类型数据加载状态
      // 上传方式选项数据（注意：这里使用相同的DBConnectType）
      uploadMethodOptions: [], // 上传方式选项列表
      uploadMethodLoading: false, // 上传方式数据加载状态
      // 日志记录方式选项数据
      logTypeOptions: [], // 日志记录方式选项列表
      logTypeLoading: false, // 日志记录方式数据加载状态
      // 列映射数据
      salesColumnMapping: [
        { fieldName: '经销商编号', excelColumn: 'A', format: '文本', required: true },
        { fieldName: '门店编号', excelColumn: 'B', format: '文本', required: true },
        { fieldName: '门店名称', excelColumn: 'C', format: '文本', required: true },
        { fieldName: '产品编号', excelColumn: 'D', format: '文本', required: true },
        { fieldName: '产品名称', excelColumn: 'E', format: '文本', required: true },
        { fieldName: '销售数量', excelColumn: 'F', format: '数字', required: true },
        { fieldName: '销售金额', excelColumn: 'G', format: '货币', required: true },
        { fieldName: '销售日期', excelColumn: 'H', format: '日期', required: true }
      ],
      inventoryColumnMapping: [
        { fieldName: '经销商编号', excelColumn: 'A', format: '文本', required: true },
        { fieldName: '门店编号', excelColumn: 'B', format: '文本', required: true },
        { fieldName: '门店名称', excelColumn: 'C', format: '文本', required: true },
        { fieldName: '产品编号', excelColumn: 'D', format: '文本', required: true },
        { fieldName: '产品名称', excelColumn: 'E', format: '文本', required: true },
        { fieldName: '库存数量', excelColumn: 'F', format: '数字', required: true },
        { fieldName: '库存金额', excelColumn: 'G', format: '货币', required: true },
        { fieldName: '盘点日期', excelColumn: 'H', format: '日期', required: true }
      ],
      purchaseColumnMapping: [
        { fieldName: '经销商编号', excelColumn: 'A', format: '文本', required: true },
        { fieldName: '门店编号', excelColumn: 'B', format: '文本', required: true },
        { fieldName: '门店名称', excelColumn: 'C', format: '文本', required: true },
        { fieldName: '产品编号', excelColumn: 'D', format: '文本', required: true },
        { fieldName: '产品名称', excelColumn: 'E', format: '文本', required: true },
        { fieldName: '购进数量', excelColumn: 'F', format: '数字', required: true },
        { fieldName: '购进金额', excelColumn: 'G', format: '货币', required: true },
        { fieldName: '购进日期', excelColumn: 'H', format: '日期', required: true }
      ]
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    // 判断是否为新增模式
    isNewMode() {
      return !this.currentDistributorId
    },
    // 选中经销商的显示文本
    selectedDistributorDisplayText() {
      if (this.selectedDistributor) {
        return this.selectedDistributor.name
      }
      return ''
    }
  },
  watch: {
    // 监听对话框显示状态
    modelValue(newVal) {
      if (newVal) {
        
        // 加载所有选项数据
        this.loadStatusOptions();
        this.loadFileFormatOptions();
        this.loadGatherMethodOptions();
        this.loadOperatingModeOptions();
        this.loadRepeatMethodOptions();
        this.loadSourceTypeOptions();
        this.loadDbConnectTypeOptions();
        this.loadUploadMethodOptions();
        this.loadLogTypeOptions();

        if (this.currentDistributorId) {
          // 编辑模式：加载详细数据
          this.loadDetailData();
        } else {
          // 新增模式：初始化数据
          this.initNewMode();
        }
      } else {
        // 对话框关闭时重置数据
        this.resetNewModeData();
      }
    }
  },
  methods: {
    // 加载状态选项数据
    async loadStatusOptions() {
      this.statusLoading = true;
      try {
        const response = await selectorApi.getDicts('SdrClientStatus');

        if (response.data && response.data.success !== false) {
          // 处理返回的数据，转换为下拉框需要的格式
          this.statusOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取状态选项失败:', response.data?.messages || '未知错误');
          this.statusOptions = [];
        }
      } catch (error) {
        console.error('加载状态选项失败:', error);
        this.statusOptions = [];
        this.$message.error('加载状态选项失败');
      } finally {
        this.statusLoading = false;
      }
    },

    // 加载文件格式选项数据
    async loadFileFormatOptions() {
      this.fileFormatLoading = true;
      try {
        const response = await selectorApi.getDicts('FileFormat');

        if (response.data && response.data.success !== false) {
          this.fileFormatOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取文件格式选项失败:', response.data?.messages || '未知错误');
          this.fileFormatOptions = [];
        }
      } catch (error) {
        console.error('加载文件格式选项失败:', error);
        this.fileFormatOptions = [];
        this.$message.error('加载文件格式选项失败');
      } finally {
        this.fileFormatLoading = false;
      }
    },

    // 加载采集方式选项数据
    async loadGatherMethodOptions() {
      this.gatherMethodLoading = true;
      try {
        const response = await selectorApi.getDicts('GatherMethod');

        if (response.data && response.data.success !== false) {
          this.gatherMethodOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取采集方式选项失败:', response.data?.messages || '未知错误');
          this.gatherMethodOptions = [];
        }
      } catch (error) {
        console.error('加载采集方式选项失败:', error);
        this.gatherMethodOptions = [];
        this.$message.error('加载采集方式选项失败');
      } finally {
        this.gatherMethodLoading = false;
      }
    },

    // 加载运行模式选项数据
    async loadOperatingModeOptions() {
      this.operatingModeLoading = true;
      try {
        const response = await selectorApi.getDicts('OperatingMode');

        if (response.data && response.data.success !== false) {
          this.operatingModeOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取运行模式选项失败:', response.data?.messages || '未知错误');
          this.operatingModeOptions = [];
        }
      } catch (error) {
        console.error('加载运行模式选项失败:', error);
        this.operatingModeOptions = [];
        this.$message.error('加载运行模式选项失败');
      } finally {
        this.operatingModeLoading = false;
      }
    },

    // 加载重复方式选项数据
    async loadRepeatMethodOptions() {
      this.repeatMethodLoading = true;
      try {
        const response = await selectorApi.getDicts('RepeatMethod');

        if (response.data && response.data.success !== false) {
          this.repeatMethodOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取重复方式选项失败:', response.data?.messages || '未知错误');
          this.repeatMethodOptions = [];
        }
      } catch (error) {
        console.error('加载重复方式选项失败:', error);
        this.repeatMethodOptions = [];
        this.$message.error('加载重复方式选项失败');
      } finally {
        this.repeatMethodLoading = false;
      }
    },

    // 加载数据源类型选项数据
    async loadSourceTypeOptions() {
      this.sourceTypeLoading = true;
      try {
        const response = await selectorApi.getDicts('SourceType');

        if (response.data && response.data.success !== false) {
          this.sourceTypeOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取数据源类型选项失败:', response.data?.messages || '未知错误');
          this.sourceTypeOptions = [];
        }
      } catch (error) {
        console.error('加载数据源类型选项失败:', error);
        this.sourceTypeOptions = [];
        this.$message.error('加载数据源类型选项失败');
      } finally {
        this.sourceTypeLoading = false;
      }
    },

    // 加载DB连接类型选项数据
    async loadDbConnectTypeOptions() {
      this.dbConnectTypeLoading = true;
      try {
        const response = await selectorApi.getDicts('DBConnectType');

        if (response.data && response.data.success !== false) {
          this.dbConnectTypeOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取DB连接类型选项失败:', response.data?.messages || '未知错误');
          this.dbConnectTypeOptions = [];
        }
      } catch (error) {
        console.error('加载DB连接类型选项失败:', error);
        this.dbConnectTypeOptions = [];
        this.$message.error('加载DB连接类型选项失败');
      } finally {
        this.dbConnectTypeLoading = false;
      }
    },

    // 加载上传方式选项数据（使用DBConnectType）
    async loadUploadMethodOptions() {
      this.uploadMethodLoading = true;
      try {
        const response = await selectorApi.getDicts('DBConnectType');

        if (response.data && response.data.success !== false) {
          this.uploadMethodOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取上传方式选项失败:', response.data?.messages || '未知错误');
          this.uploadMethodOptions = [];
        }
      } catch (error) {
        console.error('加载上传方式选项失败:', error);
        this.uploadMethodOptions = [];
        this.$message.error('加载上传方式选项失败');
      } finally {
        this.uploadMethodLoading = false;
      }
    },

    // 加载日志记录方式选项数据
    async loadLogTypeOptions() {
      this.logTypeLoading = true;
      try {
        const response = await selectorApi.getDicts('LogType');

        if (response.data && response.data.success !== false) {
          this.logTypeOptions = (response.data.data || response.data || []).map(item => ({
            label: item.name || item.label || item.text,
            value: item.code || item.value || item.id
          }));
        } else {
          console.error('获取日志记录方式选项失败:', response.data?.messages || '未知错误');
          this.logTypeOptions = [];
        }
      } catch (error) {
        console.error('加载日志记录方式选项失败:', error);
        this.logTypeOptions = [];
        this.$message.error('加载日志记录方式选项失败');
      } finally {
        this.logTypeLoading = false;
      }
    },

    // 加载详细数据
    async loadDetailData() {
      if (!this.currentDistributorId) {
        return;
      }

      this.detailLoading = true;

      try {
        // 并行加载详细数据和所有选项数据
        const [detailResponse] = await Promise.all([
          configApi.getReceiverClient(this.currentDistributorId)
        ]);

        if (detailResponse.data && detailResponse.data.success !== false) {
          // 触发父组件更新详细数据
          this.$emit('detail-loaded', detailResponse.data.data || detailResponse.data);
        } else {
          this.$message.error(detailResponse.data.messages?.[0] || '获取详细配置失败');
        }
      } catch (error) {
        console.error('获取经销商详细配置失败:', error);
        this.$message.error('获取详细配置失败，请稍后重试');
      } finally {
        this.detailLoading = false;
      }
    },

    // 初始化新增模式
    initNewMode() {
      this.selectedDistributorId = null;
      this.selectedDistributor = null;
      this.showDistributorSelectorDialog = false;
      this.distributorOptions = [];
      // 设置默认的货主名称
      if (this.detailForm) {
        this.detailForm.ownerName = '科盟贸易';
      }
    },

    // 重置新增模式数据
    resetNewModeData() {
      this.selectedDistributorId = null;
      this.selectedDistributor = null;
      this.showDistributorSelectorDialog = false;
      this.distributorOptions = [];
      this.distributorLoading = false;
      this.statusOptions = [];
      this.statusLoading = false;
      this.fileFormatOptions = [];
      this.fileFormatLoading = false;
      this.gatherMethodOptions = [];
      this.gatherMethodLoading = false;
      this.operatingModeOptions = [];
      this.operatingModeLoading = false;
      this.repeatMethodOptions = [];
      this.repeatMethodLoading = false;
      this.sourceTypeOptions = [];
      this.sourceTypeLoading = false;
      this.dbConnectTypeOptions = [];
      this.dbConnectTypeLoading = false;
      this.uploadMethodOptions = [];
      this.uploadMethodLoading = false;
      this.logTypeOptions = [];
      this.logTypeLoading = false;
    },

    // 加载经销商选项
    async loadDistributorOptions(query = '') {
      this.distributorLoading = true;
      try {
        // 调用API获取经销商列表
        const params = {
          pageIndex: 1,
          pageSize: 50,
          name: query
        };

        const response = await configApi.queryReceiverClient(params);

        if (response.data && response.data.success !== false) {
          this.distributorOptions = (response.data.datas || []).map(item => ({
            id: item.id,
            code: item.distributorCode || item.code,
            name: item.distributorName || item.name,
            address: item.address || '',
            provinceName: item.provinceName || '',
            cityName: item.cityName || ''
          }));
        } else {
          this.distributorOptions = [];
        }
      } catch (error) {
        console.error('加载经销商选项失败:', error);
        this.distributorOptions = [];
      } finally {
        this.distributorLoading = false;
      }
    },

    // 搜索经销商
    searchDistributors(query) {
      if (query) {
        this.loadDistributorOptions(query);
      } else {
        this.loadDistributorOptions();
      }
    },

    // 打开经销商选择器
    openDistributorSelector() {
      this.showDistributorSelectorDialog = true;
    },

    // 清空选中的经销商
    clearSelectedDistributor() {
      this.selectedDistributor = null;
      this.selectedDistributorId = null;
      // 清空表单中的经销商信息
      if (this.detailForm) {
        this.detailForm.code = '';
        this.detailForm.name = '';
        this.detailForm.distributorAdress = '';
        this.detailForm.region = '';
      }
    },

    // 处理经销商选择器变化
    handleDistributorSelectorChange(distributor) {
      // 这里可以添加实时变化的处理逻辑
      console.log('经销商选择变化:', distributor);
    },

    // 确认经销商选择
    confirmDistributorSelection() {
      if (this.selectedDistributor && this.detailForm) {
        // 填充经销商信息
        this.selectedDistributorId = this.selectedDistributor.id;
        this.detailForm.code = this.selectedDistributor.code;
        this.detailForm.name = this.selectedDistributor.name;
        this.detailForm.address = this.selectedDistributor.address || '';
        this.detailForm.eMail = this.selectedDistributor.eMail;
        this.detailForm.telephone = this.selectedDistributor.telephone;
      }
      this.showDistributorSelectorDialog = false;
    },

    // 处理经销商选择变化（保留兼容性）
    handleDistributorChange(distributorId) {
      const selectedDistributor = this.distributorOptions.find(item => item.id === distributorId);
      if (selectedDistributor && this.detailForm) {
        // 填充经销商信息
        this.detailForm.code = selectedDistributor.code;
        this.detailForm.name = selectedDistributor.name;
        this.detailForm.distributorAdress = selectedDistributor.address;
        this.detailForm.region = `${selectedDistributor.provinceName}/${selectedDistributor.cityName}`;
      }
    },

    // 处理关闭对话框
    handleClose() {
      this.$emit('close')
    },
    // 处理保存
    handleSave() {
      this.$emit('save')
    },
    // 编辑列映射
    editMapping(row, index) {
      this.$emit('edit-mapping', row, index)
    },

    // 处理编辑列映射
    editMapping(row, index) {
      this.$emit('edit-mapping', row, index)
    }
  }
}
</script>

<style scoped>
/* 配置区块样式 */
.config-section {
  margin-bottom: 10px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

/* 业务选项卡样式 */
.business-tabs {
  margin-top: 15px;
}

.business-tabs :deep(.el-tabs__header) {
  margin-bottom: 20px;
}

/* 规则配置样式 */
.rule-section {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.rule-options {
  margin-bottom: 15px;
}

.rule-button {
  margin-right: 10px;
}

.validation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-left: 20px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.validation-checkbox {
  margin-right: 0;
}

/* 表格通用样式 */
.mapping-table,
.communication-log-list {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.column-table,
.communication-log-table {
  width: 100%;
}

.column-table :deep(.el-table__header),
.communication-log-table :deep(.el-table__header) {
  background-color: #f5f7fa;
}

/* 编辑按钮样式 */
.edit-mapping-btn {
  color: #fd9e00;
  border-color: #fd9e00;
}

.edit-mapping-btn:hover {
  background-color: #fd9e00;
  color: #fff;
}



/* 对话框样式 */
.distributor-config-dialog :deep(.el-dialog__header) {
  background-color: #f5f7fa;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.distributor-config-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.distributor-config-dialog :deep(.el-dialog__body) {
  padding: 0;
}

/* 选项卡通用样式 */
:deep(.el-tabs__header) {
  margin-bottom: 0 !important;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__nav) {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

:deep(.el-tabs--card > .el-tabs__header .el-tabs__item) {
  border-bottom: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
}

:deep(.el-tabs__content) {
  padding: 0;
}

/* 经销商选择器输入框样式 */
.distributor-selector-input {
  width: 100%;
}

.input-suffix {
  display: flex;
  align-items: center;
}
</style>
