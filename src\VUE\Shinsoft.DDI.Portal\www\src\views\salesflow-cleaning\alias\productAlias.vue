<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>数据清洗</el-breadcrumb-item>
        <el-breadcrumb-item>别名管理</el-breadcrumb-item>
        <el-breadcrumb-item>产品别名</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-cascader
            v-model="filter.productSpec"
            :options="productList"
            placeholder="产品/规格"
            clearable
            :props="{ checkStrictly: true, emitPath: false }"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.aliasName"
            placeholder="产品别名"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addProductAlias">新增别名</el-button>
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="productAliasList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="productCode" label="产品编码" width="120" />
        <el-table-column prop="productName" label="产品名称" min-width="180" />
        <el-table-column prop="productSpec" label="产品规格" width="120" />
        <el-table-column prop="genericName" label="通用名" min-width="150" />
        <el-table-column prop="productAlias" label="产品别名" min-width="180" />
        <el-table-column prop="productSpecAlias" label="产品规格别名" width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editProductAlias(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" type="danger" @click="deleteProductAlias(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 编辑弹窗 -->
    <ProductAliasEdit
      v-model:visible="showEditDialog"
      :edit-data="currentEditData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script>
import ProductAliasEdit from './components/productAliasEdit.vue'

export default {
  name: 'ProductAlias',
  components: {
    ProductAliasEdit
  },
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        productSpec: '',
        aliasName: '',
        status: ''
      },
      totalCount: 0,
      // 产品级联选择数据
      productList: [
        {
          value: 'P001',
          label: '阿司匹林肠溶片',
          children: [
            { value: 'P001-S001', label: '100mg*30片' },
            { value: 'P001-S002', label: '75mg*30片' },
            { value: 'P001-S003', label: '50mg*30片' }
          ]
        },
        {
          value: 'P002',
          label: '青霉素注射液',
          children: [
            { value: 'P002-S001', label: '80万单位*10支' },
            { value: 'P002-S002', label: '160万单位*10支' }
          ]
        },
        {
          value: 'P003',
          label: '布洛芬缓释胶囊',
          children: [
            { value: 'P003-S001', label: '0.3g*20粒' },
            { value: 'P003-S002', label: '0.2g*20粒' }
          ]
        },
        {
          value: 'P004',
          label: '手术刀片',
          children: [
            { value: 'P004-S001', label: '11号*10片' },
            { value: 'P004-S002', label: '15号*10片' }
          ]
        }
      ],
      showEditDialog: false,
      currentEditData: {},
      productAliasList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          productCode: 'P001',
          productName: '阿司匹林肠溶片',
          productSpec: '100mg*30片',
          genericName: '乙酰水杨酸',
          productAlias: '阿斯匹林',
          productSpecAlias: '100毫克*30片'
        },
        {
          id: 2,
          productCode: 'P001',
          productName: '阿司匹林肠溶片',
          productSpec: '100mg*30片',
          genericName: '乙酰水杨酸',
          productAlias: 'Aspirin',
          productSpecAlias: '0.1g*30片'
        },
        {
          id: 3,
          productCode: 'P002',
          productName: '青霉素注射液',
          productSpec: '80万单位*10支',
          genericName: '青霉素G钠',
          productAlias: '盘尼西林',
          productSpecAlias: '800000U*10支'
        },
        {
          id: 4,
          productCode: 'P003',
          productName: '布洛芬缓释胶囊',
          productSpec: '0.3g*20粒',
          genericName: '布洛芬',
          productAlias: '布洛芬胶囊',
          productSpecAlias: '300mg*20粒'
        },
        {
          id: 5,
          productCode: 'P004',
          productName: '手术刀片',
          productSpec: '11号*10片',
          genericName: '一次性手术刀片',
          productAlias: '一次性手术刀',
          productSpecAlias: '11#*10片'
        }
      ]
    }
  },
  computed: {
    // 获取选中的产品信息
    selectedProductInfo() {
      if (!this.filter.productSpec) return null;

      // 查找选中的产品
      for (const product of this.productList) {
        if (this.filter.productSpec === product.value) {
          return { productName: product.label, productSpec: null };
        }
        // 查找选中的规格
        if (product.children) {
          for (const spec of product.children) {
            if (this.filter.productSpec === spec.value) {
              return {
                productName: product.label,
                productSpec: spec.label
              };
            }
          }
        }
      }
      return null;
    }
  },
  mounted() {
    this.loadProductAliasList();
  },
  methods: {
    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadProductAliasList();
    },

    // 加载产品别名列表数据
    loadProductAliasList() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.productAliasList;

        // 根据产品/规格过滤
        if (this.filter.productSpec) {
          filteredList = filteredList.filter(item => {
            // 如果选择的是产品级别（如P001），则匹配产品编码
            if (this.filter.productSpec.startsWith('P') && !this.filter.productSpec.includes('-S')) {
              return item.productCode === this.filter.productSpec;
            }
            // 如果选择的是规格级别（如P001-S001），则需要匹配产品编码和规格
            if (this.filter.productSpec.includes('-S')) {
              const productCode = this.filter.productSpec.split('-S')[0];
              return item.productCode === productCode;
            }
            return true;
          });
        }

        // 根据产品别名过滤
        if (this.filter.aliasName) {
          filteredList = filteredList.filter(item =>
            item.productAlias.includes(this.filter.aliasName)
          );
        }

        // 根据状态过滤
        if (this.filter.status) {
          filteredList = filteredList.filter(item =>
            item.status === this.filter.status
          );
        }

        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.productAliasList = filteredList.slice(start, end);

        this.loading = false;
      }, 500);
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadProductAliasList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.page = page;
      this.loadProductAliasList();
    },
    // 新增别名
    addProductAlias() {
      this.currentEditData = {};
      this.showEditDialog = true;
    },
    // 编辑别名
    editProductAlias(row) {
      this.currentEditData = { ...row };
      this.showEditDialog = true;
    },
    // 删除别名
    deleteProductAlias(row) {
      this.$confirm(`确定要删除别名"${row.productAlias}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },

    // 编辑成功处理
    handleEditSuccess(data) {
      if (data.id && this.productAliasList.find(item => item.id === data.id)) {
        // 更新现有记录
        const index = this.productAliasList.findIndex(item => item.id === data.id);
        if (index !== -1) {
          this.productAliasList.splice(index, 1, data);
        }
      } else {
        // 新增记录
        this.productAliasList.unshift(data);
        this.totalCount++;
      }

      // 重新加载数据以保持分页正确
      this.loadProductAliasList();
    }
  }
}
</script>
