<template>
  <div>
    <div class="divider">
      <Checkbox
        :indeterminate="indeterminate"
        :value="checkAll"
        @click.prevent.native="handleCheckAll"
        >{{ $t("system.checkAll") }}</Checkbox
      >
    </div>
    <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
      <Row>
        <ul
          v-for="(value, key, index) in column"
          :key="index"
          class="customexportUl"
        >
          <li><Checkbox :label="key" :key="value"></Checkbox></li>
        </ul>
      </Row>
    </CheckboxGroup>
    <div slot="footer">
      <Row class="ivu-row-Padding-5 ivu-row-Top">
        <Col span="24">
          <Row type="flex" justify="end" :gutter="10">
            <Col span="2.5">
              <Button @click="handleSubmit()" icon="ios-checkmark-outline">{{
                $t("system.save")
              }}</Button>
            </Col>
            <Col span="2.5">
              <Button
                @click="handlecancel()"
                icon="ios-close-outline"
                style="margin-left: 8px"
                >{{ $t("system.cancel") }}</Button
              >
            </Col>
          </Row>
        </Col>
      </Row>
    </div>
  </div>
</template>
<script>
export default {
  props: ["column"],
  data() {
    return {
      indeterminate: false,
      checkAll: false,
      checkAllGroup: []
    };
  },
  methods: {
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        this.checkAllGroup = this.allColumn;
      } else {
        this.checkAllGroup = [];
      }
    },
    checkAllGroupChange(data) {
      if (data.length === this.allColumn.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    handleSubmit() {
      //检查是否选中列
      if (this.checkAllGroup.length == 0) {
        // 未选择任何列，请重新选择需要导出的列
        this.$Message.error(this.$t('system.unselectedColumn'));
        return;
      }
      this.$emit("exportSubmitEvent", this.checkAllGroup);
      this.indeterminate = false;
      this.checkAll = false;
      this.checkAllGroup = [];
    },
    handlecancel() {
      this.indeterminate = false;
      this.checkAll = false;
      this.checkAllGroup = [];
      this.$emit("exportCancelEvent");
    }
  },
  computed: {
    allColumn() {
      let array = Object.keys(this.column).map(key => key);
      return array;
    }
  },
  watch: {
    column(val) {
      this.indeterminate = false;
      this.checkAll = true;
      for (let key in val) {
        this.checkAllGroup.push(key);
      }
    }
  }
};
</script>
<style lang="less" scoped>
.divider {
  border-bottom: 1px solid #e9e9e9;
  padding-bottom: 6px;
  margin-bottom: 6px;
}
.customexportUl li {
  list-style: none;
  width: 25%;
  float: left;
}
</style>
