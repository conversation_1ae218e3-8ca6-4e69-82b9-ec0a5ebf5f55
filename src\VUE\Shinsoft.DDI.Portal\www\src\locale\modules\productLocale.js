// 主数据管理-->产品-->中英文
export const productLocale = {
    'zhCN': {
        public: {
            description: '描述',
            productReferred: '产品简称',
            whetherDeleteMateriel: '是否删除该物料?',
            whetherDeletePoductName: '是否删除该产品别名?',
            whetherDeleteClassification: '是否删除该分型?',
            whetherDisableMaterialGroup: '是否停用该分型?',
            whetherDeleteBrand: '是否删除该品牌?',
            whetherDeleteBU: '是否删除该BU?',
            whetherDeleteProductLine: '是否删除该产品线?',
            whetherDeleteBatch: '是否删除该批号管理?',
            whetherDeleteBatchAlias: '是否删除该产品批号?',
            whetherDeleteList: '是否上市该产品规格?',
            whetherDeleteDelisting: '是否退市该产品规格?',
            whetherDeleteProductGroup: '是否删除该权限组?',
            whetherDeleteQuota: '是否删除阶段潜力?',
            whetherDeleteReceiverQuota: '是否删除终端潜力?',
            whetherStopReceiver: '是否停用该收货方?',
            whetherStartReceiver: '是否启用该收货方?',
            warningProductLineID: '请选择产品线',
            warningName: '品牌名不能为空',
            warningNameLength: '品牌名过长',
            warningDescription: '品牌描述过长',
            warningBUID: '请选择BU',
            warningDepartmentID: '请选择部门',
            warningNameCPX: '产品线名不能为空',
            warningNameLengthCPX: '产品线名过长',
            warningDescriptionCPX: '产品线描述过长',
            warningNameBU: 'BU不能为空',
            warningNameLengthBU: 'BU名过长',
            warningDescriptionBU: 'BU描述过长',
            delBrandModal: '删除品牌成功!',
            addBrandModal: '添加品牌成功!',
            UpdateBrandModal: '更新品牌成功!',
            delBU: '删除BU成功!',
            addBU: '添加BU成功!',
            UpdateBU: '更新BU成功!',
            successDeleteList: '产品规格上市成功!',
            retreatDeleteList: '产品规格退市成功!',
            addDeleteList: '添加产品规格成功!',
            modifyDeleteList: '修改产品规格成功!',
            whetherDelNearHosptail: '当前终端存在医院周边药店的关系，停用后该关系将被删除，是否确认要停用并删除该关系？',
            whetherDeleteAccountingPrice: '是否删除该考核价?',
            whetherDeleteTargetReceiver: '是否删除该目标客户?'
        },
        productBatch: {
            materialGroup: '分型',
            materialCode: '物料号',
            material: '物料',
            batch: '批号',
            expiryDate: '有效期',
            manufactureDate: '生产日期',
            customsInDate: '进关日期',
            customsInNumber: '进关数量',
            dateSaleable: '可销售日期',
            creator: '创建人',
            createTime: '创建时间',
            finalEditor: '修改人',
            finalEditTime: '修改时间',
            isDefault: '是否默认批号',
            addBatch: '批号',
            addBatchAlias: '新增批号别名',
            productMaterialCodeMaterial: '产品简称/分型/物料',
            productMaterialCodeMaterialrequired: '产品简称/分型/物料必填',
            batchRequired: '批号必填',
            batchNumberLength: '批号过长',
            expiryDateRequired: '有效期必填',
            customsInNumberRequired: '产品规格',
            productMaterialCodeMaterialNotValid: '产品简称/分型/物料未选择物料',
            aliasNotAvailable: '批号別名不能与批号一致',
            thisDefaultBatchNumber: '此批号是默认批号',
            customsInNumberLength: '最大支持9位整数'
        },
        bu: {
            name: 'BU'
        },
        productLine: {
            name: '产品线'
        },
        brand: {
            name: '品牌',
            addBrand: '新增品牌',
            editBrand: '编辑品牌',
            productLineManagement: '产品线维护',
            bUManagement: 'BU维护'
        },
        ignoreMaterialHistroy: {
            material: '物料号',
            materialDescription: '物料说明',
            materialName: '物料名称',
            createTime: '忽略时间',
            creator: '忽略人',
            cancelTime: '取消忽略时间',
            cancelUser: '取消忽略人',
            cancelIgnore: '取消忽略',
            uploadListA: '产品批号导入模板下载',
            ignored: '已忽略'
        },
        product: {
            productLine: '产品线',
            product: '产品',
            brand: '品牌',
            nameCN: '标准名称',
            nameEN: '英文名',
            code: '产品编码',
            approvalNumber: '批准文号',
            productName: '产品名',
            genericName: '通用名',
            specification: '规格',
            packingSpecifications: '单位数量',
            packing: '包装单位',
            isColdChain: '是否冷链',
            isDelisting: '是否退市',
            allMaterial: '全部物料',
            addProduct: '新增产品规格',
            bu: 'BU',
            addMaterialGroup: '新增分型',
            addMaterial: '新增物料',
            materialDetail: '物料明细',
            productAnother: '产品别名',
            delisting: '退市',
            listing: '上市',
            productManagement: '产品维护',
            materialGroupManagement: '分型维护',
            materialManagement: '物料维护',
            certificateHolder: '持证商',
            packer: '分包商',
            productAnotherManagement: '产品别名维护',
            buProductLineBrand: 'BU/产品线/品牌',
            dosageForm: '剂型',
            defaultbatchNumber: '默认批号',
            productDefaultBatchNumber: '产品规格默认批号',
            materialGroup: '分型',
            manufacturer: '生产商',
            materialCode: '物料号',
            material: '物料',
            conversionUnit: '件装量',
            periodOfValidity: '有效期（月）',
            checkPrice: '考核价(元)',
            checkPriceRequired: '价格不能为空',
            standardPrice: '出厂价(元)',
            retailPrice: '零售价(元)',
            filingCertificateNumber: '备案号',
            createTime: '创建时间',
            materialManagerment: '物料管理',
            standardCode: '物料本位码',
            isDefaultMaterial: '是否默认物料',
            warningProductProductID: '产品简称不能为空',
            warningProductName: '产品别名',
            warningProductNameLength: '产品别名过长',
            addProductAliasSuccessfully: '添加产品别名成功!',
            successfullyModified: '修改成功!',
            deleteProductAliasSuccessfully: '删除产品别名成功!',
            specificationsBeEmpty: '规格不能为空',
            buProductLineBrandBeEmpty: 'BU/产品线/品牌',
            buProductLineBrandNotSelected: 'BU/产品线/品牌未选择品牌',
            productTypeBeEmpty: '产品类型不能为空',
            dosageFormIDBeEmpty: '剂型不能为空',
            productCategoryEmpty: '产品类型不能为空',
            sortEmpty: '排序不能为空',
            nameCNBeEmpty: '中文名称不能为空',
            nameENLength: '英文过长',
            nameCNLength: '中文名称过长',
            packingLength: '包装单位过长',
            packingSpecificationsLength: '单位数量过长',
            packingBeEmpty: '包装单位不能为空',
            packingSpecificationsBeEmpty: '单位数量不能为空',
            genericNameBeEmpty: '通用名不能为空',
            genericNameLength: '通用名称过长',
            referredBeEmpty: '产品简称不能为空',
            referredLength: '产品简称过长',
            specificationLength: '规格过长',
            defaultBatchNumberBeEmpty: '默认批号不能为空',
            defaultBatchNumberLength: '默认批号过长',
            productLineIDLength: '请选择产品线',
            fieldCannotBeZeroOrEmpty: '该字段不能为零或空',
            pleaseEnterAnInteger: '请输入整数',
            productScoreCannotBeEmpty: '产品规格/分型不能为空',
            productScoreCannotNot: '产品规格/分型未选择分型',
            scoreCannotBeEmpty: '产品规格/分型不能为空',
            materialCodeBeEmpty: '物料号不能为空',
            materialCodeLength: '物料号过长',
            materialBeEmpty: '物料不能为空',
            materialLength: '物料过长',
            conversionUnitBeEmpty: '件装量不能为空',
            conversionUnitLength: '最大支持9位整数',
            periodOfValidityBeEmpty: '有效期不能为空',
            periodOfValidityLength: '最大支持9位整数',
            retailPriceBeEmpty: '零售价不能为空',
            retailPriceLength: '最大支持9位整数',
            checkPriceBeEmpty: '最大支持9位整数',
            filingCertificateNumberBeEmpty: '备案号不能为空',
            filingCertificateNumberLength: '备案号过长',
            standardPriceLength: '最大支持9位整数',
            standardCodeLength: '物料本位码过长',
            addMaterialSuccess: '添加物料成功',
            updateMaterialSuccess: '更新物料成功',
            materialGroupBeEmpty: '分型不能为空',
            materialGroupLenght: '分型名称过长',
            manufacturerBeEmpty: '生产商不能为空',
            manufacturerLenght: '生产商过长',
            pckerLength: '分包商过长',
            certificateHolderLength: '持证商过长',
            addMaterialGroupSuccess: '添加分型成功',
            updateMaterialGroupSuccess: '更新分型成功',
            delMaterialGroupSuccess: '删除分型成功',
            checkMaterialHasBatch: '物料下存在批号，不能删除!',
            removeMaterial: '删除物料成功!',
            addProductLine: '添加产品线成功',
            updateProductLine: '更新产品线成功',
            delProductLine: '删除产品线成功',
            enableMaterialGroupSuccess: '启用分型成功',
            disableMaterialGroupSuccess: '停用分型成功',
            taxTypeCode: '税收分类编码',
            isCompensation: '是否补偿',
            externalName: '外部名称',
            externalCode: '外部编码',
            productCategory: '产品类型',
            sort: '排序',
            copyTargetReceiver: '复制目标客户',
            copyTargetReceiverMaterialGroupIdRequired: '复制目标客户的分型ID不能为空',
            shipper: '货主',
            shipperRequired: '货主不能为空'
        },
        accountingPrice: {
            startDate: '开始日期',
            endDate: '截止日期',
            productName: '产品名称',
            materialGroupName: '分型名称',
            price: '价格(元)',
            addTitle: '新增价格',
            editTitle: '编辑价格',
            priceValid: '请填写价格',
            startDateValid: '请填写开始日期',
            dateComparison: '截止日期不能早于开始日期',
            priceComparison: '价格需大于0',
            priceType: '价格类型',
            priceTypeValid: '请选择价格类型'
        }
    },
    'enUS': {
        public: {
            description: 'Description',
            productReferred: 'Product',
            whetherDeleteMateriel: 'Are you sure you want to delete it?',
            whetherDeletePoductName: 'Are you sure you want to delete it?',
            whetherDeleteClassification: 'Are you sure you want to delete it?',
            whetherDisableMaterialGroup: 'Do you want to disable the materialgroup?',
            whetherDeleteBrand: 'Are you sure you want to delete it?',
            whetherDeleteBU: 'Are you sure you want to delete it??',
            whetherDeleteProductLine: 'Are you sure you want to delete it?',
            whetherDeleteBatch: 'Are you sure you want to delete it?',
            whetherDeleteBatchAlias: 'Are you sure you want to delete it?',
            whetherDeleteList: 'Do you want to enable the product?',
            whetherDeleteDelisting: 'Do you want to disable the product?',
            whetherDeleteQuota: '是否删除阶段潜力?',
            whetherDeleteReceiverQuota: '是否删除终端潜力?',
            whetherStopReceiver: 'Do you want to disable the receiver?',
            whetherStartReceiver: 'Do you want to enable the receiver?',
            warningProductLineID: 'Required',
            warningName: 'Required',
            warningNameLength: 'Brand name is too long.',
            warningDescription: 'Brand description is too long.',
            warningBUID: 'Required',
            warningDepartmentID: 'Required',
            warningNameCPX: 'Required',
            warningNameLengthCPX: 'Product line name is too long.',
            warningDescriptionCPX: 'Product description is too long.',
            warningNameBU: 'Required',
            warningNameLengthBU: 'BU name is too long.',
            warningDescriptionBU: 'BU description is too long.',
            delBrandModal: 'Success',
            addBrandModal: 'Success',
            UpdateBrandModal: 'Success',
            delBU: 'Success',
            addBU: 'Success',
            UpdateBU: 'Success',
            successDeleteList: 'Success',
            retreatDeleteList: 'Success',
            addDeleteList: 'Success',
            modifyDeleteList: 'Success',
            whetherDelNearHosptail: 'There is a relationship between durgstore around the hospital at the current terminal. After the relationship is deactivated, it will be deleted. Are you sure you want to disable and delete the relationship? ',
            whetherDeleteAccountingPrice: '是否删除该考核价?',
            whetherDeleteTargetReceiver: '是否删除该目标客户?'
        },
        productBatch: {
            materialGroup: 'Material Group',
            materialCode: 'Material Code',
            material: 'Material',
            batch: 'Batch',
            expiryDate: 'ExpiryDate',
            manufactureDate: 'ManufactureDate',
            customsInDate: 'CustomsInDate',
            customsInNumber: 'CustomsInNumber',
            dateSaleable: 'DateSaleable',
            creator: 'Creator',
            createTime: 'CreateTime',
            finalEditor: 'FinalEditor',
            finalEditTime: 'FinalEditTime',
            isDefault: 'IsDefaultBatch',
            addBatch: 'Batch',
            addBatchAlias: 'Add BatchAlias',
            productMaterialCodeMaterial: 'Product/Material Group/Material',
            productMaterialCodeMaterialrequired: 'Required',
            batchRequired: 'Required',
            batchNumberLength: 'The batch number is too long',
            expiryDateRequired: 'Required',
            customsInNumberRequired: 'CustomsInNumber must be numeral',
            productMaterialCodeMaterialNotValid: 'The Product/Material Group/Material must choose Material',
            aliasNotAvailable: 'Batch number alias cannot be the same as batch number.',
            thisDefaultBatchNumber: 'This batch number is the default batch number.',
            customsInNumberLength: 'Maximum support for 9 digit integer'
        },
        bu: {
            name: 'BU Name'
        },
        productLine: {
            name: 'Product Line'
        },
        brand: {
            name: 'Brand Name',
            addBrand: 'Add Brand',
            editBrand: 'Edit Brand',
            productLineManagement: 'Product Line Management',
            bUManagement: 'BU Management'
        },
        ignoreMaterialHistroy: {
            material: 'Material',
            materialDescription: 'Description',
            materialName: 'Material Name',
            createTime: 'Ignore Time',
            creator: 'Ignore User',
            cancelTime: 'Cancel Time',
            cancelUser: 'Cancel User',
            cancelIgnore: 'Cancel Ignore',
            uploadListA: 'Download template',
            ignored: 'Ignored'
        },
        product: {
            productLine: 'Product Line',
            product: 'Product',
            brand: 'Brand',
            nameCN: 'Name',
            nameEN: 'English Name',
            code: 'code',
            approvalNumber: 'Approval Number',
            productName: 'Product Name',
            genericName: 'Generic Name',
            specification: 'Specification',
            isColdChain: 'IsColdChain',
            isDelisting: 'IsDelisting',
            packingSpecifications: 'Packing Specifications',
            packing: 'Packing',
            allMaterial: 'All Material',
            addProduct: 'Add Product',
            bu: 'BU',
            addMaterialGroup: 'Add Group',
            addMaterial: 'Add Material',
            materialDetail: 'MaterialDetail',
            productAnother: 'ProductAnother',
            delisting: 'Delistion',
            listing: 'Listing',
            productManagement: 'Product Management',
            materialGroupManagement: 'Group Manegement',
            certificateHolder: 'certificateHolder',
            packer: 'packer',
            materialManagement: 'Material Management',
            productAnotherManagement: 'ProductAnother Management',
            buProductLineBrand: 'BU/Product Line/Brand',
            dosageForm: 'DosageForm',
            defaultbatchNumber: 'Default Batch No.',
            materialGroup: 'Material Group',
            manufacturer: 'Manufacturer',
            materialCode: 'Material Code',
            material: 'Material',
            conversionUnit: 'Conversion Unit',
            periodOfValidity: 'Period Of Validity',
            checkPrice: 'Check Price',
            checkPriceRequired: 'Required',
            standardPrice: 'Standard Price',
            retailPrice: 'Retail Price',
            filingCertificateNumber: 'Certificate No.',
            createTime: 'Create Time',
            productDefaultBatchNumber: 'Product Batch No.',
            materialManagerment: 'Material Managerment',
            standardCode: 'Standard Code.',
            isDefaultMaterial: 'Is Default Material.',
            warningProductProductID: 'Required',
            warningProductName: 'Product Alias',
            warningProductNameLength: 'Product alias is too long.',
            addProductAliasSuccessfully: 'Success',
            successfullyModified: 'Success',
            deleteProductAliasSuccessfully: 'Success',
            specificationsBeEmpty: 'Required',
            buProductLineBrandBeEmpty: 'Required',
            buProductLineBrandNotSelected: 'Required',
            productTypeBeEmpty: 'Required',
            dosageFormIDBeEmpty: 'Required',
            productCategoryEmpty: 'Required',
            sortEmpty: 'Required',
            nameCNBeEmpty: 'Required',
            nameENLength: 'English name is too long.',
            nameCNLength: 'Name is too long.',
            genericNameBeEmpty: 'Required',
            genericNameLength: 'Common name is too long.',
            referredBeEmpty: 'Required',
            referredLength: 'Referred is too long.',
            specificationLength: 'Specification is too long.',
            defaultBatchNumberBeEmpty: 'Required',
            defaultBatchNumberLength: 'Default batch number is too long.',
            productLineIDLength: 'Required',
            fieldCannotBeZeroOrEmpty: 'The field cannot be zero or empty.',
            pleaseEnterAnInteger: 'Integers must be entered.',
            productScoreCannotBeEmpty: 'Required',
            productScoreCannotNot: 'Required',
            scoreCannotBeEmpty: 'Required',
            materialCodeBeEmpty: 'Required',
            materialCodeLength: 'Material code is too long.',
            materialBeEmpty: 'Required',
            materialLength: 'Material is too long.',
            conversionUnitBeEmpty: 'Required',
            conversionUnitLength: 'Maximum support for 9 digit integer',
            periodOfValidityBeEmpty: 'Required',
            periodOfValidityLength: 'Maximum support for 9 digit integer',
            retailPriceBeEmpty: 'Required',
            retailPriceLength: 'Maximum support for 9 digit integer',
            checkPriceBeEmpty: 'Maximum support for 9 digit integer',
            filingCertificateNumberBeEmpty: 'Required',
            filingCertificateNumberLength: 'Certificate is too long.',
            standardPriceLength: 'Maximum support for 9 digit integer.',
            standardCodeLength: 'Drug standard code is too long.',
            addMaterialSuccess: 'Success',
            updateMaterialSuccess: 'Success',
            materialGroupBeEmpty: 'Required',
            materialGroupLenght: 'Material group is too long.',
            manufacturerBeEmpty: 'Required',
            manufacturerLenght: 'Manufacturer is too long.',
            pckerLength: 'Packaging manufacturer is too long.',
            certificateHolderLength: 'Certificate holder is too long.',
            addMaterialGroupSuccess: 'Success',
            updateMaterialGroupSuccess: 'Success',
            delMaterialGroupSuccess: 'Success',
            checkMaterialHasBatch: 'The material contains batch number and cannot be deleted.',
            removeMaterial: 'Success',
            addProductLine: 'Success',
            updateProductLine: 'Success',
            delProductLine: 'Success',
            enableMaterialGroupSuccess: 'Enable Success',
            disableMaterialGroupSuccess: 'Disable Success',
            taxTypeCode: 'Tax classification code',
            isCompensation: '是否补偿',
            externalName: '外部名称',
            externalCode: '外部编码',
            productCategory: 'Product Category',
            sort: 'Sort',
            copyTargetReceiver: '复制目标客户',
            copyTargetReceiverMaterialGroupIdRequired: '复制目标客户的分型ID不能为空',
            shipper: '货主',
            shipperRequired: '货主不能为空'
        },
        accountingPrice: {
            startDate: '开始日期',
            endDate: '截止日期',
            productName: '产品名称',
            materialGroupName: '分型名称',
            price: '价格(元)',
            addTitle: '新增价格',
            editTitle: '编辑价格',
            priceValid: '请填写价格',
            startDateValid: '请填写开始日期',
            priceComparison: '价格需大于0',
            dateComparison: '截止日期不能早于开始日期',
            priceType: '价格类型',
            priceTypeValid: '请选择价格类型'
        }
    }
}
export default productLocale
