import http from '../../utils/axios'
const ignoreProductBatchHistroy = {
    state: {
        ignoreProductBatchHistroyList: [],
        totalCount: 1
    },
    mutations: {
        InitStateIgnoreProductBatchHistroyList (state, data) {
            state.ignoreProductBatchHistroyList = data.Models
            state.totalCount = data.TotalCount
        }
    },
    actions: {
        queryIgnoreProductBatchHistroyAction ({
            commit
        }, params) {
            http.get('/Product/QueryIgnoreBatch', {
                params: params
            }).then(function (response) {
                commit('InitStateIgnoreProductBatchHistroyList', response.data)
            })
        }
    }
}
export default ignoreProductBatchHistroy
