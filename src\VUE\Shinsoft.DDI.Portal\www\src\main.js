
import { createApp } from 'vue'
import router from './router/index.js'
import { appRouter } from './router/router'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './assets/element-theme.scss'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'
import { createI18n } from 'vue-i18n'
import store from './store'
import Locales from './locale'
import moment from 'moment'
import axios from 'axios'
import './assets/PublicStyle.css'
import './assets/common/style.css'
import behaviors from './assets/behaviors.js'
import publicJS from './assets/publicJS.js'
import ConstDefinition from './utils/constDefinition'
import tips from './views/components/tips.vue'

// 清除用户数据的工具函数
function clearUserData() {
    // 清除token和用户信息
    localStorage.removeItem('token');
    localStorage.removeItem('tokenStorage');
    localStorage.removeItem('userId');
    localStorage.removeItem('account');
    localStorage.removeItem('name');
    localStorage.removeItem('userInfo');

    // 清除其他可能的用户相关数据
    sessionStorage.clear();
}
import App from './App.vue'
import VueViewer from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'

// 创建axios实例
var attachmentAxios = axios.create({
    baseURL: import.meta.env.VITE_API_HOST,
    timeout: 1000 * 60 * 30
});

// 配置axios默认设置
axios.defaults.baseURL = import.meta.env.VITE_API_HOST;
axios.defaults.timeout = 1000 * 60 * 30;
axios.defaults.withCredentials = false;

// 请求拦截器
axios.interceptors.request.use(
    config => {
        // 添加token到请求头
        const token = localStorage.getItem('token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    error => {
        return Promise.reject(error);
    }
);

// 响应拦截器
axios.interceptors.response.use(
    response => {
        return response;
    },
    error => {
        if (error.response && error.response.status === 401) {
            // Token过期或无效，清除用户数据并跳转到登录页
            clearUserData();
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

// 国际化配置
const messages = {
    mergeEN: Object.assign(Locales['en-US'], en),
    mergeZH: Object.assign(Locales['zh-CN'], zhCn)
}

const i18n = createI18n({
    locale: localStorage.getItem('currentLanguage') === null ? 'mergeZH' : localStorage.getItem('currentLanguage'),
    legacy: false,
    messages
})

// 创建Vue应用
const app = createApp(App)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局属性
app.config.globalProperties.$attachemtnHttp = attachmentAxios
app.config.globalProperties.$http = axios
app.config.globalProperties.$constDefinition = ConstDefinition
app.config.globalProperties.$SiteBaseUrl = import.meta.env.VITE_API_HOST
app.config.globalProperties.behaviors = behaviors
app.config.globalProperties.publicJS = publicJS

// 全局过滤器改为全局属性
app.config.globalProperties.$filters = {
    decimalformat: (input, scale) => Number(input).toFixed(scale),
    dateformat: (input, formatstring) => moment(input).format(formatstring)
}

// 使用插件
app.use(i18n)
app.use(router)
app.use(store)
app.use(ElementPlus, { locale: zhCn })
app.use(VXETable)
app.use(VueViewer, {
    defaultOptions: {
        zIndex: 9999,
        inline: true,
        button: true,
        navbar: true,
        title: true,
        toolbar: true,
        tooltip: true,
        movable: true,
        zoomable: true,
        rotatable: true,
        scalable: true,
        transition: true,
        fullscreen: true,
        keyboard: true,
        url: 'data-source'
    }
})

// 全局组件
app.component('tips', tips)

// eslint-disable-next-line no-extend-native
Number.prototype.toFixed = function (s) {
    let num = this;
    let absNum = Math.abs(num);
    let changenum = (parseInt(absNum * Math.pow(10, s) + 0.5) / Math.pow(10, s)).toString();
    let index = changenum.indexOf('.');
    if (index < 0 && s > 0) {
        changenum = changenum + '.';
        for (let i = 0; i < s; i++) {
            changenum = changenum + '0';
        }
    } else {
        index = changenum.length - index;
        for (let i = 0; i < (s - index) + 1; i++) {
            changenum = changenum + '0';
        }
    }
    if (num < 0) { changenum = `-${changenum}`; }
    return changenum;
}

// 挂载应用
app.mount('#app')
