import http from '../../utils/axios'
const BU = {
    state: {
        buAllList: [],
        buSelectList: [],
        buModel: {}
    },
    mutations: {
        InitStateBUAll (state, data) {
            state.buAllList = data
        },
        InitBUSelect (state, data) {
            state.buSelectList = data
        },
        InitBUModel (state, data) {
            state.buModel = data
        }
    },
    actions: {
        queryAllBUAction ({
            commit
        }) {
            http.get('/Product/QueryAllBU').then(function (response) {
                commit('InitStateBUAll', response.data)
            })
        },
        queryBUSelectAction ({
            commit
        }) {
            http.get('/Product/QueryBUSelect').then(function (response) {
                commit('InitBUSelect', response.data)
            })
        },
        queryBUSelectActionBy ({
            commit
        }, params) {
            http.get('/Product/QueryBrandSelectListBy', {params: params}).then(function (response) {
                commit('InitBUSelect', response.data)
            })
        },
        getBUAction ({
            commit
        }, BUID) {
            http.get('/Product/GetBU', {
                params: { 'BUID': BUID }
            }).then(function (response) {
                commit('InitBUModel', response.data)
            })
        }
    }
}
export default BU
