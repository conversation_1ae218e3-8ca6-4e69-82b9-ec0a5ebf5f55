import { createStore } from 'vuex'
import user from './modules/user'
import BUModules from './modules/bu'
import productLineModules from './modules/productline'
import brandModules from './modules/brand'
import productBatch from './modules/productbatch'
import importProductBatchLogModules from './modules/importProductBatchLog'
import ignoreMaterialHistroyModules from './modules/ignoreMaterialHistroy'
import ignoreProductBatchHistroyModules from './modules/ignoreProductBatchHistroy'
import product from './modules/product'
import productanother from './modules/productanother'
import materialgroup from './modules/materialgroup'
import productBatchAnother from './modules/productBatchAnother'
import material from './modules/material'
import dictionary from './modules/dictionary'
import productChannelList from './modules/productChannelList'
import receiverContractor from './modules/receiverContractor'
import distributor from './modules/distributor'

const store = createStore({
    modules: {
        user: user,
        productBatch: productBatch,
        importProductBatchLog: importProductBatchLogModules,
        ignoreMaterialHistroy: ignoreMaterialHistroyModules,
        ignoreProductBatchHistroy: ignoreProductBatchHistroyModules,
        bu: BUModules,
        productLine: productLineModules,
        brand: brandModules,
        product: product,
        productanother: productanother,
        productBatchAnother: productBatchAnother,
        materialgroup: materialgroup,
        material: material,
        dictionary: dictionary,
        productChannelList: productChannelList,
        receiverContractor: receiverContractor,
        distributor: distributor
    }
})
export default store
