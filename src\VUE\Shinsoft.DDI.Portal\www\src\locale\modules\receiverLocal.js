/* eslint-disable key-spacing */
/* eslint-disable no-dupe-keys */
// 收货方主数据-->中英文
export const Receiver = {
    'zhCN': {
        drugstoreNearHospital: {
            drugstore: '药店',
            hospital: '医院',
            drugstoreParentName: '药店上级单位名称',
            drugstoreParentDistributorType: '药店上级商业类型',
            validDrugStoreNameRequired: '请选择药店',
            validHospitalNameRequired: '请选择医院',
            addDrugstoreNearHospital: '添加院边店',
            drugstoreName: '药店名称',
            drugStoreType: '药店类型',
            hospitalName: '医院名称',
            hospitalType: '医院类型'
        },
        receiverType: {
            nameCn: '收货方类型',
            nameEn: '英文名称',
            shortName: '简称',
            canbeUpgrade: '转换成流向商业',
            IsAllowBuyDoping: '是否允许兴奋剂',
            chooseProductSettingOfsuspect: '选择疑似调拨分型 - ',
            receiverTypeGroup: '收货方类型Group',
            validReceiverTypeGroupRequired: '请选择收货方类型分类',
            receiverTypeGroupAndType: '收货方类型Group/收货方类型',
            isRetail: '是否零售'
        },
        receiver: {
            // 收货方管理
            chooseHospital: '选择医院',
            receiverType: '收货方类型',
            distributorReceiverType: '流向商业类型',
            isStop: '是否停用',
            distributorType: '商业公司类型',
            receiverNameOrFormerName: '收货方（名称/曾用名）',
            receiverFormerName: '收货方曾用名',
            GBCode: 'Code',
            externalCode: '外部Code',
            stopTime: '停用日期',
            restartTime: '重启日期',
            finalEditor: '修改人',
            creator: '创建人',
            receiverName: '收货方名称',
            validNameRequired: '收货方名称不能为空',
            validTypeRequired: '收货方类型不能为空',
            validNameLength: '收货方名称过长',
            validGBCodeLength: 'Code过长',
            validGBCodeRequired: 'Code不能为空',
            validateRequiredHospitalGrade: '医院等级未选择',
            validateRequiredHospitalLevel: '医院等次未选择',
            validGBCode: '只允许输入字母或数字',
            receiverlocation: '收货方省份/城市/区县',
            distributorlocation: '流向商业省份/城市/区县',
            receiverProvinceCity: '收货方省份/城市',
            distributorProvinceCity: '流向商业省份/城市',
            senderName: '发货方名称',
            senderProvinceName: '发货方省份',
            receiverProvinceName: '收货方省份',
            receiverAnotherName: '收货方别名',
            createTime: '创建时间',
            finalEditTime: '修改时间',
            addReceiver: '添加收货方别名成功',
            editReceiver: '修改成功',
            notEqual: '发货方名称和收货方名称不能相等',
            notNull: '发货方名称和收货方名称不能为空',
            // 收货方日志
            operationType: '操作类型',
            effectiveTime: '生效时间',
            changeColumn: '更新字段',
            beforeChange: '更新前',
            afterChange: '更新后',
            operationer: '操作人',
            operationTime: '操作时间',
            confirmationer: '确认人',
            addReceiverManagement: '新增收货方',
            updateReceiverManagement: '编辑收货方',
            // 选择负责人
            selectHead: '选择负责人',
            drugstoreTag: '药店标签',
            // 选择上级单位
            selectParentName: '选择上级单位',
            senderNameBeEmpty: '发货方名称不能为空',
            receiverAnotherNameBeEmpty: '收货方别名不能为空',
            stopReceiverListShow: '收货方停用并转换',
            crmReceiverMergeTitle: 'CRM机构合并',
            targetTerminalName: '目标终端',
            targetTerminalNameNotEmpty: '目标终端不能为空',
            isTargetHospital: '是否目标医院',
            disable: '停用',
            enable: '启用',
            enumEntityStatus: '有流向的终端，需要进行目标转换',
            importTime: '导入时间',
            templateDownload: '模板下载',
            deactivateTerminal: '停用终端',
            mergeTerminal: '合并机构',
            targetTerminal: '目标终端',
            distributorName: '流向商业名称',
            pHCodeBeEmpty: '选择的目标终端Code不能为空',
            pHCodeNotEnabled: '选择的目标终端未启用',
            displayDefaultAnotherName: '是否显示默认别名',
            successfulInitiationOfReceiver: '启用收货方成功',
            successfulDiscontinuationOfConsignee: '停用收货方成功',
            targetTerminalSaveSuccess: '保存成功',
            targetTerminalGBCode: '目标终端Code',
            parentName: '上级单位',
            chooseIsGXPMessage: '请先添加授权采购联系人信息',
            synchronousSuccess: '同步成功',
            targetHospitalName: '目标医院名称',
            targetHospitalProvinceCity: '目标医院省份/城市',
            targetHospitalGBCode: 'Code',
            targetHospitalVolume: '潜力',
            targetHospitalLog: '目标医院同步日志',
            mdmValue: 'MDM数据',
            cdmsValue: 'CDMS数据',
            mdmName: 'MDM名称',
            cdmsName: 'CDMS名称',
            cdmsProvince: 'CDMS省份',
            cdmsCity: 'CDMS城市',
            cdmsCounty: 'CDMS区县',
            cdmsReceiverType: 'CDMS类型',
            cdmsGBCode: 'CDMS Code',
            gapCount: '差异数量',
            gapType: '差异类型',
            lastModifyDate: '最后更新时间',
            mdmCode: 'MDM Code',
            hasTerminalCompensationPeriod: '当前终端存在未过期的终端补偿周期，需先将终端补偿周期的结束时间缩短后才可以终端停用转换。',
            reveiverAnotherModifyAlter: '注意：修改收货方别名后仅会更新流向中的收货方信息，流向的销售类型不会更新,需人工检查相关流向。',
            mdmCodeNoSpace: 'MDMCode',
            grade: '等级等次',
            longitude: '百度经度',
            latitude: '百度纬度',
            mdmSyncTime: 'MDM同步时间',
            receiverHasAliasChangeRequestForm: '该终端在系统中存在审批中的别名变更申请单据，不能停用终端。',
            remark: '备注',
            currentReceiver: '当前收货方',
            changeReceiver: '变更收货方',
            receiverAnotherRequest: '变更申请',
            uploadFormatError: '上传文件格式错误.',
            uploadMaxSizeError: '超出上传文件最大限制，文件不能超过20M.',
            uploadError: '处理上传数据时出错，请联系管理员',
            reveiverAnotherAttachmentAlter: '只可上传一个附件，多个请打压缩包，只支持jpg、pdf、zip、rar、msg格式文件，文件不超过20M',
            validateGradeLength: '等次过长',
            validateLongitudeLength: '经度过长',
            validateLatitudeLength: '纬度过长',
            validBusinessModeRequired: '经营方式不能为空',
            address: '注册地址',
            telephone: '电话',
            o2o: 'O2O店',
            businessDistrict: '商圈店',
            medicalInsurance: '医保',
            hospitalSide: '院边店',
            community: '社区店',
            validatCommunity: '社区店请输入正整数',
            batchChange: '批量变更',
            batchChangeByBusinessLicenseNo: '批量变更【License】',
            gaudeLongitude: '高德经度',
            gaudeLatitude: '高德纬度',
            mDMCode: 'MDMCode',
            mergeReceiver: '合并机构',
            receiverInfo: '机构信息',
            mergeeceiverProvince: '机构省份'
        },
        receiverAliasChangeRequestForm: {
            originalReceiverName: '原收货方名称',
            originalReceiverGBCode: '原收货方Code',
            targetReceiverName: '变更收货方名称',
            receiverAnotherName: '收货方别名',
            requestNO: '申请单号',
            applyUserName: '申请人',
            applyTime: '申请时间',
            formStatus: '单据状态',
            distributorName: '发货方名称',
            remark: '备注',
            approvalUserName: '审批人',
            approvalTime: '审批时间',
            approvalOpinion: '审批意见',
            attachment: '附件',
            confirmApproval: '修改收货方别名后仅会更新流向中的收货方，销售类型不会更新,需人工检查相关流向。是否确认调整？',
            rejectApproval: '您确定要对此别名变更申请进行拒绝吗？'

        },
        targetReceiver: {
            department: '部门',
            receiverName: '客户名称',
            customerCategory: '客户类型',
            productName: '产品',
            materialGroupName: '分型',
            startDate: '开始日期',
            endDate: '截止日期',
            hospitalClass: '医院级别',
            hospitalClassWithoutCOE: '医院级别不含COE',
            strategyHospital: '战略医院',
            hCOLevel: 'HCO Level',
            mSLCover: 'MSL Cover',
            cNRIDDSite: 'CNRIDD Site',
            vumeritySite: 'Vumerity Site',
            tecPMS: 'Tec PMS',
            editTitle: '编辑目标客户',
            addTitle: '新增目标客户',
            productAndMaterialGroup: '产品/分型',
            departmentValid: '请选择部门',
            receiverNameValid: '请选择客户名称',
            productAndMaterialGroupValid: '请选择产品/分型',
            productValid: '请选择产品',
            product: '产品',
            customerCategoryValid: '请选择客户类型',
            startDateValid: '请选择起始日期',
            endDateValid: '请选择截止日期',
            dateComparison: '截止日期不能早于开始日期',
            region: '大区',
            commerceOrganization: '岗位',
            employeeName: '负责人',
            importantCustomerType: '重要客户类型',
            projectName: '项目名称',
            projectValid: '请填写项目名称',
            editProjectTitle: '启用项目',
            isDisplayOnQuery: '是否显示查询',
            IsEnable: '是否启用',
            mustEditName:'必须修改项目名称',
            maxNameLengthError:'项目名称不能超过十个字符',
            projectAddTitle:'新增客户分级',
            projectEditTitle:'编辑客户分级',
            deleteLevelConfirm:'删除将同时删除项目关系，是否确认？',
            projectClass:'项目值'
        },
        procurement: {
            hospital: '医院名称',
            productName: '产品',
            materialGroupName: '分型',
            productAndMaterialGroup: '产品/分型',
            startDate: '开始日期',
            endDate: '截止日期',
            procurementType: '进药类型',
            region: '大区',
            commerceOrganization: '岗位',
            employeeName: '负责人',
            whetherDeleteProcurement: '是否删除该医院进药数据?',
            addTitle: '新增医院进药',
            editTitle: '编辑医院进药',
            hospitalIsNotNull: '医院不可为空',
            procurementTypeIsNotNull: '进药类型不可为空',
            startDateIsNotNull: '起始时间不可为空',
            endDateIsNotNull: '截止时间不可为空'
        },
        crmData: {
            name: '机构名称',
            firstType: '一级类型',
            secondType: '二级类型',
            thirdType: '三级类型',
            provinceName: '省份',
            cityName: '城市',
            countyName: '区县',
            address: '地址',
            level: '等次',
            grade: '等级',
            approvalTitle: '审核机构',
            receiverType: '机构类型',
            validReceiverTypeRequired: '机构类型不能为空',
            validNameRequired: '机构名称不能为',
            validNameLength: '机构名称过长'
        },
        drugstoreOfHospital: {
            addTitle:'新增院外药店',
            editTitle:'编辑院外药店',
            selHospitalTitle:'选择医院',
            selDrugStoreTitle:'选择药店',
            monthly: '月份',
            beginMonth: '起始月份',
            endMonth: '结束月份',
            hospitalName: '医院名称',
            hospitalCode: '医院编码',
            province: '省份',
            drugstoreName: '药店名称',
            drugstoreCode: '药店编码',
            productName: '产品名称',
            materialGroupName: '规格',
            productAndMaterialGroup: '产品/分型',
            hospitalStoreType: '关联类型',
            hospitalNameValid:'医院不能为空',
            drugStoreNameValid:'药店不能为空',
            productAndMaterialGroupValid:'产品/规格不能为空',
            beginMonthValid:'起始月份不能为空',
            endMonthValid:'结束月份不能为空',
            beginMonthGreater:'起始月份不能大于结束月份',
            endMonthLess:'结束月份不能小于起始月份',
            hospitalStoreTypeValid:'关联类型必选',
            confirmDelete:'确定删除院外医院'
        }
    },
    'enUS': {
        drugstoreNearHospital: {
            drugstore: 'Drugstore ',
            hospital: 'Hospital ',
            drugstoreParentName: 'Drugstore Parent Name',
            drugstoreParentDistributorType: 'Drugstore Parent Distributor Type',
            validDrugStoreNameRequired: 'Drugstore is required',
            validHospitalNameRequired: 'Hospital is required',
            addDrugstoreNearHospital: 'Add DrugstoreNearHospital',
            drugstoreName: 'Drugstore Name',
            drugstoreGBCode: 'Drugstore Code',
            drugstoreMDMCode: 'Drugstore MDMCode',
            drugStoreType: 'Drugstore Type',
            hospitalName: 'Hospital Name',
            hospitalGBCode: 'Hospital Code',
            hospitalMDMCode: 'Hospital MDMCode'
        },
        receiverType: {
            nameCn: 'Name',
            nameEn: 'English Name',
            shortName: 'Short Name',
            canbeUpgrade: 'Can be Upgrade',
            IsAllowBuyDoping: 'Allowed Stimulants',
            chooseProductSettingOfsuspect: 'Selection of suspected - ',
            receiverTypeGroup: 'Receiver Type Group',
            validReceiverTypeGroupRequired: 'Required',
            receiverTypeGroupAndType: 'Receiver Type Group/Receiver Type',
            isRetail: 'IsRetail'
        },
        receiver: {
            // 收货方管理
            receiverType: 'Receiver Type',
            distributorReceiverType: 'Distributor Receiver Type',
            isStop: 'Status',
            distributorType: 'Distributor Type',
            receiverNameOrFormerName: 'Receiver Name',
            receiverFormerName: 'Receiver Former Name',
            GBCode: 'Code',
            externalCode: '外部Code',
            stopTime: 'Disable Time',
            restartTime: 'Re-enabled Time',
            receiverName: 'Receiver Name',
            validNameRequired: 'Required',
            validTypeRequired: 'Required',
            validNameLength: 'Receiver name is too long.',
            validGBCodeLength: 'Code is too long.',
            receiverlocation: 'Receiver Province/City/Contry',
            distributorlocation: 'Distributor Province/City/Contry',
            receiverProvinceCity: 'Receiver Province/City',
            distributorProvinceCity: 'Distributor Province/City',
            senderName: 'Distributor',
            senderProvinceName: 'Distributor Province',
            receiverProvinceName: 'Receiver Province',
            receiverAnotherName: 'Receiver Alias',
            addReceiver: 'Success',
            editReceiver: 'Success',
            notEqual: 'The receiver name and the receiver alias must not be the same.',
            notNull: 'Shipper name and ship-to party name cannot be empty',
            drugstoreTag: 'Drugstore Tag',
            // 收货方日志
            operationType: 'Operation Type',
            effectiveTime: 'Confirm Time',
            changeColumn: 'Column',
            beforeChange: 'Before Change',
            afterChange: 'After Change',
            operationer: 'Operator',
            operationTime: 'Operation time',
            confirmationer: 'Approver',
            createTime: 'CreateTime',
            creator: 'Creator',
            finalEditor: 'FinalEditor',
            finalEditTime: 'FinalEditTime',
            addReceiverManagement: 'Add',
            updateReceiverManagement: 'Edit',
            // 选择负责人
            selectHead: 'Choose Representative',
            // 选择上级单位
            selectParentName: 'Choose Parent',
            validGBCodeRequired: 'Code can not be empty',
            senderNameBeEmpty: 'Required',
            receiverAnotherNameBeEmpty: 'Required',
            stopReceiverListShow: 'Change Terminal Target',
            crmReceiverMergeTitle: 'CRM机构合并',
            targetTerminalName: 'Target Terminal',
            targetTerminalNameNotEmpty: 'Target Terminal not be empty',
            isTargetHospital: 'Target hospital',
            disable: 'Disable',
            enable: 'Enable',
            enumEntityStatus: 'There is flow data and need to transform target terminal.',
            importTime: 'Import Time',
            templateDownload: 'Download template',
            deactivateTerminal: 'Disable Terminal',
            mergeTerminal: '合并机构',
            targetTerminal: 'Target Terminal',
            distributorName: 'Distributor Name',
            pHCodeBeEmpty: 'Target terminal Code can not be empty.',
            pHCodeNotEnabled: 'Target terminal not enabled.',
            displayDefaultAnotherName: 'Display default alias',
            successfulInitiationOfReceiver: 'Successful Initiation of Receiver',
            successfulDiscontinuationOfConsignee: 'Successful discontinuation of consignee',
            targetTerminalSaveSuccess: 'Successfully saved',
            targetTerminalGBCode: 'Target Terminal Code',
            parentName: 'Parent Name',
            chooseIsGXPMessage: 'Please add purchase contactor information first',
            synchronousSuccess: 'Synchronous Success',
            targetHospitalName: 'Target Hospital Name',
            targetHospitalProvinceCity: 'Target Hospital Province/City',
            targetHospitalGBCode: 'Code',
            targetHospitalVolume: 'Volume',
            targetHospitalLog: 'Synchronized Log of Target Hospital',
            mdmValue: 'MDM',
            cdmsValue: 'CDMS',
            mdmName: 'MDM Name',
            cdmsName: 'CDMS Name',
            cdmsProvince: 'CDMS Province',
            cdmsCity: 'CDMS City',
            cdmsCounty: 'CDMS County',
            cdmsReceiverType: 'CDMS ReceiverType',
            cdmsGBCode: 'CDMS Code',
            gapCount: 'Gap Count',
            gapType: 'Gap Type',
            lastModifyDate: 'Last Modify Date',
            mdmCode: 'MDM Code',
            hasTerminalCompensationPeriod: 'There is a terminal compensation period that does not expire in the current terminal. It is necessary to shorten the end time of the terminal compensation period before the terminal can stop。',
            reveiverAnotherModifyAlter: 'Note: after changing the recipient alias, the recipient information in the flow will only be updated. The type of sales in the flow will not be updated. The relevant flow should be manually checked.',
            mdmCodeNoSpace: 'MDMCode',
            grade: 'Grade',
            longitude: 'Longitude',
            latitude: 'Latitude',
            mdmSyncTime: 'MDM Sync Time',
            receiverHasAliasChangeRequestForm: 'There are alias change application documents in the system of the terminal under approval, so the terminal cannot be deactivated.',
            remark: 'Remark',
            currentReceiver: 'Current Receiver',
            changeReceiver: 'Change Receiver',
            receiverAnotherRequest: 'ReceiverAnother Request',
            uploadFormatError: 'Error uploading file format.',
            uploadMaxSizeError: 'The maximum upload limit is exceeded. The file cannot exceed 20M.',
            uploadError: 'There was an error when uploading the data. Please contact the administrator.',
            reveiverAnotherAttachmentAlter: 'Only one attachment can be uploaded, and multiple files can be compressed. Only JPG, PDF, zip, RAR and MSG format files are supported. The file size is no more than 20m ',
            validateGradeLength: 'Grade is too long',
            validateLongitudeLength: 'Longitude is too long',
            validateLatitudeLength: 'Latitude is too long',
            validBusinessModeRequired: 'Required',
            address: 'Address',
            telephone: 'Telephone',
            o2o: 'O2O',
            businessDistrict: 'Business District',
            medicalInsurance: 'Medical Insurance',
            hospitalSide: 'Hospital Side',
            community: 'Community',
            validatCommunity: 'Please enter a positive integer',
            batchChangeByBusinessLicenseNo: 'BatchChangeByBusinessLicenseNo',
            gaudeLongitude: 'Gaude Longitude',
            gaudeLatitude: 'Gaude Latitude',
            mergeReceiver: 'Merge Receiver',
            receiverInfo: 'Receiver Information',
            mergeeceiverProvince: 'Merge Receiver Province'
        },
        receiverAliasChangeRequestForm: {
            originalReceiverName: 'Original Receiver Name',
            originalReceiverGBCode: 'Original Receiver Code',
            targetReceiverName: 'Target Receiver Name',
            receiverAnotherName: 'Alias',
            requestNO: 'Request NO',
            applyUserName: 'Apply User',
            applyTime: 'Apply Time',
            formStatus: 'Form Status',
            distributorName: 'Distributor Name',
            remark: 'Remark',
            approvalUserName: 'Approval User',
            approvalTime: 'Approval Time',
            approvalOpinion: 'Opinion',
            attachment: 'Attachment',
            confirmApproval: 'After the receiver alias is modified, only the receiver in the flow direction will be updated, and the sales type will not be updated. The relevant flow direction needs to be checked manually. Are you sure to adjust?',
            rejectApproval: 'Are you sure you want to reject?'
        },
        targetReceiver: {
            department: '部门',
            receiverName: '客户名称',
            customerCategory: '客户类型',
            productName: '产品',
            materialGroupName: '分型',
            startDate: '开始日期',
            endDate: '截止日期',
            hospitalClass: '医院级别',
            hospitalClassWithoutCOE: '医院级别不含COE',
            strategyHospital: '战略医院',
            hCOLevel: 'HCO Level',
            mSLCover: 'MSL Cover',
            cNRIDDSite: 'CNRIDD Site',
            vumeritySite: 'Vumerity Site',
            tecPMS: 'Tec PMS',
            editTitle: '编辑目标客户',
            addTitle: '新增目标客户',
            productAndMaterialGroup: '产品/分型',
            departmentValid: '请选择部门',
            receiverNameValid: '请选择客户名称',
            productAndMaterialGroupValid: '请选择产品/分型',
            customerCategoryValid: '请选择客户类型',
            startDateValid: '请选择起始日期',
            endDateValid: '请选择截止日期',
            dateComparison: '截止日期不能早于开始日期',
            region: '大区',
            commerceOrganization: '岗位',
            employeeName: '负责人',
            importantCustomerType: '重要客户类型',
            projectName: '项目名称',
            projectValid: '请填写项目名称',
            editProjectTitle: '启用项目',
            isDisplayOnQuery: '是否显示查询',
            IsEnable: '是否启用',
            mustEditName:'必须修改项目名称',
            maxNameLengthError:'项目名称不能超过十个字符',
            projectAddTitle:'新增客户分级',
            projectEditTitle:'编辑客户分级',
            deleteLevelConfirm:'删除将同时删除项目关系，是否确认？',
            projectClass:'项目值'
        },
        crmData: {
            name: '机构名称',
            firstType: '一级类型',
            secondType: '二级类型',
            thirdType: '三级类型',
            provinceName: '省份',
            cityName: '城市',
            countyName: '区县',
            address: '地址',
            level: '等次',
            grade: '等级',
            approvalTitle: '审核机构',
            receiverType: '机构类型',
            validReceiverTypeRequired: '机构类型不能为空',
            validNameRequired: '机构名称不能为',
            validNameLength: '机构名称过长'
        },
        procurement: {
            hospital: '医院名称',
            productName: '产品',
            materialGroupName: '分型',
            startDate: '开始日期',
            endDate: '截止日期',
            procurementType: '进药类型',
            productAndMaterialGroup: '产品/分型',
            region: '大区',
            commerceOrganization: '岗位',
            employeeName: '负责人',
            whetherDeleteProcurement: '是否删除该医院进药数据?',
            addTitle: '新增医院进药',
            editTitle: '编辑医院进药',
            hospitalIsNotNull: '医院不可为空',
            procurementTypeIsNotNull: '进药类型不可为空',
            startDateIsNotNull: '起始时间不可为空',
            endDateIsNotNull: '截止时间不可为空'
        },
        drugstoreOfHospital: {
            addTitle:'新增院外药店',
            editTitle:'编辑院外药店',
            selHospitalTitle:'选择医院',
            selDrugStoreTitle:'选择药店',
            monthly: '月份',
            beginMonth: '起始月份',
            endMonth: '结束月份',
            hospitalName: '医院名称',
            hospitalCode: '医院编码',
            province: '省份',
            drugstoreName: '药店名称',
            drugstoreCode: '药店编码',
            productName: '产品名称',
            materialGroupName: '规格',
            productAndMaterialGroup: '产品/分型',
            hospitalStoreType: '关联类型',
            hospitalNameValid:'医院不能为空',
            drugStoreNameValid:'药店不能为空',
            productAndMaterialGroupValid:'产品/规格不能为空',
            beginMonthValid:'起始月份不能为空',
            endMonthValid:'结束月份不能为空',
            beginMonthGreater:'起始月份不能大于结束月份',
            endMonthLess:'结束月份不能小于起始月份',
            hospitalStoreTypeValid:'关联类型必选',
            confirmDelete:'确定删除院外医院'
        }
    }
}
export default Receiver
