<!--产品简称弹框  作者：程瑞杰 -->
<template>
  <div style="width: 100%" class="ivuInput">
    <Poptip
      placement="bottom-start"
      @on-popper-hide="allMateriallBlur"
      trigger="hover"
    >
      <Input
        v-model="addressIndex"
        readonly
        :placeholder="$t('public.productReferred')"
        @on-focus="allMateriallFocus"
      ></Input>
      <div slot="content" class="CheckboxGroupTemplate">
        <div class="CheckboxGroupHide" style="white-space: pre-wrap">
          <ul
            v-for="(todo, index) in queryProductReferredCascaderAttr"
            :key="todo.value"
          >
            <li>
              <div class="left">
                <Checkbox
                  :indeterminate="todo.indeterminate"
                  :value="todo.selectedAll"
                  @click.prevent.native="
                    handleCheckAll(index, queryProductReferredCascaderAttr)
                  "
                  ><b>{{ todo.label }}</b></Checkbox
                >
              </div>
              <div class="right">
                <CheckboxGroup
                  v-model="todo.selectedChildren"
                  @on-change="
                    checkAllGroupChange(
                      index,
                      todo.selectedChildren,
                      queryProductReferredCascaderAttr
                    )
                  "
                >
                  <div
                    v-for="itemOne in todo.children"
                    class="leftAndRightOne"
                    :key="itemOne.value"
                  >
                    <div class="leftOne">{{ itemOne.label }}</div>
                    <div class="rightOne">
                      <Checkbox
                        v-for="itemTwo in itemOne.children"
                        :key="itemTwo.value"
                        :label="itemTwo.value"
                        >{{ itemTwo.label }}</Checkbox
                      >
                    </div>
                  </div>
                </CheckboxGroup>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </Poptip>
  </div>
</template>
<script>
    export default {
        props: ['needDataAuthority'],
        data () {
            return {
                addressAttrHide: false,
                addressAttr: [],
                queryProductReferredCascaderAttr: [],
                productLevelthree: [],
                checkAllValue: []
            };
        },
        created () {
            this.initAllMateriall()
        },
        methods: {
            // 区域--全选
            handleCheckAll (index, dataAll) {
                let childrenAll = [];
                dataAll[index].children.forEach(item => {
                    item.children.forEach(itemOne => {
                        childrenAll.push(itemOne.value);
                    })
                });
                if (dataAll[index].indeterminate) {
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].selectedAll = !dataAll[index].selectedAll;
                }
                dataAll[index].indeterminate = false;
                if (dataAll[index].selectedAll) {
                    dataAll[index].selectedChildren = childrenAll;
                    childrenAll.forEach(val => {
                        this.checkAllValue.push(val)
                    })
                    this.addressAttr = this.productLevelthree.filter(filter => {
                        return this.checkAllValue.findIndex(find => {
                            return find == filter.value
                        }) != -1
                    }).map(item => { return item.label });
                } else {
                    dataAll[index].selectedChildren = [];
                    let c = new Set(childrenAll);
                    let d = new Set(this.checkAllValue);
                    this.checkAllValue = [...new Set([...d].filter(x => !c.has(x)))];

                    this.addressAttr = this.productLevelthree.filter(filter => {
                        return this.checkAllValue.findIndex(find => {
                            return find == filter.value
                        }) != -1
                    }).map(item => { return item.label });
                }
            },
            // 区域--change
            checkAllGroupChange (index, data, dataAll) {
                let selectedChildrenData = [];
                dataAll.forEach(val => {
                    if (val.selectedChildren.length > 0) {
                        val.selectedChildren.forEach(value => {
                            selectedChildrenData.push(value);
                        })
                    }
                })
                this.checkAllValue = selectedChildrenData;
                this.addressAttr = this.productLevelthree.filter(filter => {
                    return selectedChildrenData.findIndex(find => {
                        return find == filter.value
                    }) != -1
                }).map(item => { return item.label });
                let childrenAll = [];
                dataAll[index].children.forEach(item => {
                    item.children.forEach(itemOne => {
                        childrenAll.push(itemOne.label);
                    })
                });
                if (data.length == childrenAll.length) {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = true;
                } else if (data.length > 0) {
                    dataAll[index].indeterminate = true;
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = false;
                }
            },
            initAllMateriall () {
                sessionStorage.demoSpinIconLoad = 'false';
                if (this.needDataAuthority == undefined || this.needDataAuthority) {
                    this.$http
                        .get('/Product/QueryProductReferredCascader')
                        .then(response => {
                            let dataAll = response.data;
                            response.data.forEach(one => {
                                if (one.children.length > 0) {
                                    one.children.forEach(two => {
                                        if (two.children.length > 0) {
                                            two.children.forEach(three => {
                                                this.productLevelthree.push(three);
                                            })
                                        }
                                    })
                                }
                            })
                            response.data.forEach(element => {
                                element.selectedAll = false;
                                element.indeterminate = false;
                                element.selectedChildren = [];
                            })
                            this.queryProductReferredCascaderAttr = dataAll;
                    });
                } else {
                    this.$http
                        .get('/Product/QueryProductReferredCascaderWithoutDataAuthority')
                        .then(response => {
                            let dataAll = response.data;
                            response.data.forEach(one => {
                                if (one.children.length > 0) {
                                    one.children.forEach(two => {
                                        if (two.children.length > 0) {
                                            two.children.forEach(three => {
                                                this.productLevelthree.push(three);
                                            })
                                        }
                                    })
                                }
                            })
                            response.data.forEach(element => {
                                element.selectedAll = false;
                                element.indeterminate = false;
                                element.selectedChildren = [];
                            })
                            this.queryProductReferredCascaderAttr = dataAll;
                    });
                }
            },
            // 得到焦点
            allMateriallFocus () {
                this.$emit('input', '');
                this.addressAttr = [];
                this.checkAllValue = [];
                this.queryProductReferredCascaderAttr.forEach(element => {
                    element.selectedAll = false;
                    element.indeterminate = false;
                    element.selectedChildren = [];
                })
                this.addressAttrHide = true;
            },
            allMateriallBlur () {
                this.$emit('input', this.checkAllValue);
                this.$emit('onProductsAndTyping', this.checkAllValue)
            }
        },
        computed: {
            addressIndex: function () {
                return this.addressAttr.join(',');
            }
        }
    };
</script>
<style>
.ivu-poptip-rel {
  width: 100% !important;
}
</style>

<style scoped>
.CheckboxGroupTemplate {
  max-height: 300px;
  overflow: auto;
}
.CheckboxGroupTemplate::-webkit-scrollbar {
  width: 0 !important;
}

.CheckboxGroupHide {
  width: 400px;
  background: #fff;
}
.CheckboxGroupHide ul {
  margin-top: 5px;
  margin-bottom: 5px;
  clear: both;
}
.CheckboxGroupHide ul li {
  clear: both;
  list-style-type: none;
  border-bottom: 1px solid #dddee1;
  padding-bottom: 5px;
}
.CheckboxGroupHide ul li .left {
  width: 70px;
  display: inline-block;
  vertical-align: top;
}
.CheckboxGroupHide ul li .right {
  display: inline-block;
  width: 325px;
  word-wrap: break-word;
}
.CheckboxGroupHide ul:last-child li {
  border-bottom: none;
}
.addressStyle {
  float: right;
  margin-top: 5px;
  position: absolute;
  right: 20px;
  top: 3px;
  color: #666;
  cursor: pointer;
}
.leftAndRightOne {
  border-bottom: 1px solid #dddee1;
}
.leftAndRightOne:last-child {
  border-bottom: none;
}
.leftOne {
  display: inline-block;
  width: 55px;
  word-wrap: break-word;
  vertical-align: top;
}
.rightOne {
  display: inline-block;
  width: 255px;
  word-wrap: break-word;
}
.ivu-poptip-body {
  padding-right: 0 !important;
}
div.ivu-poptip {
  width: 100% !important;
}
</style>
