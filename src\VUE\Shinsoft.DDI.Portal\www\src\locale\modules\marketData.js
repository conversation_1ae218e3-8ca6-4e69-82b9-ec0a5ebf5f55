// 市场数据
export const marketData = {
    'zhCN': {
        cPASaleData: {
            province: '省',
            city: '城市',
            year: '年',
            month: '月',
            quarter: '季度',
            cPAHospitalCode: 'CPA医院编码',
            cPAHospitalName: 'CPA医院名称',
            sPCode: 'SPCode',
            aTCCode: 'ATCCode',
            productName: '产品名称',
            brand: '品牌',
            quantity: '包装单位数量',
            adjustedQuantity: '调整后数量',
            spec: '规格',
            packageQunatity: '包装数量',
            price: '金额（元）',
            quantityOfMinSpec: '最小规格数量（支/片）',
            dosageForm: '剂型',
            channel: '途径',
            manufacturer: '生产企业',
            isTECsTarget: 'TECs目标医院',
            hospitalClass: 'Hospital Class',
            importTime: '导入时间',
            importFile: '导入文件',
            chooseBU: 'BU',
            buVaild: '请先选择BU',
            hospitalName: '医院名称',
            editTitle: '编辑CPA数据',
            salesFlowCycleMonth: '记录月份',
            quarterMaximum: '季度不能大于4',
            confirmDelete: '确认删除？'
        }

    },
    'enUS': {
        cPASaleData: {
            province: '省',
            city: '城市',
            year: '年',
            month: '月',
            quarter: '季度',
            cPAHospitalCode: 'CPA医院编码',
            cPAHospitalName: 'CPA医院名称',
            sPCode: 'SPCode',
            aTCCode: 'ATCCode',
            productName: '产品名称',
            brand: '品牌',
            quantity: '包装单位数量',
            adjustedQuantity: '调整后数量',
            spec: '规格',
            packageQunatity: '包装数量',
            price: '金额（元）',
            quantityOfMinSpec: '最小规格数量（支/片）',
            dosageForm: '剂型',
            channel: '途径',
            manufacturer: '生产企业',
            isTECsTarget: 'TECs目标医院',
            hospitalClass: 'Hospital Class',
            importTime: '导入时间',
            importFile: '导入文件',
            chooseBU: 'BU',
            buVaild: '请先选择BU',
            hospitalName: '医院名称',
            editTitle: '编辑CPA数据',
            salesFlowCycleMonth: '记录月份',
            confirmDelete: '确认删除？'

        }

    }
}
export default marketData
