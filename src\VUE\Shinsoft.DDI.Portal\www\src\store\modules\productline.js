import http from '../../utils/axios'
const productLine = {
    state: {
        productLineAllList: [],
        productLineSelectList: [],
        productLineModel: {}
    },
    mutations: {
        InitStateProductLineAll (state, data) {
            state.productLineAllList = data
        },
        InitProductLineSelect (state, data) {
            state.productLineSelectList = data
        },
        InitProductLineModel (state, data) {
            state.productLineModel = data
        }
    },
    actions: {
        queryAllProductLineAction ({
            commit
        }) {
            http.get('/Product/QueryAllProductLine').then(function (response) {
                commit('InitStateProductLineAll', response.data)
            })
        },
        queryProductLineSelectAction ({
            commit
        }) {
            http.get('/Product/QueryProductLineSelect').then(function (response) {
                commit('InitProductLineSelect', response.data)
            })
        },
        queryProductLineSelectActionByBuId ({
            commit
        }, params) {
            http.get('/Product/QueryProductLineSelectByBUId', {
                params: params
            }).then(function (response) {
                commit('InitProductLineSelect', response.data)
            })
        },
        getProductLineAction ({
            commit
        }, productLineID) {
            http.get('/Product/GetProductLine', {
                params: {
                    'productLineID': productLineID
                }
            }).then(function (response) {
                commit('InitProductLineModel', response.data)
            })
        }
    }
}
export default productLine
