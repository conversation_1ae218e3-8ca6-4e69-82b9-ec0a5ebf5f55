import http from './../utils/axios'
import { ElMessage } from 'element-plus'
export function DownloadFile (filePath) {
    if (filePath === undefined || filePath == null || filePath.trim() === '') {
        return
    }
    let path = filePath.split('/')
    http.post('/Attachment/DownloadFile', {
        filePath: filePath
    }, {
        responseType: 'arraybuffer'
    }).then(response => {
        let fileDownload = require('js-file-download')
        let fileName = path[path.length - 1]
        fileDownload(response.data, fileName)
    })
}
export function DownloadFileByID (id, name) {
    http.post('/Attachment/DownloadFileByID', {
        AttachmentID: id
    }, {
        responseType: 'arraybuffer'
    }).then(response => {
        let fileDownload = require('js-file-download')
        fileDownload(response.data, name)
    })
}

export function DownloadAppointName (filePath, fileName) {
    if (filePath === undefined || filePath == null || filePath.trim() === '') {
        return
    }
    http.post('/Attachment/DownloadFile', {
        filePath: filePath
    }, {
        responseType: 'arraybuffer'
    }).then(response => {
        let fileDownload = require('js-file-download')
        fileDownload(response.data, fileName)
    })
}
export function InventoryDay (InventoryDay) {
    return parseInt(InventoryDay) === 999999 ? '∞' : InventoryDay
}
// 删除 -- 表格分页
export function handlePage (page, per, totalCount) {
    let pageNumber = page
    if (page > 1) {
        pageNumber = totalCount % per === 1 ? page - 1 : page
    }
    return pageNumber
}
// 附件上传返回错误不走统一拦截处理
export function handleUploadErrorPublic (file, uploadError) {
    let fileAll = ''
    let fileToString = file
    if (typeof file === 'object') {
        fileToString = JSON.stringify(file)
    }
    if (fileToString.indexOf('"Message":') !== -1) {
        if (typeof file === 'object') {
            fileAll = file.Message
        } else {
            fileAll = JSON.parse(file).Message
        }
    } else {
        fileAll = uploadError
    }
    ElMessage.error({
        message: fileAll,
        duration: 5000
    })
}
export function deepCopy (obj) {
    if (obj == null || typeof obj !== 'object') return obj;

    // Handle Date
    if (obj instanceof Date) {
        var copy = new Date();
        copy.setTime(obj.getTime());
        return copy;
    }

    // Handle Array
    if (obj instanceof Array) {
        let copy = [];
        for (var i = 0, len = obj.length; i < len; ++i) {
            copy[i] = deepCopy(obj[i]);
        }
        return copy;
    }

    // Handle Object
    if (obj instanceof Object) {
        let copy = {};
        for (var attr in obj) {
            if (obj.hasOwnProperty(attr)) copy[attr] = deepCopy(obj[attr]);
        }
        return copy;
    }

    throw new Error("Unable to copy obj! Its type isn't supported.");
}

export default {
    DownloadFile,
    DownloadFileByID,
    DownloadAppointName,
    InventoryDay,
    handlePage,
    handleUploadErrorPublic,
    deepCopy
}
