export const noticeLocale = {
    'zhCN': {
        notice: {
            title: '公告标题',
            isRelease: '是否发布',
            creatorName: '创建人',
            createTime: '创建时间',
            finalEditorName: '最后修改人',
            finalEditTime: '最后修改时间',
            whetherRelease: '确认要发布当前公告吗？',
            whetherCancel: '确认要取消发布当前公告吗？',
            validateContentLength: '内容过长',
            customerNoticeRelease: '信息发布',
            description: '描述',
            diagram: '简图',
            enclosure: '附件',
            content: '内容',
            customerNoticeTitleNull: '消息标题不能为空',
            customerNoticeTitleOverLength: '消息标题过长',
            fileSizeExceed200K: '图片大小不能超过200K',
            uploadDiagramFormatError: '仅支持图片jpg格式的文件',
            descriptionOverLength: '描述过长'
        }
    },
    'enUS': {
        notice: {
            title: 'Title',
            isRelease: 'Has it been released',
            creatorName: 'Creator',
            createTime: 'Create Time',
            finalEditorName: 'Final Editor',
            finalEditTime: 'Final Edit Time',
            whetherRelease: 'Are you sure you need to publish it?',
            whetherCancel: 'Are you sure you want to cancel publishing it?',
            validateContentLength: 'Content is too long',
            customerNoticeRelease: 'Customer Notice Release',
            description: 'Description',
            diagram: 'Diagram',
            enclosure: 'Enclosure',
            content: 'Content',
            customerNoticeTitleNull: 'Required',
            customerNoticeTitleOverLength: 'Notice is too long',
            fileSizeExceed200K: 'The image size cannot exceed 200K',
            uploadDiagramFormatError: 'Only JPG format files are supported',
            descriptionOverLength: 'Description is too long'
        }
    }
}
export default noticeLocale
