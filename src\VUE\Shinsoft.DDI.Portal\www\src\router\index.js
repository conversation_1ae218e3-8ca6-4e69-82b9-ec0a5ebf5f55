// 路由配置
import { createRouter, createWebHashHistory } from 'vue-router'
import { routers } from './router'

// 路由配置
const router = createRouter({
    history: createWebHashHistory(),
    routes: routers
})

function loadStorage (to) {
    sessionStorage.route = to.path
    let allProvinceCity = localStorage.allProvinceCity !== undefined ? JSON.parse(localStorage.allProvinceCity) : [];
    let allProvinceCityCounty = localStorage.allProvinceCityCounty !== undefined ? JSON.parse(localStorage.allProvinceCityCounty) : [];
    let allrouteProvince = localStorage.routeProvince !== undefined ? JSON.parse(localStorage.routeProvince) : [];

    let province = []
    allrouteProvince.forEach(function (value, index, array) {
        if (value.RouteName === to.path) {
            let p = {}
            p.label = value.ProvinceName
            p.value = value.ProvinceID
            let index = province.findIndex(item => { return item.value === value.ProvinceID })
            if (index === -1) {
                province.push(p)
            }
        }
    })

    let provinceCity = []
    allProvinceCity.forEach(function (value, index, array) {
        if (allrouteProvince.findIndex(item => item.RouteName === to.path && item.ProvinceID === value.value) > -1) {
            provinceCity.push(value)
        }
    })

    let provinceCityCounty = []
    allProvinceCityCounty.forEach(function (value, index, array) {
        if (allrouteProvince.findIndex(item => item.RouteName === to.path && item.ProvinceID === value.value) > -1) {
            provinceCityCounty.push(value)
        }
    })

    sessionStorage.provinceCity = provinceCity.length > 0 ? JSON.stringify(provinceCity) : null
    sessionStorage.provinceCityCounty = provinceCityCounty.length > 0 ? JSON.stringify(provinceCityCounty) : null
    sessionStorage.province = province.length > 0 ? JSON.stringify(province) : null
}

// TODO: 临时注释掉权限控制，用于开发测试阶段
// 正式环境需要恢复权限控制以确保系统安全
router.beforeEach((to, from, next) => {
    if (to.matched.some(m => m.meta.requireAuth)) {
        // 临时跳过权限验证，直接允许访问所有需要认证的页面
        // 原权限控制逻辑已注释，开发完成后需要恢复
        /*
        let jurisdiction = false
        if (localStorage.routeAll) {
            localStorage.routeAll.split(',').forEach(function (value, index, array) {
                if (to.path === value) {
                    jurisdiction = true
                    return false
                }
            })
        }
        if (to.path === '/login') {
            jurisdiction = true
        } else if (to.path === '/home/<USER>') {
            jurisdiction = true
        } else if (to.path === '/') {
            jurisdiction = true
        }
        if (localStorage.tokenStorage === 'tokenStorage') {
            if (jurisdiction) {
                loadStorage(to)
                next()
            } else {
                if (to.fullPath === '/403') {
                    next()
                } else {
                    next('/403')  // 这里会跳转到403页面
                }
            }
        } else {
            if (to.fullPath === '/login') {
                next()
            } else
                if (to.path === '/login' && to.query.code !== undefined) {
                    next()
                } else {
                    next('/login')
                }
        }
        */

        // 临时解决方案：直接加载存储并允许访问
        // 跳过权限检查，允许访问所有页面
        loadStorage(to)
        next()
    } else {
        // 对于不需要认证的页面，保持原有逻辑
        if (to.fullPath === '/404' || to.fullPath === '/loginJump') {
            next()
        } else {
            next('/404')
        }
    }
})
router.afterEach((to, from, next) => {
    // return next()
})

export default router
