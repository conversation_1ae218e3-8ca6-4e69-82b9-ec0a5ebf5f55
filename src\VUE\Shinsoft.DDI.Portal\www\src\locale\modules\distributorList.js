/* eslint-disable no-trailing-spaces */
// 流向商业-->中英文
export const distributor = {
    'zhCN': {
        distriButorSystem: {
            distributorName: '流向商业名称',
            modifyDistributor: '修改流向商业',
            upgradeDistributor: '升级为流向商业',
            upstreamDistributor: '上游流向商业',
            preparatoryName: '可追溯商业名称',
            modifyPreparatory: '修改可追溯商业',
            upgradePreparatory: '升级为可追溯商业',
            status: '状态',
            receiverName: '收货方名称',
            areYouSplittingIntoPoints: '你是否拆分为分'
        },
        distributorAll: {
            deliveryName: '发货方名称',
            productReferred: '产品',
            productMaterialGroupName: '分型',
            productMaterialGroup: '产品规格/分型',
            upstreamName: '上游',
            GBCode: 'Code',
            isTierOne: '是否Tier1',
            primaryUpstreamName: '原上游',
            isEverDistributor: '曾有流向',
            distributorType: '商业公司类型',
        
            isDiscontinuation: '是否停用',
            principal: '负责人',
            distributorprincipal: '发货方负责人',
            addDistributorList: '新增流向商业',
            exportHistoricalBusinessFlow: '导出历史流向商业名单',
            cycleOfHistoricalFlowToBusiness: '历史流向商业周期：',
            addDistributorListTrace: '新增可追溯商业',
            receiverType: '收货方类型',
            receiverTypeLevelOne: '一级类型',
            receiverTypeLevelTwo: '二级类型',
            receiverTypeLevelThree: '三级类型',
            distributorTypeDesc: '商业公司类型',
            distributor: '流向商业',
            distributorListTrace: '可追溯商业',
            receiver: '收货方商业',
            contractorModal: '联系人管理',
            upgradeDistributor: '升级为流向商业',
            upgradeDistributorListTrace: '升级为可追溯商业',
            downgradeDistributorListTrace: '降级为可追溯商业',
            cancelDistributor: '取消流向商业',
            cancelPreparatoryDistributor: '取消可追溯商业',
            setBidding: '设置为投标主体',
            cancelBidding: '取消投标主体设置',
            biddingType: '招标类型',
            formType: '业务类型',
            submitTime: '提交日期',
            submitUser: '提交人',
            dutyID: '业务领域',
            name: '姓名',
            finalEditTime: '修改时间',
            finalEditDate: '修改日期',
            telephone: '电话',
            phone: '电话',
            phoneNumber: '手机',
            eMail: '邮件',
            addressField: '注册地址',
            contactAddress: '联系地址',
            isPrimaryContactDesc: '主要联系人',
            finalEditor: '修改人',
            addDistributorListApply: '新增流向商业申请',
            addDistributorListTraceApply: '新增可追溯商业申请',
            upgradeDistributorListApply: '升级成流向商业申请',
            upgradeDistributorListTraceApply: '升级成可追溯商业申请',
            downDistributorListTraceApply: '降级成可追溯商业申请',
            editDistributorList: '编辑流向商业',
            editTraceable: '编辑可追溯商业',
            editReceiver: '编辑收货方',
            childBasic: '基本信息',
            childBasicError: '基本信息错误',
            childExpansion: '扩展信息',
            childExpansionError: '扩展信息错误',
            childRequestContractor: '联系人',
            childRequestContractorError: '联系人错误',
            childRequestProductChannel: '产品渠道',
            childRequestProductChannelError: '产品渠道错误',
            flowEffectiveDateIncorrect: '流向生效日期错误',
            flowEffectiveDateRequired: '流向生效日期为必填项',
            medicineGroupID: '所属医药集团',
            businessModeID: '经营方式',
            businessStatusID: '经营状态',
            businessCategory: '业务分类',
            hospitalGrade: '医院等级',
            hospitalLevel: '医院等次',
            businessCategoryLength: '',
            dsitributorBusinessCategory: '发货方业务分类',
            receiverBusinessCategory: '收货方业务分类',
            editType: '变更类型',
            // tierTypeID: '流向商业管理级别',
            downstreamPropertyID: '性质名称',
            parentName: '上级单位',
            parentHospital: '所属总院',
            ddiStatus: 'DDI状态',
            taxRegistrationNo: '社会统一信用代码',
            fax: '传真',
            postalCode: '邮编',
            netAddress: '网址',
            eleEMail: '电子邮箱',
            bankName: '开户银行',
            debitCardNumber: '开户账号',
            other1: '其他1',
            other2: '其他2',
            other3: '其他3',
            deliveryAddress: '收货地址',
            deliveryAddressCode: 'Ship-toCode',
            validNameRequired: '流向商业名称不能为空',
            businessCompanyNameNull: '商业公司名称不能为空',
            businessModeIDNull: '经营方式不能为空',
            standardReceiver: '标准收货方不能为空',
            originalReceiver: '原始收货方不能为空',
            validateTelephoneNull: '电话不能为空',
            validateEmail: '邮箱不能为空',
            validateAddress: '地址不能为空',
            batchNumber: '批号不能为空',
            saleDate: '销售日期不能为空',
            equalReceiver: '收货方和流向商业不能相等',
            flow: '流向冲销成功！',
            validNameLength: '商业公司名称过长',
            validateTelephoneLength: '电话号码过长',
            validateFaxLength: '传真号码过长',
            validateTelephone: '请填写正确的电话号码',
            validateFax: '请填写正确的传真号码',
            validatePostalCode: '请填写正确的邮编',
            validLocationRequired: '省份/城市/区县未选择',
            validReceiverTypeRequired: '收货方类型未选择',
            validateRequiredSAPCode: 'SAPcode不能为空',
            validSAPcodeLength: 'SAPcode长度过长',
            beforeSAPCode: '曾用SAPCode',
            validBeforeSAPCode: '曾用SAPCode长度过长',
            validBeforeSAPCodeSix: '曾用SAPCode长度不能少于6位',
            beingSAPCode: 'SAPCode',
            validBeingSAPCode: 'SAPCode长度过长',
            validBeingSAPCodeSix: 'SAPCode长度不能少于6位',
            validTaxRegistrationNoLength: '社会统一信用代码长度过长',
            validShortNameLength: '客户简称长度过长',
            netAddressTrue: '请填写正确的网址',
            validAddressLength: '地址长度过长',
            validBankNameLength: '开户银行长度过长',
            validDebitCardNumberLength: '开户账号长度过长',
            validOther1Length: '其他1长度过长',
            validOther2Length: '其他2长度过长',
            validDeliveryAddressLength: '收货地址长度过长',
            validRemarkLength: '备注长度过长',
            newAddress: '请填写完成后再点击新增',
            samaAddress: '已存在相同地址，请勿重复添加',
            samaAddressCode: '已存在相同Ship-toCode，请勿重复添加',
            validatePhoneNumber: '请填写正确的手机号码',
            distributorNull: '流向商业不能为空',
            nameNull: '姓名不能为空',
            nameLength: '姓名过长',
            eMailTrue: '请填写正确的email',
            dutyIDNull: '业务领域不能为空',
            effectiveDate: '流向生效月份',
            inventoryDate: '库存初始月份',
            inventoryDateData: '产品渠道生效月份',
            uploadStock: '上传库存截图',
            alterMsg: '历史月份需要联系内勤提供流向',
            alterMsgTitle: '该申请单需要全国总监进行审批',
            newProductChannel: '新增产品渠道',
            addDistributorChannel: '新增流向商业渠道',
            addPreparatoryDistributorChannel: '新增可追溯商业渠道',
            addDistributorChannelApply: '新增流向商业渠道申请',
            addPreparatoryDistributorChannelApply: '新增可追溯商业渠道申请',
            productName: '产品',
            openingInventory: '初始库存',
            afterUpstreamName: '上游流向商业',
            addProductChannel: '新增渠道',
            cancelProductChannel: '取消渠道',
            material: '物料',
            batch: '批号',
            quantity: '库存',
            productReferredJC: '产品简称',
            productNotNull: '产品简称不可为空',
            materialGroupNotnull: '分型不可为空',
            upstreamNameNotNull: '上游商业不可为空',
            newProductChannelQuantity: '新增库存',
            openingInventoryMessage: '库存不可为空',
            sumQuantity: '输入的库存数量不正确',
            materialGroupId: '分型',
            childUpstream: '上游流向商业列表',
            materialProductBatch: '物料/批号',
            materialProductBatchNull: '物料/批号不能为空',
            wasUsedName: '曾用名',
            fatherReceiverContractor: '联系人变更',
            whetherDelBill: '是否删除该单据？',
            whetherWithdrawBill: '是否撤回该单据？',
            whetherDelEnclosure: '是否删除该附件？',
            fatherReceiverContractorSQ: '流向商业联系人变更',
            productChannelNull: '渠道不能为空',
            stopQuantity: '期初库存',
            lastInventory: '期末库存',
            stopPHQuantity: '停用批号库存',
            productChannel: '渠道',
            childChangeProductChannel: '变更产品渠道',
            receiverProductChannelID: '产品/分型',
            afterUpstreamNameNull: '上游流向商业必选项',
            receiverProductChannelIDNull: '产品/分型为必选项',
            startTime: '生效日期',
            stopTime: '停用日期',
            founder: '创建人',
            founderTime: '创建时间',
            stopProductChannel: '停用产品渠道',
            upgradeTraceable: '升级可追溯商业申请确认成功',
            upgradeFlow: '升级流向商业申请确认成功',
            forcedConfirm: '没有负责人，是否强制确认',
            gxpDistributorConfirmMessage: '该商业公司是T1商业，确认后将取消T1商业,确认？',
            submitCancelDistributorGXPMessage: '该商业公司是T1商业,是否需要取消？',
            submitDowngradeDistributorGXPMessage: '该商业公司是T1商业,是否需要降级？',
            receiverChange: '信息变更确认成功',
            degradeTraceable: '降级为可追溯商业申请确认成功',
            deleteFlow: '取消流向商业申请确认成功',
            productChannelDelete: '停用产品渠道申请确认成功',
            productChannelUpdate: '变更产品渠道申请确认成功',
            productChannelAdd: '新增产品渠道申请确认成功',
            purchasePerson: '授权采购',
            purchaseTelphone: '授权采购电话',
            purchaseEmail: '授权采购邮箱',
            validateDDI: '请选择DDI状态',
            notMyParentName: '上级单位不可以是自己。',
            parentHospitalNameIsCurrent: '所属总院不可以是当前医院。',
            contactDDI: '建议选择DDI联系人为主要联系人',
            documentYes: '已存在审批中的单据,不能继续提交',
            documentYesAndNo: '已存在审批中的单据,是否继续？',
            increaseProductChannels: '增加产品渠道',
            increaseProductChannelsDate: '增加产品渠道日期',
            changeProductChannels: '变更产品渠道',
            disableProductChannels: '停用产品渠道',
            fieldCannotBeZeroOrEmpty: '该字段不能为零或空',
            pleaseEnterAnInteger: '请输入整数',
            // 新
            receiverBeEmpty: '收货方类型不能为空',
            medicineGroupIDBeEmpty: '所属医药集团不能为空',
            tierTypeIDBeEmpty: '管理级别不能为空',
            businessModeIDBeEmpty: '经营方式不能为空',
            selcetParentName: '选择上级单位',
            channelType: '渠道类型',
            channelDistributorGBCode: 'Code',
            channelProduct: '产品',
            channelMaterialGroupName: '分型',
            channelProvinceName: '省份',
            channelCityName: '城市',
            channelStartDate: '生效日期',
            channelStopDate: '停用日期',
            inventoryData: '期末库存日期',
            channelStopMonth: '渠道停用月份',
            channelLoseEfficacyMonth: '渠道失效月份',
            stopInventory: '停用库存',
            cancelReceiverWaringMessage: '收货方不能取消',
            requestNodeNotExisten: '单据申请节点未配置人员，如果确认单据将作废。',
            channelEffectiveDate: '渠道生效日期',
            initialInventoryDate: '期初库存日期',
            submitVerificationInventoryScreenshot: '无法提交，必需上传库存截图',
            submitVerificationChannelDate: '无法提交，请选择渠道日期',
            newInventory: '请先选择产品或分型',
            deleteChannelHints: '确定删除该渠道?',
            newChannelDate: '无法新增渠道，请选择增加渠道日期',
            productChannelChangeApplication: '产品渠道变更申请',
            productChannelChangeDate: '产品渠道变更日期',
            productChannelStopDate: '产品渠道停用日期',
            netAddressLength: '网址长度过长',
            pleaseChooseProduct: '请先选择产品',
            changeOfConsigneeInformation: '收货方信息变更',
            distributorAndPHcodeNotAllEmpty: '流向商业和Code不能都为空',
            isBidding: '投标主体',
            isBiddingDowngrade: '降级后将会联动停止默认投标主体关系，是否确认提交？',
            isBiddingCancel: '取消后将会联动停止默认投标主体关系，是否确认提交？',
            onceDistributor: '曾经是流向商业',
            oncePreparatory: '曾经是可追溯流向商业',
            cancleBidding: '是否确认取消投标主体设置？',
            commerceOrganization: '组织架构',
            leaderRoleLabel: '角色标签',
            splitChannel: '拆分渠道',
            submitApplication: '提交申请',
            changeClassificationChannel: '变更分型渠道',
            selectMonth: '选择月份',
            changeTime: '变更时间',
            changeUpstream: '变更上游',
            monthIsMandatory: '月份为必选项',
            atLeastOneChannelChangeCanBeSubmitted: '至少有一个渠道变更才可以提交',
            roleTarget: '角色标签',
            salesflowCycle: '周期',
            existsCustomerAgreement: '存在多个授权采购人，是否修改客户协议？',
            confirmUploadGXP: '此操作会覆盖现有GXP商业公司信息，确认是否要进行上传？',
            confirmUploadGXPAgain: '可能会覆盖现有GXP商业公司信息，请您再次确认。',
            // 新增
            businessCompanyName: '商业公司名称',
            managementLevel: '管理级别',
            changeList: '变更列表',
            upstreamDistributorName: '上游商业名称',
            upstreamDistributorGBCode: '上游商业Code',
            flowEffectiveDate: '流向生效月份',
            productTypingNotSelected: '产品/分型分型未选择',
            channelMessage: '无法添加重复分型，请检查数据！',
            differenceType: '请选择差异类型',
            afterChangeText: '变更后',
            beforeChangeText: '变更前',
            changeColumnName: '变更字段',
            shipToCode: '地址Code',
            address: '地址',
            enumEditTypeDescription: '变更类型',
            qualificationTypeName: '资质类型',
            channelProductMessage: '无法选择产品做为渠道信息，请检查是否已添加产品或分型渠道！',
            validBusinessCategoryLength: '业务分类长度过长',
            validGBCodeLength: 'Code过长',
            validGBCodeRequired: 'Code不能为空',
            hasAliasRequest: '当前别名存在变更申请单据，不能编辑',
            hasAliasRequestCanNotSubmit: '当前别名存在变更申请单据，不能提交申请',
            biddingProjectName: '招标项目名称',
            biddingProjectStatus: '招标项目状态',
            biddingProjectNO: '招标项目编号',
            taxRegistrationNoNotNULL: '社会统一信用代码不能为空',

            addContractor: '新增联系人',
            updateContractor: '编辑联系人',
            promotionMethods: '推广方式',
            mdmCode: 'MDM Code',
            hospitalClassWithoutCOE: '市场级别不含COE',
            hospitalClass: '市场医院级别',
            strategyHospital: '战略医院',
            hcoLevel: 'HCO Level',
            mSLCover: 'MSL Cover',
            tecPMS: 'Tec PMS',
            cNRIDDSite: 'CNRIDD Site',
            vumeritySite: 'Vumerity Site'
        }
    },
    'enUS': {
        distriButorSystem: {
            distributorName: 'Distributor Name',
            modifyDistributor: 'Modify Distributor',
            upgradeDistributor: 'Upgrade Distributor',
            upstreamDistributor: 'Upstream Distributor',
            preparatoryName: 'Preparatory Name',
            modifyPreparatory: 'Modify Preparatory',
            upgradePreparatory: 'Upgrade Preparatory',
            status: 'Status',
            receiverName: 'Receiver Name',
            areYouSplittingIntoPoints: 'Are you splitting into points'
        },
        distributorAll: {
            deliveryName: 'Delivery Name',
            productReferred: 'Referred',
            productMaterialGroupName: 'Material Group',
            upstreamName: 'UpStream',
            primaryUpstreamName: 'Former Upstream',
            isEverDistributor: 'Has Salesflow',
            distributorType: 'Distributor Type',
       
            isDiscontinuation: 'Is Discontinuation',
            principal: 'Representative',
            distributorprincipal: 'Distributor Representative',
            addDistributorList: 'Add Distributor',
            exportHistoricalBusinessFlow: 'Export historical business flow list',
            cycleOfHistoricalFlowToBusiness: 'Historical flow to business list cycle',
            addDistributorListTrace: 'Add Preparatory',
            receiverType: 'Receiver Type',
            productNotNull: 'Required',
            materialGroupNotnull: 'Required',
            upstreamNameNotNull: 'Required',
            distributorTypeDesc: 'Distributor Type',
            distributor: 'Distributor',
            distributorListTrace: 'Preparatory',
            receiver: 'Receiver',
            businessCategory: 'Business Category',
            dsitributorBusinessCategory: 'Distributor Business Category',
            receiverBusinessCategory: 'Receiver Business Category',
            contractorModal: 'Contractor Managerment',
            upgradeDistributor: 'Upgrade Distributor',
            upgradeDistributorListTrace: 'Upgrade Preparatory',
            downgradeDistributorListTrace: 'Downgrade Preparatory',
            cancelDistributor: 'Cancel Distributor',
            cancelPreparatoryDistributor: 'Cancel preparatory distributor',
            setBidding: 'Set Bidding',
            cancelBidding: 'Cancel Bidding',
            formType: 'Business Type',
            submitTime: 'Submit Date',
            submitUser: 'Submit User',
            dutyID: 'Duty',
            name: 'Name',
            finalEditTime: 'FinalEditTime',
            finalEditDate: 'FinalEditDate',
            telephone: 'Telephone',
            phone: 'Phone',
            phoneNumber: 'Mobile',
            eMail: 'Email',
            addressField: 'Address',
            contactAddress: 'Contact address',
            isPrimaryContactDesc: 'Primary Contract',
            finalEditor: 'FinalEditor',
            addDistributorListApply: 'Add Distributor Apply',
            addDistributorListTraceApply: 'Add Preparatory Apply',
            upgradeDistributorListApply: 'Upgrade Distributor Apply',
            upgradeDistributorListTraceApply: 'Upgrade Preparatory Apply',
            downDistributorListTraceApply: 'Downgrade Preparatory Apply',
            editDistributorList: 'Edit Distributor',
            editTraceable: 'Edit Traceable',
            editReceiver: 'Edit Receiver',
            childBasic: 'Basic',
            childBasicError: 'Basic Information Error',
            childExpansion: 'Expand',
            childExpansionError: 'Expand Information Error',
            childRequestContractor: 'Contractor',
            childRequestContractorError: 'Contractor Error',
            childRequestProductChannel: 'Product Channel',
            childRequestProductChannelError: 'Product Channel Error',
            flowEffectiveDateIncorrect: 'Flow to effective date incorrect',
            flowEffectiveDateRequired: 'Flow effective date is required',
            medicineGroupID: 'Medicine Group',
            businessModeID: 'Business Mode',
            businessStatusID: 'Business Status',
            // tierTypeID: 'Tier',
            downstreamPropertyID: 'Property Name',
            parentName: 'Superior Organize',
            parentHospital: '所属总院',
            ddiStatus: 'DDI Status',
            taxRegistrationNo: 'Uniform Social Credit Code',
            fax: 'Fax',
            postalCode: 'Postcode',
            netAddress: 'Net Address',
            eleEMail: 'Email',
            bankName: 'Bank Name',
            debitCardNumber: 'Bank ',
            other1: 'Other One',
            other2: 'Other Two',
            other3: 'Other Three',
            deliveryAddress: 'Delivery Address',
            deliveryAddressCode: 'Ship-toCode',
            validNameRequired: 'Required',
            businessCompanyNameNull: 'Required',
            businessModeIDNull: 'Required',
            standardReceiver: 'Required',
            originalReceiver: 'Required',
            validateTelephoneNull: 'Required',
            validateEmail: 'Required',
            validateAddress: 'Required',
            batchNumber: 'Required',
            saleDate: 'Required',
            equalReceiver: 'The receiver name cannot be the same as the distributor name.',
            flow: 'Success',
            validNameLength: 'Distributor name is too long',
            validateTelephoneLength: 'telephone is too long',
            validateFaxLength: 'Fax is too long',
            validateTelephone: 'The format of the telephone is incorrect.',
            validateFax: 'The format of the Fax is incorrect.',
            validatePostalCode: 'The format of the postal code is incorrect.',
            validLocationRequired: 'Required',
            validSAPcodeLength: 'SAPcode is too long',
            validCreditSAPCodeSix: 'The Credit SAPCode length not less than 6 digits',
            beforeSAPCode: 'Before SAPCode',
            validBeforeSAPCode: 'Before SAPCode is too long',
            validBeforeSAPCodeSix: 'The Before SAPCode length not less than 6 digits',
            beingSAPCode: 'Being SAPCode',
            validBeingSAPCode: 'Being SAPCode is too long',
            validBeingSAPCodeSix: 'The current SAPCode length cannot be less than 6 digits.',
            validTaxRegistrationNoLength: 'Social Credit Code is too long.',
            validShortNameLength: 'Short Name is too long.',
            netAddressTrue: 'URL is too long.',
            validAddressLength: 'Address is too long.',
            validBankNameLength: 'Bank name is too long.',
            validDebitCardNumberLength: 'Debit card number is too long.',
            validOther1Length: 'Other1 is too long.',
            validOther2Length: 'Other2 is too long.',
            longitudeLength: 'Longitude is too long.',
            latitudeLength: 'Latitude is too long.',
            validDeliveryAddressLength: 'Delivery address is too long.',
            validRemarkLength: 'Remark is too long.',
            newAddress: 'Please fill in and submit it after completion',
            samaAddress: 'The same address already exists. Do not add it again.',
            samaAddressCode: 'The same Ship-toCode already exists. Do not add it repeatedly.',
            validatePhoneNumber: 'The format of the Phone Number is incorrect.',
            distributorNull: 'Required',
            nameNull: 'Required',
            nameLength: 'Name is too long',
            eMailTrue: 'The format of the Email is incorrect.',
            dutyIDNull: 'Required',
            effectiveDate: 'Sales Flow Start Date',
            inventoryDate: 'Inventory Initial Month',
            inventoryDateData: 'Channel Start Month',
            uploadStock: 'Upload Inventory Screenshot',
            alterMsg: 'History month needs to contact Data Manager to provide Salesflow',
            alterMsgTitle: 'The application form needs to be approved by the national director.',
            newProductChannel: 'Add Product Channel',
            addDistributorChannel: 'Add Distributor Channel',
            addPreparatoryDistributorChannel: 'Add Preparatory Channel',
            addDistributorChannelApply: 'Add Distributor Channel Apply',
            addPreparatoryDistributorChannelApply: 'Add Preparatory Channel Apply',
            productName: 'Product',
            openingInventory: 'Inventory',
            afterUpstreamName: 'Upstream',
            addProductChannel: 'Add Product Channel',
            cancelProductChannel: 'Cancel Product Channel',
            material: 'Material',
            batch: 'Batch Number',
            quantity: 'Inventory',
            productReferredJC: 'Referred',
            newProductChannelQuantity: 'Add Inventory',
            openingInventoryMessage: 'Stock can not be empty.',
            sumQuantity: 'Incorrect quantity of input',
            materialGroupId: 'Material Group',
            childUpstream: 'Upstream List',
            materialProductBatch: 'Material/Batch',
            materialProductBatchNull: 'Referred',
            wasUsedName: 'Former Name',
            fatherReceiverContractor: 'Change Contractor',
            whetherDelBill: 'Do you want to delete this bill',
            whetherWithdrawBill: 'Do you want to withdraw this bill',
            whetherDelEnclosure: 'Do you want to delete this attachment',
            fatherReceiverContractorSQ: 'Change contacts',
            productChannelNull: 'Required',
            stopQuantity: 'Initial Inventory',
            lastInventory: 'Last Inventory',
            stopPHQuantity: 'Deactivate batch number',
            productChannel: 'Product Channel',
            childChangeProductChannel: 'Change Channel',
            receiverProductChannelID: 'Product/Material Group',
            afterUpstreamNameNull: 'Required',
            receiverProductChannelIDNull: 'Required',
            startTime: 'Effective Date',
            stopTime: 'Stop Date',
            founder: 'Creator',
            founderTime: 'CreateTime',
            stopProductChannel: 'Stop Channel',
            upgradeTraceable: 'Success',
            upgradeFlow: 'Success',
            forcedConfirm: 'No person in charge. Do you want to confirm it by force?',
            gxpDistributorConfirmMessage: 'Distributor is GXP，confirm will be cancel GXP.',
            submitCancelDistributorGXPMessage: 'Distributor is GXP，are you sure cancel?',
            submitDowngradeDistributorGXPMessage: 'Distributor is GXP，are you sure downgrade?',
            receiverChange: 'Success',
            degradeTraceable: 'Success',
            deleteFlow: 'Success',
            productChannelDelete: 'Success',
            productChannelUpdate: 'Success',
            productChannelAdd: 'Success',
            purchasePerson: 'Purchaser',
            purchaseTelphone: 'Purchaser Telephone',
            purchaseEmail: 'Purchaser Email',
            validateDDI: 'Please select DDI Status',
            notMyParentName: 'A superior company cannot choose the current distributor.',
            parentHospitalNameIsCurrent: '所属总院不可以是当前医院。',
            contactDDI: 'It is recommended that DDI contacts be selected as the main contact.',
            documentYes: 'The distributor has already issued documents for approval.',
            documentYesAndNo: 'The distributor has already issued documents for approval. Do you want to continue.',
            increaseProductChannels: 'Add Channel',
            increaseProductChannelsDate: 'Add Channel Date',
            changeProductChannels: 'Change Channel',
            disableProductChannels: 'Stop Channel',
            fieldCannotBeZeroOrEmpty: 'Referred',
            pleaseEnterAnInteger: 'Please enter an integer.',
            // 新
            receiverBeEmpty: 'Referred',
            medicineGroupIDBeEmpty: 'Referred',
            tierTypeIDBeEmpty: 'Referred',
            businessModeIDBeEmpty: 'Referred',
            selcetParentName: 'Choose Upstream',
            channelType: 'Distributor Type',
            channelDistributorGBCode: 'Code',
            channelProduct: 'Product',
            channelMaterialGroupName: 'Material Group',
            channelProvinceName: 'Province',
            channelCityName: 'City',
            channelStartDate: 'Effective Date',
            channelStopDate: 'Discontinuation Date',
            inventoryData: 'Inventory End Date',
            channelStopMonth: 'Channel Stop Month',
            channelLoseEfficacyMonth: 'Channel Lose Efficacy Month',
            stopInventory: 'Stop Inventory',
            cancelReceiverWaringMessage: 'Receiver can not cancel',
            requestNodeNotExisten: 'If the document application node is not staffed, if confirmed, the document will be invalid.',
            channelEffectiveDate: 'Effective Date',
            initialInventoryDate: 'Initial Date',
            submitVerificationInventoryScreenshot: 'Stock screenshots must be uploaded.',
            submitVerificationChannelDate: 'Please select the date of product channel.',
            newInventory: 'Please select product or material group.',
            deleteChannelHints: 'Confirm the deletion of the channel?',
            newChannelDate: 'Please choose the start date of product channel.',
            productChannelChangeApplication: 'Product Channel Change Application',
            productChannelChangeDate: 'Product Channel Change Date',
            productChannelStopDate: 'Product Channel Stop Date',
            netAddressLength: 'Website length is too long',
            pleaseChooseProduct: 'Please select products first.',
            changeOfConsigneeInformation: 'Change of consignee information',
            distributorAndPHcodeNotAllEmpty: "Flow to business and phcode can't be empty",
            isBidding: 'Is Bidding',
            isBiddingDowngrade: 'After downgrading, the default bidder relationship will be stopped. Do you confirm the submission?',
            isBiddingCancel: 'If cancelled, the default bidder relationship will be stopped. Do you confirm the submission?',
            onceDistributor: 'Used to flow to business',
            oncePreparatory: 'Once traceable to business',
            cancleBidding: 'Whether to confirm the cancellation of the bidding?',
            commerceOrganization: 'Commerce Organization',
            leaderRoleLabel: 'Role Label',
            selectMonth: 'Select month',
            changeClassificationChannel: 'Change classification channel',
            submitApplication: 'Submit application',
            splitChannel: 'Split channel',
            changeTime: 'Change time',
            changeUpstream: 'Change upstream',
            monthIsMandatory: 'Month is mandatory',
            atLeastOneChannelChangeCanBeSubmitted: 'At least one channel change can be submitted',
            roleTarget: 'RoleTarget',
            salesflowCycle: 'SalesFlowCycle',
            existsCustomerAgreement: 'Are there multiple authorized purchasers, do you modify the customer agreement?',
            // 新增
            businessCompanyName: 'Business company name',
            managementLevel: 'The management level',
            changeList: 'Change list',
            upstreamDistributorName: 'Upstream Distributor Name',
            upstreamDistributorGBCode: 'Upstream Distributor Code',
            flowEffectiveDate: 'Flow to Effective Month',
            productTypingNotSelected: 'Product/typing not selected',
            channelMessage: 'Cannot add duplicate typing, please check the data!',
            differenceType: 'Please select a difference type',
            afterChangeText: 'After change',
            beforeChangeText: 'Before Change',
            changeColumnName: 'Change Field',
            shipToCode: 'Address Code',
            Address: 'Address',
            enumEditTypeDescription: 'Change Type',
            qualificationTypeName: 'Qualification Type',
            channelProductMessage: 'Unable to select product as channel information, please check if product or typed channel has been added!',
            validBusinessCategoryLength: 'Business classification length is too long ',
            validGBCodeLength: 'Code is too long.',
            validGBCodeRequired: 'Code cannot be empty ',
            hasAliasRequest: 'There is a change application document for the current alias, which cannot be edited.',
            hasAliasRequestCanNotSubmit: 'There is a change application document for the current alias, so the application cannot be submitted.',
            biddingProjectName: 'Bidding Project Name',
            biddingProjectStatus: 'Bidding Project Status',
            biddingProjectNO: 'Bidding Project NO',
            taxRegistrationNoNotNULL: 'TaxRegistrationNo can not be empty',

            purchaseCustomerYesAndNo: 'At present, the dealer is corresponding purchasing authorized person has Merck cloud business customer account number. Do you need to unbind it? '
        }
    }
}
export default distributor
