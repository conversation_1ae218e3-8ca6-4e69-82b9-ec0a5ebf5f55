/* eslint-disable key-spacing */
/* eslint-disable no-trailing-spaces */
// 流向管理-->中英文
export const salseFlowLocale = {
    'zhCN': {
        // 数据导入
        importData: {
            // 自动日志列表
            remark: '备注',
            saleQuantity: '销售数量',
            productMaterial: '产品/分型/物料',
            productName: '产品简称',
            monthOfFlow: '流向月份',
            saleDate: '销售日期',
            batchNo: '批号',
            notNull: '不能为空',
            tradeType: '销售类型',
            createTime: '创建日期',
            uploader: '上传人员',
            dataType: '数据类型',
            importTime: '导入时间',
            fileName: '原始文件',
            totalCount: '总数',
            wranningCount: '警告',
            differentCount: '错误',
            conflictCount: '冲突',
            wranningFileName: '警告文件',
            errorFileName: '错误文件',
            conflictFileName: '别名冲突文件',
            transitionMonth: '转换月份',
            transitionTotalCount: '转换总数',
            operationer: '操作人',
            operationTime: '操作时间',
            SAPTransition: 'SAP自动转换月数据',
            dailyFlow: '导入日流向模板下载',
            monthFlow: '导入月流向模板下载',
            standard: '导入标准模板下载',
            dailyPurchase: '导入日采购模板下载',
            monthPurchase: '导入月采购模板下载',
            dailyInventory: '导入日库存模板下载',
            monthInventory: '导入月库存模板下载',
            sapData: '导入SAP日数据模板下载',
            dailyStatus: '导入经销商日数据提供情况模板下载',
            monthStatus: '导入经销商月数据提供情况模板下载',
            flowManagement: '流向管理',
            confirmationOfFlowDirectionData: '流向数据确认',
            traceabilityConfirmation: '可追溯商业流向确认',
            importType: '导入类型',
            originalReceiving: '收货方别名',
            targetHospital: '目标医院',
            standardReceivingParty: '收货方名称',
            traceabilityName: '可追溯流向名称',
            salesFlowDistributorName: '发货方名称',
            salesFlowArea: '发货方省份/城市',
            selectingTheReceivingParty: '选择收货方',
            monthlySAPSuccess: 'SAP月数据生成成功',
            tipsStart: '点击转换',
            tipsEnd: '的日数据为月数据',
            validateEnumTradeType: '销售类型不能为空',
            productMaterialNotNull: '产品/分型/物料不能为空',
            saleQuantityNotNull: '销售数量必须大于0',
            distributorFlowOperationSave: '收货方类型和销售类型不匹配',
            operationFlow: '新增流向',
            editFlow: '编辑流向',
            whereDeleteSuccess: '是否删除',
            fileAllMessage: '上传附件不符合系统要求，请联系系统管理员',
            flowReversal: '流向冲销',
            fileIsTooLarge: '上传的附件过大，上传失败。',
            unknownError: '未知错误。',
            importFile: '导入文件',
            rightCount: '正确数',
            errorCount: '错误数',
            importAbnormalData: '错误数据地址',
            uploadTime: '上传时间',
            number: '只支持数字格式',
            maxLengtn: '最大长度不能超过16位'
        },
        queryData: {
            sumQuantity: '库存数合计',
            cycleMonth: '周期月份',
            delete: '删除',
            add: '新增',
            all: '全部省份',
            confirm: '确认',
            allConfirm: '全部确认',
            whetherConfirm: '是否确认',
            whetherRelease: '是否发布流向数据',
            whetherFreeze: '是否冻结当前流向数据',
            whetherAllConfirm: '是否全部确认',
            materialAllowOrder: '是否允许下单',
            startRecordRequired: '请选择需要确认的流向商业流向数据。',
            startAllRecordRequired: '没有需要确认的流向商业流向数据。',
            spanProvince: '跨省',
            importTypeName: '导入方式',
            remark: '备注',
            saleQuantity: '销售数量',
            saleQuantityLY: 'LY销售数量',
            salesAmount: '销售金额',
            salesAmountLY: 'LY销售金额',
            purchaseMonth: '采购日期(按月查询)',
            purchaseDate: '采购日期',
            purchaseQuantity: '采购数量',
            saleDate: '销售日期',
            startSalesFlowCycleMonth: '起始周期',
            endSalesFlowCycleMonth: '结束周期',
            salesFlowArea: '发货方省份/城市',
            salesFlow: '发货方名称',
            salesFlowPhCode: '发货方Code',
            receiverArea: '收货方省份/城市',
            receiverPhCode: '收货方Code',
            customerCategory: '客户类型',
            receiverName: '收货方名称',
            receiver: '收货方名称',
            receiverNew: '收货方名称New',
            receiverSerchKey: '收货方名称关键字',
            receiverType: '收货方类型',
            receiverAnotherName: '收货方别名',
            receiverBeforeName: '收货方曾用名',
            tradeType: '销售类型',
            importType: '导入方式',
            importTime: '导入日期',
            isSpanProvince: '跨省',
            productShortName: '产品简称',
            spec: '规格',
            phCode: 'Code',
            phBusinessCode: '发货方Code',
            batchNo: '批号',
            businessCompanyType: '商业公司类型',
            sequence: '序号',
            queryCode: '流向ID',
            redReversalCode:'红冲流向ID',
            salesFlowCycleMonth: '记录月份',
            salesFlowCycleMonthLY: 'LY记录月份',
            salesFlowCityName: '发货方城市',
            salesFlowProvinceName: '发货方省份',
            submitTime: '提交日期',
            productName: '产品简称',
            materialGroupName: '分型名称',
            standardName: '标准名称',
            materialName: '物料名称',
            materialCode: '物料号',
            expiryDate: '有效期',
            receiverProvinceName: '收货方省份',
            receiverCityName: '收货方城市',
            receiverCountyName: '收货方区县',
            saleType: '销售类型',
            actualTime: '实际日期',
            businessdate: '业务日期',
            fileTypeName: '文件类型',
            returnTime: '返回时间',
            isReturn: '返回情况',
            notReturnReason: '未返回说明',
            dataSource: '数据采集来源',
            CollectionSituation: '补采情况',
            searchProductType: '单产品/多分型',
            productMaterial: '产品/分型/物料',
            groupMaterial: '分型/物料',
            analepticFeedback: '反馈',
            receiverName_receiverAnotherName: '收货方名称',
            monthOfFlow: '记录月份',
            notNull: '不能为空',
            pleaseSelectDataFirst: '请先选择数据',
            addPreparatorySalesFlowMonthly: '可追溯流向确认数据',
            attachmentName: '附件名',
            uploadTime: '上传时间',
            changeDefaultBatch: '修改',
            whetherChangeDefaultBatch: '是否修改',
            productCascaderRequired: '请选择分型/物料',
            batchNumberRequired: '请选择批号',
            notSelectMultipleProducts: '不能选择多个产品',
            whetherChangeDefaultBatchNumber: '权限不足,无法修改所选数据',
            analepticQueryModal: '兴奋剂流向反馈',
            analepticView: '查看兴奋剂反馈',
            deleteSuccess: '删除成功',
            originalRecipient: '收货方别名',
            preparatoryProvinceName: '可追溯商业省份',
            preparatoryArea: '可追溯商业省份/城市',
            preparatoryCityName: '可追溯商业城市',
            preparatoryName: '可追溯商业名称',
            preparatoryGBCode: '可追溯商业Code',
            dopingFeedbackLength: '反馈内容过长',
            salesReturnPercentSetUp: '退货率基线设置',
            salesReturnPercentSetUpIsNotNull: '退货率基线不能为空',
            percentSetUp: '百分比设定',
            salesReturnReason: '销退原因',
            chineseDescription: '中文描述',
            englishDescription: '英文描述',
            distributorProvinceName: '发货方省份',
            distributorName: '流向商业名称',
            standardReceiver: '收货方名称',
            salesReturnOwnerNameCn: '主导人中文名',
            salesReturnOwnerNameEn: '主导人英文名',
            salesReturnOwnerIsNotNull: '销退主导人不能为空',
            salesReturnReasonIsNotNull: '销退原因不能为空',
            salesReturnReasonOverLength: '销退原因长度超过限制',
            descriptionCnOverLength: '销退中文描述长度超过限制',
            descriptionEnOverLength: '销退英文描述长度超过限制',
            salesReturnReasonManagement: '销退原因管理',
            salesReturnOwner: '主导人',
            ownerChineseNameIsNotNull: '主导人中文名不能为空',
            ownerChineseNameOverLength: '主导人中文名长度超过限制',
            ownerEnglishNameOverLength: '主导人英文名长度超过限制',
            salesReturnOwnerChineseName: '主导人中文',
            salesReturnOwnerEnglishName: '主导人英文',
            salesReturnOwnerManagement: '销退主导人管理',
            salesReturndescription: '备注',
            ownerName: '主导人',
            distributorProvinceCity: '发货方省份/城市',
            distributorRegion: '流向商业大区',
            regionName: '收货方大区',
            sales: '销量',
            purchaser: '购买方名称',
            invoiceCompany: '开票方',
            medicineGroup: '所属医药集团',
            tierType: '管理级别',
            salesFlowType: '数据来源',
            returnSales: '退货量',
            feadBackTime: '反馈日期',
            feadBackUser: '反馈人',
            terminalArea: '终端省份/城市',
            terminalName: '终端名称',
            terminalGBCode: '终端Code',
            terminalType: '终端类型',
            targerHospitalWarningType: '预警类型',
            onpassage: '在途',
            DDIInventory: 'DDI日库存',
            inventoryCount: '库存数',
            isTargetHospital: '是否目标医院',
            isShowTargetHospital: '是否展示目标医院',
            totalAmount: '合计',
            targetHospitalListYes: '展示目标医院',
            targetHospitalListNo: '不展示目标医院',
            isAssessmentPrice: '列表中金额按照物料的考核价进行计算，若考核价未维护，则按0进行计算。',
            receivingArea: '收货方区县',
            totalOfAmount: '合计(金额)',
            totalSales: '销量合计',
            whetherTraceable: '是否发布可追溯流向数据',
            totalOndAmount: '金额合计',
            distributorMdMCode: '发货方MDMCode',
            receiverMdMCode: '收货方MDMCode',
            mdmName: 'MDM名称',
            newMdMCode: 'MDMCodeNew',
            newMdMName: 'MDM名称New',
            phcodeNew: 'CodeNew',
            isSplitReceiver: '是否拆分',
            prescriptionSource: '处方来源',
            partner: '合作伙伴',
            businessCategory: '业务分类',
            deliveryAddress: '送货地址',
            receivingAddress:'收货地址',
            customerID: '客户ID号',
            deliveryReceiverName: '收货方名称New',
            splitDate: '拆分日期',
            businessStatus: '经营状态',
            unitPrice:'单价',
            versionNumber:'版本号',
            company:'单位',
            quantity: '数量',
            validNameRequired: '发货方名称不能为空',
            hospitalRequired: '未选择医院数据',
            distributorRequired: '未选择流向商业数据'
        },
        // 数据删除
        deleteData: {
            sellerArea: '发货方省份/城市',
            sellerProvince: '发货方省份',
            sellerCity: '发货方城市',
            seller: '发货方名称',
            sellerGBCode: '发货方Code',
            sellerLevel: '发货方级别',
            phCode: 'Code',
            fileType: '文件类型',
            returnStatus: '返回情况',
            returnTime: '返回日期',
            actualDate: '实际日期',
            delAll: '全部删除',
            recordMonth: '记录月份',
            receiverArea: '收货方省份/城市',
            receiverProvince: '收货方省份',
            receiverName: '收货方名称',
            receiver: '收货方名称',
            receiverPhCode: '收货方Code',
            receiverType: '收货方类型',
            searchProduct: '单产品/多产品',
            searchProductType: '单产品/多分型',
            batchNo: '批号',
            submitTime: '提交时间',
            searchCode: '查询Code',
            productName: '产品简称',
            materiel: '物料',
            packingUnit: '包装单位',
            purchaseDate: '采购日期',
            purchaseQuantity: '采购数量',
            fatherProvince: '上游省份',
            fatherName: '上游名称',
            inventoryDate: '库存日期',
            type: '分型名称',
            expiryDate: '有效期',
            quantity: '数量',
            searchByMonth: '按月查询',
            searchByDay: '按日查询',
            salesFlowArea: '发货方省份/城市',
            salesFlowProvince: '发货方省份',
            salesFlowCity: '发货方城市',
            salesFlowPhCode: '发货方Code',
            salesFlow: '发货方名称',
            salesFlowName: '发货方名称',
            salesFlowChannelLevel: '流向商业渠道级别',
            saleType: '销售类型',
            saleQuantity: '销售数量',
            remark: '备注',
            invoicerProvince: '发货方省份',
            invoicerName: '发货方名称',
            businessCompanyType: '商业公司类型',
            dataAffordSituation: '数据提供情况',
            delAllDialog: '确定要删除吗？'
        },
        // 流向核查
        deficiencyInspect: {
            downloadComplaintsZip:'申诉附件下载',
            complaintsQuantity: '申诉数量',
            complaintsRemark: '申诉原因',
            complaintsReceiver: '申诉收货方',
            whetherToConfirmFeedback: '反馈后不可更改，是否确认反馈！',
            salesFlowCycleID: '记录月份',
            distributor: '流向商业',
            deficiencyType: '差异类型',
            tradeType: '销售类型',
            responsibilityer: '负责人',
            distributorProvince: '发货方省份',
            receiver: '收货方',
            receiverGBCode: '收货方PHCODE',
            productName: '产品简称',
            materialGroup: '分型名称',
            material: '物料',
            batchNumber: '批号',
            validityDate: '有效期',
            saleDate: '销售日期',
            quantity: '数量',
            remark: '备注',
            submitTime: '更新时间',
            shipper: '发货方名称',
            shipperRegion: '发货方大区',
            shipperProvince: '发货方省份',
            shipperCity: '发货方城市',
            shipperProvinceAndCity: '发货方省份/城市',
            shipperGBCode: '发货方Code',
            salesDate: '销售月份',
            isHandled: '是否处理',
            isProvideSale: '是否提供流向',
            shipperCompanyName: '登记发货公司',
            receiverEmployeeName: '收货方负责人',
            businessEmployeeName: '业务负责人',
            handleUpdate: '外勤处理',
            handleCheck: '查看',
            salesFlowAbnormal: '渠道异常',
            salesFlowDetailed: '流向明细',
            feedbackReason: '反馈原因',
            feedbackReasonOne: '渠道归拢中，暂不调整',
            feedbackReasonTow: '按照实际发货上游变更渠道',
            hasFeedBack: '已反馈',
            suspectAllocation: '疑似调拨',
            addProductChannel: '新增渠道',
            feadBack: '反馈',
            isFeadBack: '反馈完成',
            salesFlowCategory: '数据来源',
            roleTag: '角色标签',
            feadBackUser: '反馈人',
            feadBackTime: '反馈日期',
            createTime: '生成时间',
            salesFlowDaily: '日流向',
            salesFlowMonthly: '月流向',
            feedbackReasonRequired: '反馈原因必填',
            existPending: '存在待审批/待确认的申请单',
            existPendingeffect: '存在待生效的申请单',
            existPendingeffectConfirm: '存在待生效的申请单,是否撤回重新申请',
            feedbackSuccess: '反馈成功',
            enumSalesFlowCategoryDesc: '数据来源',
            isHandledDesc: '反馈完成',
            isChannelAbnormal: '渠道是否异常',
            newUpstreamName: '新上游',
            feadBackUserName: '反馈人',
            changeChannel: '变更渠道',
            existenceIncompleteApproval: '存在未完成审批得单据，无法变更渠道',
            existingPendingApplicationForm: '存在待生效申请单，是否撤销后重新申请？',
            confirmFeedBack: '反馈后不可更改，是否确认反馈？',
            actualShipmentUpstream: '实际发货上游',
            registerUpstream: '登记上游',
            theNumberShipments: '发货数量',
            warningType: '预警类型',
            distributorEmployeeName: '发货方负责人',
            targetHospital: '目标医院',
            notTargetHospital: '非目标医院',
            redWarning: '红色预警',
            yellowWarning: '黄色预警',
            terminalName: '终端名称',
            terminalRegion: '收货方大区',
            terminalProvinceName: '终端省份',
            terminalGBCode: '终端Code',
            targetHospatilType: '收货方类型',
            productReferred: '产品简称',
            lastOneMonthQuantity: '前1月销量',
            lastTweMonthQuantity: '前2月销量',
            lastThreeMonthQuantity: '前3月销量',
            lastFourMonthQuantity: '前4月销量',
            lastFiveMonthQuantity: '前5月销量',
            lastSixMonthQuantity: '前6月销量',
            currentMonthQuantity: '当月销量',
            lastSixMonthAVGQuantity: '前6个月平均销量',
            isTargetHospital: '是否目标医院',
            allTargetHospitalData: '全部数据',
            warningTargetHospitalData: '预警数据',
            complaintsFormStatus: '申诉状态',
            complaintsComplete: '申诉完成',
            downloadComplaintsZip: '下载申诉附件',
            complaintsFormType:'申诉类型',
            complaintsRemark: '申诉说明'
        }
    },
    'enUS': {
        // 数据导入
        importData: {
            remark: 'Remark',
            saleQuantity: 'Quantity',
            productMaterial: 'Product/Material Group/Material',
            productName: 'Product Referred',
            monthOfFlow: 'Month',
            saleDate: 'Sale Date',
            batchNo: 'Batch Number',
            notNull: ' Required',
            tradeType: 'Trade Type',
            createTime: 'CreateTime',
            uploader: 'Uploader',
            dataType: 'Data Type',
            importTime: 'Import Time',
            fileName: 'File Name',
            totalCount: 'Total',
            wranningCount: 'Wranning',
            differentCount: 'Error',
            conflictCount: 'Conflict',
            wranningFileName: 'Wranning Data',
            errorFileName: 'Error Data',
            conflictFileName: 'Conflict Data',
            transitionMonth: 'Month',
            transitionTotalCount: 'Total',
            operationer: 'Operator',
            operationTime: 'Operation Time',
            SAPTransition: 'Generate',
            dailyFlow: 'Daily SalesFlow',
            monthFlow: 'Monthly SalesFlow',
            dailyPurchase: 'Daily Purchase',
            monthPurchase: 'Monthly Purchase',
            dailyInventory: 'Daily Inventory',
            monthInventory: 'Monthly Inventory',
            sapData: 'SAP SalesFlow',
            dailyStatus: 'Daily Status',
            monthStatus: 'Monthly Status',
            flowManagement: 'Sales Flow',
            confirmationOfFlowDirectionData: 'Confirm',
            traceabilityConfirmation: 'Preparatory Sales Flow',
            importType: 'Import Type',
            originalReceiving: 'Receiver Alias',
            targetHospital: 'Target hospital',
            standardReceivingParty: 'Receiver Name',
            traceabilityName: 'Preparatory Distributor Name',
            salesFlowDistributorName: 'Distributor Name',
            salesFlowArea: 'Distributor Province/City',
            selectingTheReceivingParty: 'Choose Receiver',
            monthlySAPSuccess: 'Success',
            tipsStart: 'Generate',
            tipsEnd: 'SAP Monthly SalesFlow',
            validateEnumTradeType: 'Required',
            productMaterialNotNull: 'Required',
            saleQuantityNotNull: 'Sales quantity must be greater than 0.',
            distributorFlowOperationSave: 'The type of receiver and the type of sale do not match.',
            operationFlow: 'Add SalesFlow',
            editFlow: 'Edit SalesFlow',
            whereDeleteSuccess: 'Are you sure you want to delete it?',
            fileAllMessage: 'Uploading attachments does not meet the system requirements, please contact the administrator.',
            flowReversal: 'Flow reversal',
            fileIsTooLarge: 'The attachment you uploaded is too large and the upload failed.',
            unknownError: 'Unknown error.',
            deliveryAddress: 'DeliveryAddress',
            prescriptionSource: 'PrescriptionSource',
            partner: 'Partner',
            importFile: 'Import File',
            rightCount: 'Right Count',
            errorCount: 'Error Count',
            importAbnormalData: 'Error Data',
            uploadTime: 'Upload Time',
            customerID: 'Customer ID',
            number: 'mast be Number',
            maxLengtn: 'Maximum length cannot exceed 16 bits'
        },
        queryData: {
            sumQuantity: 'Total inventory',
            cycleMonth: 'Month',
            delete: 'Delete',
            add: 'Add',
            all: 'All Province',
            confirm: 'Confirm',
            allConfirm: 'All Confirm',
            whetherConfirm: 'Are you sure you want to confirm it?',
            whetherRelease: 'Are you sure you want to release it?',
            whetherFreeze: 'Are you sure you want to freeze it?',
            whetherAllConfirm: 'Are you sure you want to confirm it?',
            materialAllowOrder: 'Whether to allow orders',
            startRecordRequired: 'Please select the data you need to confirm.',
            startAllRecordRequired: 'There is no data to confirm.',
            spanProvince: 'Cross Province',
            importTypeName: 'Import Type',
            remark: 'Remark',
            purchaseMonth: 'Purchase Date',
            saleQuantityLY: 'LY sales quantity',
            salesAmount: 'Sales Amount',
            salesAmountLY: 'LY sales amount',
            purchaseDate: 'Purchase Date',
            saleDate: 'Sale Date',
            startSalesFlowCycleMonth: 'Start Cycle',
            endSalesFlowCycleMonth: 'End Cycle',
            salesFlowCycleMonth: 'Record month',
            salesFlowCycleMonthLY: 'LY record month',
            purchaseQuantity: 'Quantity',
            salesFlowArea: 'Distributor Province/City',
            saleQuantity: 'Quantity',
            salesFlow: 'Distributor Name',
            receiverArea: 'Receiver Province/City',
            receiverName: 'Receiver Name',
            receiver: 'Receiver',
            receiverNew: 'ReceiverNew',
            receiverSerchKey: 'Receiver Name Serch Key',
            receiverAnotherName: 'Receiver Alias',
            receiverBeforeName: 'Receiver Former Name',
            receiverType: 'Receiver Type',
            receiverPhCode: 'Receiver Code',
            customerCategory: '客户类型',
            salesFlowPhCode: 'Distributor Code',
            tradeType: 'Trade Type',
            importType: 'Import Type',
            importTime: 'ImportTime',
            isSpanProvince: 'Cross Province',
            productShortName: 'Product Referred',
            spec: '规格',
            batchNo: 'Batch Number',
            businessCompanyType: 'Distributor Type',
            sequence: 'No',
            queryCode: 'Query Id',
            redReversalCode:'RedReversalCode',
            salesFlowProvinceName: 'Province',
            salesFlowCityName: 'City',
            actualTime: 'ActualTime',
            businessdate: 'Business date',
            fileTypeName: 'FileType',
            returnTime: 'ReturnTime',
            isReturn: 'Return Status',
            notReturnReason: 'Description not returned',
            dataSource: 'Data collection sources',
            CollectionSituation: 'Supplementary mining',
            phCode: 'Code',
            submitTime: 'Submit Time',
            productName: 'Product Referred',
            materialGroupName: 'Material Group',
            standardName: '标准名称',
            materialName: 'Material',
            materialCode: 'Material Code',
            expiryDate: 'Expiry Date',
            receiverProvinceName: 'Province',
            receiverCityName: 'City',
            receiverCountyName: 'County',
            saleType: 'Trade Type',
            searchProductType: 'Material Group',
            productMaterial: 'Product/Material Group/Material',
            groupMaterial: 'Material Group/Material',
            analepticFeedback: 'Reply',
            receiverName_receiverAnotherName: 'Receiver Name',
            monthOfFlow: 'Month',
            notNull: ' Required',
            pleaseSelectDataFirst: 'Please choose the data to be processed.',
            addPreparatorySalesFlowMonthly: 'Confirm Preparatory SalesFlow',
            attachmentName: 'Attachment Name',
            uploadTime: 'Upload Time',
            changeDefaultBatch: 'Edit',
            whetherChangeDefaultBatch: 'Are you sure you want to modify it?',
            productCascaderRequired: 'Required',
            batchNumberRequired: 'Required',
            notSelectMultipleProducts: 'Multiple products cannot be selected.',
            whetherChangeDefaultBatchNumber: 'You have enough permissions to edit',
            analepticQueryModal: 'Reply',
            analepticView: 'View',
            deleteSuccess: 'Success',
            originalRecipient: 'Shipper alias',
            preparatoryProvinceName: 'Preparatory Province Name',
            preparatoryArea: 'Business province/city traceability',
            preparatoryCityName: 'Business cities are traceable',
            preparatoryName: 'Preparatory Name',
            preparatoryGBCode: 'Preparatory Code',
            dopingFeedbackLength: 'Feedback Too Long',
            salesReturnPercentSetUp: 'Sales Return Rate Line',
            salesReturnPercentSetUpIsNotNull: 'Sales Return Rate Line can not be  empty',
            percentSetUp: 'Percent Setting',
            salesReturnReason: 'Sales Return Reason',
            chineseDescription: 'Chinese Description',
            englishDescription: 'English Description',
            distributorProvinceName: 'Distributor Province',
            distributorName: 'Distributor Name',
            standardReceiver: 'Receiver Name',
            salesReturnOwnerNameCn: 'Owner Chinese Name',
            salesReturnOwnerNameEn: 'Owner English Name',
            salesReturnOwnerIsNotNull: 'Owner can not be empty',
            salesReturnReasonIsNotNull: 'Reason can not be empty',
            salesReturnReasonOverLength: 'Reason is too long',
            descriptionCnOverLength: 'Sales return chinese description is too long',
            descriptionEnOverLength: 'Sales return english description is too long',
            salesReturnOwner: 'SalesReturnOwner',
            salesReturnReasonManagement: 'SalesReturnReasonManagement',
            ownerChineseNameIsNotNull: 'Owner chinese can not be empty',
            ownerChineseNameOverLength: 'Owner chinese is too long',
            ownerEnglishNameOverLength: 'Owner english is too long',
            salesReturnOwnerChineseName: 'Owner Chinese Name',
            salesReturnOwnerEnglishName: 'Owner English Name',
            salesReturnOwnerManagement: 'SalesReturnOwnerManagement',
            salesReturndescription: 'Remark',
            ownerName: 'OWNER',
            distributorProvinceCity: 'Province/City',
            distributorRegion: 'Region',
            regionName: 'Region',
            sales: 'Sales',
            purchaser: 'Purchaser',
            invoiceCompany: 'Bill Company',
            medicineGroup: 'Medicine Group',
            tierType: 'Management level',
            salesFlowType: 'Data Sources',
            returnSales: 'Return amount',
            feadBackTime: 'FeadBack Time',
            feadBackUser: 'FeadBack User',
            terminalArea: 'Terminal Area',
            terminalName: 'Terminal Name',
            terminalGBCode: 'Terminal Code',
            terminalType: 'Terminal Type',
            targerHospitalWarningType: 'Warning Type',
            onpassage: 'on the way',
            DDIInventory: 'DDI Daily stock',
            inventoryCount: 'Inventory',
            isTargetHospital: 'Is TargetHospital',
            totalAmount: 'Total',
            targetHospitalListYes: 'Display target hospital',
            targetHospitalListNo: 'Do not show the target hospital',
            isAssessmentPrice: 'In the list the amount is calculated according to the assessed price of the material. If the assessed price is not maintained, it is calculated as 0.',
            receivingArea: 'Receiving area',
            totalOfAmount: 'total(Amount)',
            totalSales: 'Total sales',
            totalOndAmount: 'Total amount',
            whetherTraceable: 'Whether to publish traceable flow data',
            isShowTargetHospital: 'Whether to show the target hospital',
            distributorMdMCode: 'Distributor MDMCode',
            receiverMdMCode: 'Receiver MDMCode',
            mdmName: 'MDMName',
            newMdMCode: 'MDMCodeNew',
            newMdMName: 'MDMNameNew',
            phcodeNew: 'CodeNew',
            isSplitReceiver: 'Is Split Receiver',
            prescriptionSource: 'Prescription Source',
            partner: 'Partner',
            businessCategory: 'Category',
            deliveryAddress: 'DeliveryAddress',
            customerID: 'Customer ID',
            deliveryReceiverName: 'ReceiverNameNew',
            splitDate: 'Split Date',
            businessStatus: 'Business Status',
            unitPrice:'Unit Price',
            versionNumber:'Version number',
            company:'Company',
            hospitalRequired: 'Must choose the hospital',
            distributorRequired: '未选择Must choose the distributor'
        },
        // 数据删除
        deleteData: {
            sellerArea: 'Distributor Province/City',
            sellerProvince: 'Distributor Province',
            sellerCity: 'Distributor City',
            seller: 'Distributor',
            sellerGBCode: 'Distributor Code',
            sellerLevel: 'Tier',
            phCode: 'Code',
            fileType: 'Data Type',
            returnStatus: 'Return Status',
            returnTime: 'Return Time',
            actualDate: 'Actual Date',
            delAll: 'Delete All',
            recordMonth: 'Month',
            receiverArea: 'Receiver Province/City',
            receiverProvince: 'Receiver Province',
            receiverName: 'Receiver Name',
            receiver: 'Receiver',
            receiverPhCode: 'Receiver Code',
            receiverType: 'Receiver Type',
            searchProduct: 'Product',
            searchProductType: 'Material Group',
            batchNo: 'Batch Number',
            submitTime: 'Submit Time',
            searchCode: 'Query Code',
            productName: 'Product Referred',
            materiel: 'Material',
            packingUnit: 'Package',
            purchaseDate: 'Purchase Date',
            purchaseQuantity: 'Quantity',
            fatherProvince: 'Upstream Province',
            fatherName: 'Upstream',
            inventoryDate: 'Inventory Date',
            type: 'Material Group',
            expiryDate: 'Expiry Date',
            quantity: 'Quantity',
            searchByMonth: 'By Monthly',
            searchByDay: 'By Daily',
            salesFlowArea: 'Distributor Province/City',
            salesFlowProvince: 'Distributor Province',
            salesFlowCity: 'Distributor City',
            salesFlowPhCode: ' Distributor Code',
            salesFlow: 'Distributor Name',
            salesFlowName: 'Distributor Name',
            salesFlowChannelLevel: 'Tier',
            saleType: 'Trade Type',
            saleQuantity: 'Quantity',
            remark: 'Remark',
            invoicerProvince: 'Distributor Province',
            invoicerName: 'Distributor',
            businessCompanyType: 'Distributor Type',
            dataAffordSituation: 'Data Status',
            delAllDialog: 'Are you sure you want to delete it?'
        },
        // 流向核查
        deficiencyInspect: {
            complaintsQuantity: '申诉数量',
            complaintsReceiver: '申诉收货方',
            complaintsRemark: '申诉原因',
            downloadComplaintsZip:'申诉附件下载',
            whetherToConfirmFeedback: 'Feedback can not be changed, whether to confirm the feedback!',
            salesFlowCycleID: 'Month',
            distributor: 'Distributor',
            deficiencyType: 'Deficiency Type',
            tradeType: 'Trade Type',
            responsibilityer: 'Representative',
            distributorProvince: 'Distributor Province',
            receiver: 'Receiver',
            receiverGBCode: 'Receiver Code',
            productName: 'Product Code',
            materialGroup: 'Material Group',
            material: 'Material',
            batchNumber: 'Batch Number',
            validityDate: 'Expiry Date',
            saleDate: 'Sale Date',
            quantity: 'Quantity',
            remark: 'Remark',
            submitTime: 'Submit Time',
            shipper: 'Distributor',
            shipperRegion: 'Distributor Region',
            shipperProvince: 'Distributor Province',
            shipperCity: 'Distributor City',
            shipperProvinceAndCity: 'Distributor Province/City',
            shipperGBCode: 'Distributor Code',
            salesDate: 'Month',
            isHandled: 'Is Handled',
            isProvideSale: 'Provide SalesFlow',
            shipperCompanyName: 'Upstream',
            receiverEmployeeName: 'Receiver Employee',
            businessEmployeeName: '业务负责人',
            handleUpdate: 'Reply',
            handleCheck: 'View',
            salesFlowAbnormal: 'Reply',
            salesFlowDetailed: 'SalesFlow Detail',
            feedbackReason: 'Feedback reason',
            feedbackReasonOne: 'Channels are being rolled up, not adjusted yet',
            feedbackReasonTow: 'According to the actual shipment upstream change channel',
            hasFeedBack: 'Has Feedback',
            suspectAllocation: 'Suspect Allocation',
            addProductChannel: 'Add Product Channel',
            feadBack: 'Fead Back',
            isFeadBack: 'Is FeedBack',
            salesFlowCategory: 'Data Sources',
            roleTag: 'Role Tag',
            feadBackUser: 'FeedBack User',
            feadBackTime: 'FeedBack Time',
            createTime: 'Create Time',
            salesFlowDaily: 'SalesFlow Daily',
            salesFlowMonthly: 'SalesFlow Monthly',
            feedbackReasonRequired: 'Feedback Reason Required',
            existPending: 'There are pending approval/to be confirmed application forms',
            existPendingeffect: 'There is an application form to be valid',
            existPendingeffectConfirm: 'There is an application form to be valid, whether to withdraw the re-application',
            feedbackSuccess: 'Feedback Success',
            enumSalesFlowCategoryDesc: 'Data sources',
            isHandledDesc: 'Feedback completed',
            isChannelAbnormal: 'Is channel abnormal',
            newUpstreamName: 'New upstream',
            feadBackUserName: 'Feedback person',
            changeChannel: 'Change channel',
            existenceIncompleteApproval: 'Documents that have not been completed for examination and approval cannot be altered',
            existingPendingApplicationForm: 'If there is an application form to be effective, will it be revoked and reapplied?',
            confirmFeedBack: 'Cannot be changed after feedback, is the feedback confirmed?',
            actualShipmentUpstream: 'Actual shipment upstream',
            registerUpstream: 'Register upstream',
            theNumberShipments: 'The number of shipments',
            warningType: 'Warning Type',
            distributorEmployeeName: 'Distributor Employee',
            targetHospital: 'Target Hospital',
            notTargetHospital: 'Not Target Hospital',
            redWarning: 'Red Warning',
            yellowWarning: 'Yellow Warning',
            terminalName: 'Terminal Name',
            terminalRegion: 'Region',
            terminalProvinceName: 'Province',
            terminalGBCode: 'Code',
            targetHospatilType: 'Receiver Type',
            productReferred: 'Referred',
            lastOneMonthQuantity: 'Last One Month Sales',
            lastTweMonthQuantity: 'Last Twe Month Sales',
            lastThreeMonthQuantity: 'Last Three Month Sales',
            lastFourMonthQuantity: 'Last Four Month Sales',
            lastFiveMonthQuantity: 'Last Five Month Sales',
            lastSixMonthQuantity: 'Last Six Month Sales',
            currentMonthQuantity: 'Current Month Sales',
            lastSixMonthAVGQuantity: 'Average Sales',
            isTargetHospital: 'Is Target Hospital',
            allTargetHospitalData: 'All Data',
            warningTargetHospitalData: 'Warning Data',
            complaintsFormStatus: '申诉状态',
            complaintsComplete: '申诉完成',
            downloadComplaintsZip: '下载申诉附件',
            complaintsFormType:'申诉类型',
            complaintsRemark: '申诉说明'
        }
    }
}
export default salseFlowLocale
