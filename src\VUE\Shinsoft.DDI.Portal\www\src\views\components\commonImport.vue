<!--批量导入 作者：Tiger-->
<template>
  <div>
    <Form :label-width="110">
      <Row>
        <Col span="6">
        <!-- 导入时间 -->
        <DatePicker :value="importLogFilter.ImportTime" @on-change="changeDatepicker" placement="bottom-end" type="daterange" split-panels :placeholder="$t('batchImport.importTime')" style="width:100%"></DatePicker>
        </Col>
        <Col span="2">
        <!-- 查询 -->
        <Button icon="ios-search-strong" @click="handleSearch">{{ $t('system.search') }}</Button>
        </Col>
        <Col span="3" offset="5">
        <Upload ref="uploadQualification" :show-upload-list="false" :on-success="handleSuccess"
        :format="['xlsx']" :max-size="this.$constDefinition.uploadSize.uploadSizeM20"
        :on-format-error="handleFormatError"
        :on-error="handleUploadError" :on-exceeded-size="handleMaxSize"
        :before-upload="handleBeforeUpload" type="select"
        :action="uploadReceiverUpdateAction"
        :headers="uploadHeaders" class="primary_L">
          <Button icon="ios-cloud-upload-outline">{{$t('system.selectFile')}}</Button>
        </Upload>
        </Col>
        <Col span="4"> &nbsp;
        </Col>
        <Col span="4">
        <!-- TODO: 需要更换模板地址 -->
        <a :href="templateUrl" target="_blank" class="uploadListA">{{$t("system.templateDownload")}}</a>
        </Col>
      </Row>
      <Row class="ivu-row-Top">
        <Col span="24">
        <Table stripe :columns="showImportLogColumns" size="small" :data="importLogList" @on-sort-change="handleImportLogSortChange"></Table>
        </Col>
        <Col span="24" class="ivu-row-Top">
        <Page class="primary_R" :total="importLogTotalCount"   :page-size-opts="pageSizeOpts"  :current="importLogFilter.Page" :page-size="importLogFilter.Per" size="small" @on-page-size-change="changeImportLogPageSize" show-total show-sizer show-elevator @on-change="changeImportLogPage"></Page>
        </Col>
      </Row>
    </Form>
    <div slot="footer">
      <Row class="ivu-row-Padding-5 ivu-row-Top">
        <Col span="24">
        <Row type="flex" justify="end" :gutter="10">
          <Col span="2.5">
          <Button @click="handlecancel" icon="ios-close-outline" style="margin-left: 8px">{{$t('system.close')}}</Button>
          </Col>
        </Row>
        </Col>
      </Row>
    </div>
    <div class="progressBarBox" v-show="visible">
      <div class="progressBarCon">
        <!-- 文件上传 -->
        <p><b>{{$t('system.uploadFileProgressBar')}}</b></p>
        <div id="progressCss">
          <span></span>
        </div>
        <!-- 文件名称 文件大小 字节 -->
        <p :title="fileAllData.name">{{$t('system.uploadFileName')}}{{fileAllData.name}}</p>
        <p>{{$t('system.uploadFileSize')}}{{fileAllData.size}}{{$t('system.uploadFileByte')}}</p>
      </div>
    </div>
  </div>
</template>
<script>
    import moment from 'moment';
    export default {
        props: ['isLoad', 'importTable', 'importTemplateFile'],
        data () {
            return {
                fileAllData: {},
                visible: false,
                uploadReceiverUpdateAction: '',
                uploadHeaders: {
                    'Accept-Language': localStorage.getItem('currentLanguage') === null ? 'zh-CN' : localStorage.getItem('currentLanguage') === 'mergeZH' ? 'zh-CN' : 'en-US',
                    Authorization: 'SperogenixAuthentication ' + localStorage.token,
                    route: sessionStorage.route
                },
                filter: {},
                pageSizeOpts: this.$constDefinition.pageSizeOpts,
                importLogFilter: {
                    Page: 1,
                    Per: 10,
                    ImportTime: null,
                    ImportTable: this.importTable
                },
                importLogList: [],
                importLogTotalCount: 0,
                templateUrl: '',
                // 上传记录列表
                showImportLogColumns: [
                    {
                        width: 70,
                        align: 'center',
                        title: this.$t('system.no'),
                        render: (h, params) => {
                            return h(
                                'span',
                                params.index +
                                    (this.importLogFilter.Page - 1) * this.importLogFilter.Per +
                                    1
                            );
                        }
                    },
                    {
                        title: this.$t('batchImport.importTime'),
                        align: 'center',
                        width: 130,
                        key: 'ImportTime',
                        sortable: 'custom',
                        render: function (h, params) {
                            if (params.row.ImportTime !== null) {
                                return h(
                                    'div',
                                    moment(params.row.ImportTime).format('YYYY-MM-DD HH:mm:ss')
                                );
                            }
                        }
                    },
                    {
                        title: this.$t('batchImport.importFile'),
                        key: 'FileName',
                        sortable: 'custom',
                        minWidth: 140,
                        render: (h, params) => {
                            return h('div', [
                                h(
                                    'a',
                                    {
                                        on: {
                                            click: () => {
                                                console.log(params.row.FullSourceFilePath);
                                                this.publicJS.DownloadFile(params.row.FullSourceFilePath);
                                            }
                                        }
                                    },
                                    params.row.FileName
                                )
                            ]);
                        }
                    },
                    {
                        title: this.$t('batchImport.totalCount'),
                        key: 'TotalCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 90
                    },
                    {
                        title: this.$t('batchImport.rightCount'),
                        key: 'RightCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 90
                    },
                    {
                        title: this.$t('batchImport.differentCount'),
                        key: 'DifferentCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 90
                    },
                    {
                        title: this.$t('batchImport.repeatCount'),
                        key: 'RepeatCount',
                        align: 'center',
                        sortable: 'custom',
                        width: 90
                    },
                    {
                        title: this.$t('batchImport.importAbnormalData'),
                        minWidth: 140,
                        key: 'ErrorFileName',
                        sortable: 'custom',
                        render: (h, params) => {
                            return h(
                                'a',
                                {
                                    on: {
                                        click: () => {
                                            console.log(params.row.FullErrorFilePath);
                                            this.publicJS.DownloadFile(params.row.FullErrorFilePath);
                                        }
                                    }
                                },
                                params.row.ErrorFileName
                            );
                        }
                    }
                ]
            };
        },
        methods: {
            // 上传
            initImportReceiverUpdateConfig () {
                this.templateUrl = this.importTemplateFile;
                this.uploadReceiverUpdateAction =
                    this.$http.defaults.baseURL + '/CommonImport/BatchImportDataFromXlsx?ImportTable=' + this.importTable;
            },

            // 附件格式错误
            handleFormatError (error, file, fileList) {
                this.visible = false;
                this.$Message.warning(this.$t('batchImport.uploadFormatXMLError'));
            },
            // 附件大小验证
            handleMaxSize (file, fileList) {
                this.visible = false;
                this.$Message.warning(this.$t('batchImport.uploadMaxSizeError'));
            },
            // 附件上传前
            handleBeforeUpload (file) {
                this.fileAllData.name = file.name
                this.fileAllData.size = file.size
                this.visible = true;
            },
            // 附件上传返回错误不走统一拦截处理
            handleUploadError (error, file, fileList) {
                this.visible = false;
                this.publicJS.handleUploadErrorPublic(file, this.$t('batchImport.uploadError'));
            },
            // 附件上传成功
            handleSuccess (res, file, fileList) {
                this.visible = false;
                this.$Message.success(this.$t('system.uploadSuccess'));
                this.$refs.uploadQualification.clearFiles();
                this.$emit('handleFlagChange', true);
                this.handleSearch();
            },
            handleSearch () {
                this.importLogFilter.Page = 1;
                this.queryImportReceiverUpdateLog(this.importLogFilter);
            },
            queryImportReceiverUpdateLog (importLogFilter) {
                this.$http
                    .get('/CommonImport/QueryImportLog', {
                        params: importLogFilter
                    })
                    .then(response => {
                        this.importLogList = response.data.Models;
                        this.importLogTotalCount = response.data.TotalCount;
                });
            },
            // 上传记录翻页
            changeImportLogPage (value) {
                this.importLogFilter.Page = value;
                this.queryImportReceiverUpdateLog(this.importLogFilter);
            },
            // 上传记录调整单页显示数
            changeImportLogPageSize (value) {
                this.importLogFilter.Per = value;
                this.importLogFilter.Page = 1;
                this.queryImportReceiverUpdateLog(this.importLogFilter);
            },
            // 上传记录排序
            handleImportLogSortChange (column) {
                this.importLogFilter.OrderBy = [`${column.key} ${column.order}`];
                this.queryImportReceiverUpdateLog(this.importLogFilter);
            },
            // 取消，关闭
            handlecancel () {
                this.importLogFilter = {
                    Page: 1,
                    Per: 10,
                    ImportTime: null
                }
                this.$emit('batchImportClose');
            },
            changeDatepicker (dateArr) {
                this.importLogFilter.ImportTime = dateArr;
            }
        },
        watch: {
            isLoad (val) {
                if (val) {
                    this.initImportReceiverUpdateConfig();
                    this.handleSearch();
                }
            }
        }
    };
</script>
 <style  scoped>
.uploadListA {
  display: block;
  height: 32px;
  line-height: 32px;
}
</style>
