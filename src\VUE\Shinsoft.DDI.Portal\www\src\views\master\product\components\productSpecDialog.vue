<!--
/**
 * 产品规格管理对话框组件
 * 支持新增和编辑功能
 * 根据传入的recordId判断是新增还是编辑模式
 * 采用两栏式设计，优化用户体验
 */
-->
<template>
  <el-dialog
    :title="isEdit ? '编辑规格' : '新增规格'"
    v-model="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="productSpecFormRef"
      :model="productSpecForm"
      :rules="formRules"
      label-width="120px"
    >
      <!-- 第一行：产品选择 -->
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="产品" prop="productId">
            <el-cascader
              v-model="cascaderValue"
              :options="manufacturerProductOptions"
              :props="cascaderProps"
              placeholder="请选择药企和产品"
              style="width: 100%"
              @change="handleCascaderChange"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：规格编码和规格 -->
      <el-row :gutter="16">
        <!-- 规格编码 - 仅编辑时显示且不可编辑 -->
        <el-col :span="12" v-if="isEdit">
          <el-form-item label="规格编码" prop="code">
            <el-input v-model="productSpecForm.code" placeholder="请输入规格编码" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="isEdit ? 12 : 24">
          <el-form-item label="规格" prop="spec">
            <el-input v-model="productSpecForm.spec" placeholder="请输入规格，如：100mg*30片" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：单位和剂型 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="productSpecForm.unit" placeholder="请输入单位，如：盒、瓶" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="剂型" prop="dosageFormId">
            <el-select v-model="productSpecForm.dosageFormId" placeholder="请选择剂型" style="width: 100%">
              <el-option
                v-for="dosageForm in dosageFormList"
                :key="dosageForm.id"
                :label="dosageForm.name"
                :value="dosageForm.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：生产厂家和分型 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="生产厂家" prop="pharmaceuticalFactory">
            <el-input v-model="productSpecForm.pharmaceuticalFactory" placeholder="请输入生产厂家" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分型" prop="materialGroup">
            <el-input v-model="productSpecForm.materialGroup" placeholder="请输入分型，如：西药、中药" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          {{ isEdit ? '更新' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import productApi from '@/api/productApi'
import selectorApi from '@/api/selectorApi'

export default {
  name: 'productSpecDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    // 表单引用
    const productSpecFormRef = ref(null)

    // 保存加载状态
    const saveLoading = ref(false)

    // 是否为编辑模式
    const isEdit = computed(() => !!props.recordId)

    // 对话框显示状态
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 表单数据
    const productSpecForm = reactive({
      id: '',
      productId: '',
      code: '',
      spec: '',
      unit: '',
      dosageFormId: '',
      pharmaceuticalFactory: '',
      materialGroup: ''
    })

    // 级联选择器的值
    const cascaderValue = ref([])

    // 级联选择器配置
    const cascaderProps = {
      value: 'value',
      label: 'label',
      children: 'children',
      checkStrictly: false
    }

    // 药企-产品级联选择器选项
    const manufacturerProductOptions = ref([])

    // 剂型列表
    const dosageFormList = ref([])

    // 表单验证规则 - 动态规则，根据编辑模式调整
    const formRules = computed(() => ({
      productId: [
        { required: true, message: '请选择产品', trigger: 'change' }
      ],
      code: isEdit.value ? [
        { required: true, message: '请输入规格编码', trigger: 'blur' },
        { max: 50, message: '编码长度不能超过50个字符', trigger: 'blur' }
      ] : [],
      spec: [
        { required: true, message: '请输入规格', trigger: 'blur' },
        { max: 50, message: '规格长度不能超过50个字符', trigger: 'blur' }
      ],
      unit: [
        { max: 50, message: '单位长度不能超过50个字符', trigger: 'blur' }
      ],
      dosageFormId: [],
      pharmaceuticalFactory: [
        { max: 200, message: '生产厂家长度不能超过200个字符', trigger: 'blur' }
      ],
      materialGroup: [
        { max: 50, message: '分型长度不能超过50个字符', trigger: 'blur' }
      ]
    }))

    /**
     * 加载药企-产品级联选择器数据
     */
    const loadManufacturerProductOptions = async () => {       
      try {
        const response = await productApi.getProductCascader();

        if (response.data && response.data.success === true) {
          const cascaderData = response.data.data || [];
 
          manufacturerProductOptions.value = cascaderData 
        
        } else {
          console.error('获取产品级联数据失败');
          manufacturerProductOptions.value = [];
        }
      } catch (error) {
        console.error('获取产品级联数据失败:', error);
        manufacturerProductOptions.value = [];
      }
    }

    /**
     * 级联选择器变化处理
     */
    const handleCascaderChange = (value) => {
      if (value && value.length === 2) {
        productSpecForm.productId = value[1] // 产品ID
      } else {
        productSpecForm.productId = ''
      }
    }

    /**
     * 加载剂型列表
     * 从字典中读取 code 为 DosageForm 的数据
     */
    const loadDosageFormList = async () => {
      try {
        // 调用 getDicts 接口获取剂型数据
        const response = await selectorApi.getDicts('DosageForm')
        
        if (response.data && response.data.success === true) {
          dosageFormList.value = response.data.data || []
        } else {
          console.error('获取剂型数据失败')
          dosageFormList.value = []
        }
      } catch (error) {
        console.error('加载剂型数据失败:', error)
        dosageFormList.value = []
      }
    }

    /**
     * 加载记录数据（编辑模式）
     */
    const loadRecordData = async (recordId) => {
      try {
        const response = await productApi.getProductSpec(recordId)

        if (response.data && response.data.success === true) {
          const data = response.data.data

          // 填充表单数据
          Object.assign(productSpecForm, {
            id: data.id,
            productId: data.productId,
            code: data.code,
            spec: data.spec,
            unit: data.unit,
            dosageFormId: data.dosageFormId,
            pharmaceuticalFactory: data.pharmaceuticalFactory,
            materialGroup: data.materialGroup
          })

          // 设置级联选择器的值
          // 需要根据productId找到对应的药企ID和产品ID
          if (data.productId) {
            // 在级联选择器数据中查找对应的路径
            findCascaderPath(data.productId)
          }
        } else {
          let errorMessage = '获取产品规格详情失败'
          if (response.data?.messages && response.data.messages.length > 0) {
            errorMessage = response.data.messages.join('; ')
          }
          ElMessage.error(errorMessage)
        }
      } catch (error) {
        console.error('加载产品规格详情失败:', error)
        ElMessage.error('加载产品规格详情失败：' + (error.message || '网络错误'))
      }
    }

    /**
     * 根据产品ID查找级联选择器路径
     */
    const findCascaderPath = (productId) => {
      for (const manufacturer of manufacturerProductOptions.value) {
        if (manufacturer.children) {
          for (const product of manufacturer.children) {
            if (product.value === productId) {
              cascaderValue.value = [manufacturer.value, product.value]
              return
            }
          }
        }
      }
      // 如果没找到，清空级联选择器
      cascaderValue.value = []
    }

    /**
     * 重置表单
     */
    const resetForm = () => {
      Object.assign(productSpecForm, {
        id: '',
        productId: '',
        code: '',
        spec: '',
        unit: '',
        dosageFormId: '',
        pharmaceuticalFactory: '',
        materialGroup: ''
      })
      cascaderValue.value = []
      if (productSpecFormRef.value) {
        productSpecFormRef.value.resetFields()
      }
    }

    /**
     * 保存规格信息
     */
    const handleSave = () => {
      productSpecFormRef.value.validate(async (valid) => {
        if (valid) {
          saveLoading.value = true
          try { 
    
            
            if (isEdit.value) {
              // 编辑模式，确保ID已设置
              if (!productSpecForm.id) {
                productSpecForm.id = props.recordId
              }
              await productApi.editProductSpec(productSpecForm)
              ElMessage.success('规格信息更新成功')
            } else {
              // 新增模式
              await productApi.addProductSpec(productSpecForm)
              ElMessage.success('规格添加成功')
            }
            emit('success')
            handleClose()
          } catch (error) {
            console.error('保存失败:', error)
            ElMessage.error(isEdit.value ? '更新失败，请重试' : '添加失败，请重试')
          } finally {
            saveLoading.value = false
          }
        }
      })
    }

    /**
     * 关闭对话框
     */
    const handleClose = () => {
      resetForm()
      emit('update:visible', false)
    }

    // 监听对话框显示状态
    watch(() => props.visible, (newVal) => {
      if (newVal) {
        if (isEdit.value && props.recordId) {
          // 编辑模式，加载数据
          loadRecordData(props.recordId)
        } else {
          // 新增模式，重置表单
          resetForm()
        }
      }
    })

    // 组件挂载时加载下拉列表数据
    onMounted(() => {
      loadManufacturerProductOptions()
      loadDosageFormList()
    })

    return {
      productSpecFormRef,
      saveLoading,
      isEdit,
      dialogVisible,
      productSpecForm,
      formRules,
      cascaderValue,
      cascaderProps,
      manufacturerProductOptions,
      dosageFormList,
      handleCascaderChange,
      handleSave,
      handleClose,
      resetForm
    }
  }
}
</script>