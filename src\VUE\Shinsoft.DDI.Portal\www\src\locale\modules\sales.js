export const sales = {
    'zhCN': {
        quota: {
            name: '潜力名称',
            quotaStatus: '拆分状态',
            quotaMonth: '潜力月份',
            department: '部门',
            receiverName: '终端名称',
            productName: '产品',
            materialGroupName: '分型',
            tradeType: '销售类型',
            startMonth: '起始月份',
            endMonth: '截止月份',
            month: '月份',
            quantity: '潜力数量',
            stageQuota: '阶段潜力',
            receiverQuota: '终端潜力',
            editTitle: '编辑潜力',
            addTitle: '新增潜力',
            productAndMaterialGroup: '产品/分型',
            receiverNameValid: '请选择终端名称',
            nameValid: '请填写潜力名称',
            buValid: '请选择BU',
            productAndMaterialGroupValid: '请选择产品/分型',
            startMonthValid: '请选择起始月份',
            endMonthValid: '请选择截止月份',
            dateComparison: '截止月份不能小于起始月份',
            isAddTradeType: '按销售类型',
            validStageQuota: '请先生成阶段潜力',
            monthValid: '请选择月份',
            receiverQuotaValid: '请填写终端潜力',
            receiverQuotaCompleteValid: '请将终端潜力填写完整',
            quotaQuantityValid: '潜力数量应为大于等于0的数字，支持两位小数。',
            addReceiverQuotaTitle: '新增终端潜力',
            editReceiverQuotaTitle: '编辑终端潜力',
            quotaQuantityNotNull: '请填写潜力数量',
            isPotentialCustomer: '是否潜力客户',
            amount: '金额(元)',
            receiverHasExist: '终端潜力存在重复',
            addNewQuotaError: '请将信息填写完整后添加新潜力数据',
            totalAmount: '总金额(元)',
            totalQuantity: '总数量',
            totalproduct: '包含品种',
            splitQuota: '拆分潜力',
            editRegionQuota: '编辑大区潜力',
            regionQuota: '大区潜力',
            regionName: '大区',
            price: '考核价(元)',
            currentRegion: '已生成潜力大区',
            excludeRegion: '未生成潜力大区',
            changeRegion: '调整大区',
            unsplitQuantity: '未拆分数量',
            validOneRegion: '请至少添加一个大区',
            quotaQuantityLengthValid: '潜力数量不能超过9位',
            regionQuotaShow: '查看大区潜力'
        }
    },
    'enUS': {
        quota: {
            name: '潜力名称',
            quotaStatus: '拆分状态',
            quotaMonth: '潜力月份',
            department: '部门',
            receiverName: '终端名称',
            productName: '产品',
            materialGroupName: '分型',
            tradeType: '销售类型',
            startMonth: '起始月份',
            endMonth: '截止月份',
            month: '月份',
            quantity: '潜力数量',
            stageQuota: '阶段潜力',
            receiverQuota: '终端潜力',
            editTitle: '编辑潜力',
            addTitle: '新增潜力',
            productAndMaterialGroup: '产品/分型',
            receiverNameValid: '请选择终端名称',
            nameValid: '请填写潜力名称',
            buValid: '请选择BU',
            productAndMaterialGroupValid: '请选择产品/分型',
            startMonthValid: '请选择起始月份',
            endMonthValid: '请选择截止月份',
            dateComparison: '截止月份不能小于起始月份',
            isAddTradeType: '按销售类型',
            validStageQuota: '请先生成阶段潜力',
            monthValid: '请选择月份',
            receiverQuotaValid: '请填写终端潜力',
            receiverQuotaCompleteValid: '请将终端潜力填写完整',
            quotaQuantityValid: '潜力数量应为大于等于0的数字，支持两位小数。',
            addReceiverQuotaTitle: '新增终端潜力',
            editReceiverQuotaTitle: '编辑终端潜力',
            quotaQuantityNotNull: '请填写潜力数量',
            isPotentialCustomer: '是否潜力客户',
            amount: '金额(元)',
            receiverHasExist: '终端潜力存在重复',
            addNewQuotaError: '请将信息填写完整后添加新潜力数据',
            totalAmount: '总金额(元)',
            totalQuantity: '总数量',
            totalproduct: '包含品种',
            splitQuota: '拆分潜力',
            editRegionQuota: '编辑大区潜力',
            regionQuota: '大区潜力',
            regionName: '大区',
            price: '考核价(元)',
            currentRegion: '已生成潜力大区',
            excludeRegion: '未生成潜力大区',
            changeRegion: '调整大区',
            unsplitQuantity: '未拆分数量',
            validOneRegion: '请至少添加一个大区',
            quotaQuantityLengthValid: '潜力数量不能超过9位',
            regionQuotaShow: '查看大区潜力'
        }
    }
}
export default sales
