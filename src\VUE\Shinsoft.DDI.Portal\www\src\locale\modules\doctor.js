// 导航-->中英文
export const doctor = {
    'zhCN': {
        doctor: {
            doctor: '医生',
            hospital: '医院',
            name: '姓名',
            oldName: '(原始)姓名',
            title: '职称',
            doctorTitle: '医生职称',
            odlDoctorTitle: '(原始)医生职称',
            position: '职务',
            oldPosition: '(原始)职务',
            doctorPosition: '医生职务',
            level: '分级',
            doctorLevel: '医生分级',
            oldDoctorLevel: '(原始)医生分级',
            dept: '科室',
            deptDesc: '科室/部门',
            oldDeptDesc: '(原始)科室/部门',
            remark: '简介',
            oldRemark: '(原始)简介',
            mobile: '电话',
            oldMobile: '(原始)电话',
            email: 'Email',
            oldEmail: '(原始)Email',
            gender: '性别',
            oldgender: '(原始)性别',
            oldProductLine: '(原始)产品线',
            buRequired: '请选择产品线',
            receiverRequired: '请添加医生所属医院',
            approvalDialogTitle: '新增医生',
            approvalSpeakerDialogTitle: '新增讲者',
            approvalUpgradeDialogTitle: '升级讲者',
            dataManagerUpgradeDialogTitle: '内勤升级讲者',
            hospitalNameRequired: '请选择医院',
            doctorNameRequired: '请输入医生姓名',
            genderRequired: '请输入性别',
            deptRequired: '请输入科室',
            titleRequired: '请输入职称',
            positionRequired: '请输入职务',
            LevelRequired: '请选择分级',
            mobileRequired: '请输入电话',
            emailRequired: '请输入邮箱地址',
            eMailFormat: '请检查邮箱格式',
            remarkLength: '备注信息过长',
            isDoctor: '是否是医生',
            addReceiver: '新增医院',
            existHospital: '已存在相同的医院',
            editApprovalDialogTitle: '编辑医生/讲者审批',
            detailApprovalDialogTitle: '编辑医生/讲者明细',
            changeTyep: '变更方式',
            mainReceiver: '设为主机构',
            IsMainReceiver: '是否主机构',
            whetherStop: '是否停用该数据，合并至新的讲者/医生?',
            deactivateDoctor: '医生/讲者停用并转换',
            targetDoctorName: '目标医生/讲者',
            doctorOrSpeakerName: '医生/讲者名称',
            targetDoctorNameNotEmpty: '目标医生/讲者不能为空',
            targetDoctorSaveSuccess: '保存成功',
            addSpeakerDialogTitle: '内勤新增讲者',
            addDoctorDialogTitle: '内勤新增医生',
            editSpeakerDialogTitle: '内勤编辑讲者',
            editDoctorDialogTitle: '内勤编辑医生',
            import: '批量导入',
            doctorTemplateDownload: '医生模板下载',
            speakerTemplateDownload: '讲者模板下载',
            doctorSpeakerTemplateDownload: '医生&讲者模板下载',
            importType: '导入类型',
            receiverProvince: '省份',
            mainReceiverName: '主机构',
            importChange: '批量变更',
            productLineLevel: '产品线-分级',
            editOutSideSpeakerDialogTitle: '外勤编辑讲者',
            editOutSideDoctorDialogTitle: '外勤编辑医生',
            outsideReceiverRequired: '请添加机构',
            doctorInfo: '医生信息'
        },
        speaker: {
            speaker: '讲者',
            dept: '部门',
            speakerTitle: '讲者职称',
            oldSpeakerTitle: '(原始)讲者职称',
            organization: '机构',
            speakerLevel: '讲者分级',
            oldSpeakerLevel: '(原始)讲者分级',
            isSpeaker: '是否是讲者',
            deptRequired: '请输入部门',
            addReceiver: '新增机构',
            mainReceiverRequired: '请设置主机构',
            academicPositions: '学术职务',
            academicTitle: '学术职称',
            speechAbility: '演讲能力',
            education: '学历',
            experience: '资格经验',
            speakerNameRequired: '请输入讲者姓名',
            speakerReceiverRequired: '请添加讲者所属医院',
            MSLKOL: 'MSL关键客户',
            MMKOL: 'MM关键客户',
            selectedReceiver: '选择机构',
            productLinetAndLevel: '产品线及分级',
            addProductLineAndLevel: '添加产品线及分级',
            productLineAndLevelRequired: '产品线及分级信息不可为空',
            speakerLevelRequired: '讲者分级不可为空',
            productLineAndDoctorLevelRequired: '产品线及医生分级信息不可为空',
            speakerInfo: '讲者信息',
            oldAcademicPositions: '(原始)学术职务',
            oldAcademicTitle: '(原始)学术职称',
            oldSpeechAbility: '(原始)演讲能力',
            oldEducation: '(原始)学历',
            oldExperience: '(原始)资格经验',
            oldMSLKOL: '(原始)MSL关键客户',
            oldMMKOL: '(原始)MM关键客户'
        }
    },
    'enUS': {
        doctor: {
            doctor: '医生',
            hospital: '医院',
            name: '姓名',
            oldName: '(原始)姓名',
            title: '职称',
            doctorTitle: '医生职称',
            odlDoctorTitle: '(原始)医生职称',
            position: '职务',
            oldPosition: '(原始)职务',
            doctorPosition: '医生职务',
            level: '分级',
            doctorLevel: '医生分级',
            oldDoctorLevel: '(原始)医生分级',
            dept: '科室',
            deptDesc: '科室/部门',
            oldDeptDesc: '(原始)科室/部门',
            remark: '简介',
            oldRemark: '(原始)简介',
            mobile: '电话',
            oldMobile: '(原始)电话',
            email: 'Email',
            oldEmail: '(原始)Email',
            gender: '性别',
            oldgender: '(原始)性别',
            oldProductLine: '(原始)产品线',
            buRequired: '请选择产品线',
            receiverRequired: '请添加医生所属医院',
            approvalDialogTitle: '新增医生',
            approvalSpeakerDialogTitle: '新增讲者',
            approvalUpgradeDialogTitle: '升级讲者',
            dataManagerUpgradeDialogTitle: '内勤升级讲者',
            hospitalNameRequired: '请选择医院',
            doctorNameRequired: '请输入医生姓名',
            genderRequired: '请输入性别',
            deptRequired: '请输入科室',
            titleRequired: '请输入职称',
            positionRequired: '请输入职务',
            LevelRequired: '请选择分级',
            mobileRequired: '请输入电话',
            emailRequired: '请输入邮箱地址',
            eMailFormat: '请检查邮箱格式',
            remarkLength: '备注信息过长',
            isDoctor: '是否是医生',
            addReceiver: '新增医院',
            existHospital: '已存在相同的医院',
            editApprovalDialogTitle: '编辑医生/讲者审批',
            detailApprovalDialogTitle: '编辑医生/讲者明细',
            changeTyep: '变更方式',
            mainReceiver: '设为主机构',
            whetherStop: '是否停用该数据，合并至新的讲者/医生?',
            deactivateDoctor: '医生/讲者停用并转换',
            targetDoctorName: '目标医生/讲者',
            doctorOrSpeakerName: '医生/讲者名称',
            targetDoctorNameNotEmpty: '目标医生/讲者不能为空',
            targetDoctorSaveSuccess: '保存成功',
            addSpeakerDialogTitle: '内勤新增讲者',
            addDoctorDialogTitle: '内勤新增医生',
            editSpeakerDialogTitle: '内勤编辑讲者',
            editDoctorDialogTitle: '内勤编辑医生',
            import: '批量导入',
            doctorTemplateDownload: '医生模板下载',
            speakerTemplateDownload: '讲者模板下载',
            doctorSpeakerTemplateDownload: '医生&讲者模板下载',
            importType: '导入类型',
            receiverProvince: '省份',
            mainReceiverName: '主机构',
            importChange: '批量变更',
            productLineLevel: '产品线-分级',
            editOutSideSpeakerDialogTitle: '外勤编辑讲者',
            editOutSideDoctorDialogTitle: '外勤编辑医生',
            outsideReceiverRequired: '请添加机构',
            doctorInfo: '医生信息'
        },
        speaker: {
            speaker: '讲者',
            dept: '部门',
            speakerTitle: '讲者职称',
            oldSpeakerTitle: '(原始)讲者职称',
            organization: '机构',
            speakerLevel: '讲者分级',
            oldSpeakerLevel: '(原始)讲者分级',
            isSpeaker: '是否是讲者',
            deptRequired: '请输入部门',
            addReceiver: '新增机构',
            mainReceiverRequired: '请设置主机构',
            academicPositions: '学术职务',
            academicTitle: '学术职称',
            speechAbility: '演讲能力',
            education: '学历',
            experience: '资格经验',
            speakerNameRequired: '请输入讲者姓名',
            speakerReceiverRequired: '请添加讲者所属医院',
            MSLKOL: 'MSL关键客户',
            MMKOL: 'MM关键客户',
            selectedReceiver: '选择机构',
            productLinetAndLevel: '产品线及分级',
            addProductLineAndLevel: '添加产品线及分级',
            productLineAndLevelRequired: '产品线及分级信息不可为空',
            speakerLevelRequired: '讲者分级不可为空',
            productLineAndDoctorLevelRequired: '产品线及医生分级信息不可为空',
            speakerInfo: '讲者信息',
            oldAcademicPositions: '(原始)学术职务',
            oldAcademicTitle: '(原始)学术职称',
            oldSpeechAbility: '(原始)演讲能力',
            oldEducation: '(原始)学历',
            oldExperience: '(原始)资格经验',
            oldMSLKOL: '(原始)MSL关键客户',
            oldMMKOL: '(原始)MM关键客户'
        }
    }
}
export default doctor
