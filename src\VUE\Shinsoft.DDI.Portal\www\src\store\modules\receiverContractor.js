import http from '../../utils/axios'
const receiverContractor = {
    state: {
        // 联系人变更申请中变更列表
        contractorChangeList: [],
        // 联系人变更申请列表
        contractorList: []
    },
    mutations: {
        RefurbishContractorAdd (state, data) {
            state.contractorChangeList = [...state.contractorChangeList, data]
        },
        SetContractorList (state, data) {
            state.contractorList = data
        },
        SetContractorChangeList (state, data) {
            state.contractorChangeList = data
        }
    },
    actions: {

        queryRequestContractorChange ({commit}, params) {
            http.get('/Receiver/QueryContractorChangeRequestByRequestFormID', {params: params}).then(function (response) {
                commit('SetContractorChangeList', response.data)
            })
        },
        queryReceiverContractor ({commit}, params) {
            http.get('/Receiver/QueryReceiverContractorByDistributorID', {params: params}).then(function (response) {
                commit('SetContractorList', response.data)
            })
        }
    }
}
export default receiverContractor
