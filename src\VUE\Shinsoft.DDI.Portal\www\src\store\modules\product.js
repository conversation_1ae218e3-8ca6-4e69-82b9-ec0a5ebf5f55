import http from '../../utils/axios'
import { ElMessage } from 'element-plus'
const product = {
    state: {
        productlist: [],
        totalcount: 1,
        bu: [],
        productLine: [],
        brand: [],
        buCascader: [],
        editProduct: null,
        productAnotherList: [],
        productTotal: 1,
        defaultProductBatchNumber: null,
        productCategory: []
    },
    mutations: {
        QueryProduct (state, data) {
            state.productlist = data.Models
            state.totalcount = data.TotalCount
        },
        QueryBU (state, data) {
            state.bu = data
        },
        QueryBUCascader (state, data) {
            state.buCascader = data
        },
        GetProduct (state, data) {
            state.editProduct = data
        },
        QueryProductAnother (state, data) {
            state.productAnotherList = data.Models
            state.productTotal = data.TotalCount
        },
        RefurbishBatchNumber (state, data) {
            state.defaultProductBatchNumber = data + '_Default'
        },
        QueryProductCategory (state, data) {
            state.productCategory = data
        }
    },
    actions: {
        queryProduct ({
            commit
        }, params) {
            http.get('/Product/QueryProduct', {
                params: params
            }).then(response =>
                commit('QueryProduct', response.data)
            )
        },
        queryBUCascader ({
            commit
        }) {
            http.get('/Product/GetBUCascader').then(response =>
                commit('QueryBUCascader', response.data)
            )
        },
        getProduct ({
            commit
        }, params) {
            http.get('/Product/GetProduct', {
                params: params
            }).then(response =>
                commit('GetProduct', response.data)
            )
        },
        queryProductAnother ({
            commit
        }, params) {
            http.get('/Product/QueryProductAnother', {
                params: params
            }).then(response =>
                commit('QueryProductAnother', response.data)
            )
        },
        queryProductCategory ({
            commit
        }) {
            http.get('/Product/QueryProductCategory').then(function (response) {
                commit('QueryProductCategory', response.data)
            })
        }
    }
}
export default product
