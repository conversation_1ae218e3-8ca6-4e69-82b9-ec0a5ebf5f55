
// 系统管理-->人员和权限-->中英文
export const permission = {
    'zhCN': {
        userlist: {
            muid: '员工ID',
            loginName: '用户名',
            depart: '离职',
            entry: '入职',
            searchname: '姓名',
            employeeStatus: '员工状态',
            userType: '用户类型',
            gender: '性别',
            position: '职务',
            status: '状态',
            viewUerInformation: '查看用户信息',
            department: '部门',
            addUser: '新增CDMS用户',
            cdmsUser: 'CDMS用户',
            email: '邮箱',
            telephone: '电话',
            lineManagerMUID: '直线上级',
            entryDate: '入职日期',
            lastWorkDay: '离职日期',
            synchronizationDate: '同步时间',
            roleDesc: '所属角色',
            updatePassword: '修改密码',
            oldPassword: '原始密码',
            newPassword: '新密码',
            pwdLevel: '密码强度',
            confirmPassword: '确认新密码',
            setEmployeeDepart: '是否将该员工设置为离职状态？确认后权限会一并删除。',
            setEmployeeNormal: '是否将该员工设置为在职状态？确认后请分配权限。',
            resetPassword: '是否重置密码？'
        },
        userEdit: {
            title: '编辑员工',
            male: '男',
            female: '女',
            validmuid: '员工ID不能为空',
            validLoginName: '用户名不能为空',
            validNameCn: '姓名不能为空',
            validOldPassword: '请填写原始密码',
            validNewPassword: '请填写新密码',
            validConfirmPassword: '请填写确认新密码',
            validPasswordCompare: '确认新密码与新密码不一致',
            validPasswordRules: '您的新密码复杂度太低（密码中必须包含字母、数字、特殊字符，至少8个字符，最多30个字符），请修改新密码！'
        },
        permission: {
            receiverName: '流向商业/终端名称',
            onStationDate: '挂岗时间',
            stationName: '岗位名称',
            employName: '人员名称',
            startDate: '挂岗起始时间',
            endDate: '挂岗截止时间',
            departureDate: '离岗时间',
            beginDateValid: '挂岗起始时间不能为空',
            endDateValid: '挂岗截止时间不能为空',
            beginDateGreater: '挂岗起始时间不能大于挂岗截止时间',
            endDateLess: '挂岗截止时间不能小于挂岗起始时间',
            modalTitle: '编辑挂岗日期'
        },
        role: {
            isDataManager: '是否数据管理员',
            DataManagerCanNotApproval: '审批树角色不能同时为数据管理员'
        }
    },
    'enUS': {
        userlist: {
            muid: 'User ID',
            loginName: 'Login Name',
            depart: 'Depart',
            searchname: 'Name',
            employeeStatus: 'Employee Status',
            userType: 'User Type',
            gender: 'Gender',
            position: 'Position',
            status: 'Status',
            viewUerInformation: 'View user information',
            department: 'Department',
            addUser: 'Add User',
            cdmsUser: 'CDMS User',
            email: 'Email',
            telephone: 'Telephone',
            lineManagerMUID: 'Line Manager',
            entryDate: 'HireDate',
            lastWorkDay: 'LeaveDate',
            synchronizationDate: 'SynchronizationDate',
            roleDesc: 'User role',
            updatePassword: 'Update password',
            newPassword: 'New password',
            confirmPassword: 'Confirm password',
            setEmployeeDepart: 'Do you want to set the employee as in resignation status?',
            setEmployeeNormal: 'Do you want to set the employee as in normal status?',
            resetPassword: 'Do you want to reset the password'
        },
        userEdit: {
            title: 'Edit Employee',
            male: 'Male',
            female: 'Female',
            validLoginName: 'The login name can not be empty',
            validNameCn: 'The name can not be empty',
            validOldPassword: 'The old password can not be empty',
            validNewPassword: 'The new password can not be empty',
            validConfirmPassword: 'The confirm password can not be empty',
            validPasswordCompare: 'Confirm that the new password must match the new password',
            validPasswordRules: 'Your new password has too low complexity (it must contain letters, numbers, special characters, at least 8 characters, and at most 30 characters). Please modify your new password!'
        },
        permission: {
            receiverName: '流向商业/终端名称',
            onStationDate: '挂岗时间',
            stationName: '岗位名称',
            employName: '人员名称',
            startDate: '挂岗起始时间',
            endDate: '挂岗截止时间',
            departureDate: '离岗时间',
            beginDateValid: '挂岗起始时间不能为空',
            endDateValid: '挂岗截止时间不能为空',
            beginDateGreater: '挂岗起始时间不能大于挂岗截止时间',
            endDateLess: '挂岗截止时间不能小于挂岗起始时间',
            modalTitle: '编辑挂岗日期'
        },
        role: {
            isDataManager: '是否数据管理员',
            DataManagerCanNotApproval: '审批树角色不能同时为数据管理员'
        }
    }
}
export default permission
