// 产品权限组-->中英文
export const system = {
    'zhCN': {
        productGroup: {
            groupName: '产品权限组',
            addModalTitle: '产品权限组管理',
            groupShortName: '产品简称',
            productList: '产品列表',
            checkAll: '全选',
            add: '新增',
            nameCnBeEmpty: '类型不能为空',
            nameCnLength: '类型名称过长',
            shortNameLength: '简称过长',
            nameEnLength: '英文名称过长',
            // 角色管理
            roleListCode: '角色编码',
            roleListName: '角色名称',
            roleListEnumRoleType: '审批树角色',
            HasDataManagerRole: '是否拥有数据管理权限',
            roleListAddRoleList: '角色管理',
            roleListViewingPersonnel: '查看人员',
            role: '角色',
            roleDescription: '角色描述',
            roleFullName: '姓名',
            checkRolesOfStaff: '查看角色所属人员',
            roleDepartment: '部门',
            personnel: '人员',
            addPersonnel: '添加人员',
            region: '区域',
            product: '产品',
            distributor: '流向商业',
            addDistributor: '添加流向商业',
            addApprovalTreeTitle: '新增审批节点',
            editApprovalTreeTitle: '编辑审批节点',
            provinceName: '省份',
            flowToBusinessName: '流向商业名称',
            nodePersonnel: '当前节点存在人员，不可以删除',
            nodeSubnode: '当前节点存在子节点，不可以删除',
            nodeflowToBusiness: '当前节点存在流向商业，不可以删除',
            nodeflowToReceiver: '当前节点存在收货方，不可以删除',
            whetherNode: '是否将当前节点移除',
            unselected: '没有选中当前项，不可以删除',
            whetherflowToBusiness: '是否将流向商业从当前节点移除',
            doYouWantRemoveReceiver: '是否将收货方从当前节点移除',
            whetherPreparatoryDistributorToBusiness: '是否将可追溯商业从当前节点移除',
            whetherPersonnel: '是否将人员从当前节点移除',
            nodeName: '节点名称',
            nodeDescription: '节点描述',
            dataPermissions: '数据权限',
            nodeRegion: '地域',
            nodeRegionRequired: '（地域为必选项）',
            productRequired: '（产品组或者产品/分型为必选项）',
            nodeNameBeEmpty: '节点名称为必填项',
            nodeNameLength: '节点名称长度过长',
            nodeDescriptionLength: '节点描述长度过长',
            roleListNameBeEmpty: '角色名称为必选项',
            nodeTips: '当前节点存在下级节点，该操作会影响下级节点的数据范围，是否确认执行此操作',
            hasReceivers: '当前节点下移除的区域所包含的的流向商业也会移除',
            chooseDistributor: '选择流向商业',
            choosePreparatoryDistributor: '选择可追溯商业',
            thisPersonClear: '该人员还被分配了其他角色，是否需要清除掉其他角色？',
            userCDMS: 'CDMS用户',
            groupNameBeEmpty: '权限组名称不能为空',
            groupNameLength: '权限组名称过长',
            groupNameProductID: '未选择任何产品，请重新选择需要添加的产品。',
            whetherRoleListEnumRoleType: '是否审批树角色',
            functionList: '功能',
            roleCodeBeEmpty: '角色编码不能为空',
            roleCodeLength: '角色编码过长',
            roleNameBeEmpty: '角色名称不能为空',
            roleNameLength: '角色名称过长',
            roleDescriptionLength: '角色描述过长',
            roleEnumRoleType: '请选择是否审批树角色',
            selectFunction: '请选择功能。',
            saveRoleSuccessfully: '保存角色成功。',
            roleHints: '该角色下已有员工，对权限进行调整将影响到员工权限，是否确认变更?',
            selectRole: '选择角色',
            assignedRole: '已分配角色',
            deleteRole: '删除角色',
            configuringDataPermissions: '配置数据权限',
            addRole: '添加角色',
            deselected: '确定要对当前用户移除该角色吗',
            removeRoles: '移除角色',
            configurationRole: '配置角色',
            isAllProduct: '是否全产品',
            addRoleTag: '添加标签',
            roleTagLabel: '标签',
            selectedTags: '已选标签',
            wordlimit: '最多输入10个字',
            contentHint: '输入标签内容点击添加按钮即可添加标签',
            preparatoryDistributor: '可追溯商业',
            addPreparatoryDistributor: '添加可追溯商业',
            preparatoryDistributorName: '可追溯商业名称',
            dataRightsManagement: '数据权限管理',
            saveDataPermissionSuccessfully: '保存数据权限成功',
            whetherToSaveDataPermissions: '是否保存数据权限',
            MUID: '员工ID',
            removeAllDistributor: '移除全部流向商业',
            removeAllHospital: '移除全部医院',
            batchAddDistributor: '批量添加流向商业',
            batchAddHospital: '批量添加医院',
            addHospital: '添加医院',
            removeCheckedHospital: '移除选中医院',
            removeCheckedDistributor: '移除选中流向商业',
            distributorRequired: '请选择流向商业'
        }
    },
    'enUS': {
        productGroup: {
            groupName: 'Product Group',
            addModalTitle: 'Product Group',
            groupShortName: 'Product',
            productList: 'Product',
            checkAll: 'All',
            add: 'Add',
            nameCnBeEmpty: 'Required',
            nameCnLength: 'Name is too long.',
            shortNameLength: 'Short name is too long.',
            nameEnLength: 'English name is too long.',
            // 角色管理
            roleListCode: 'Role Code',
            roleListName: 'Role Name',
            roleListEnumRoleType: 'Hierarchy Role',
            HasDataManagerRole: 'Has DataManager Permission',
            roleListAddRoleList: 'Edit Role',
            roleListViewingPersonnel: 'Member',
            role: 'Role',
            roleDescription: 'Description',
            roleFullName: 'Name',
            checkRolesOfStaff: 'Member',
            roleDepartment: 'Department',
            personnel: 'Member',
            addPersonnel: 'Add Member',
            region: 'Area',
            product: 'Product',
            distributor: ' Distributor',
            addDistributor: 'Add',
            addApprovalTreeTitle: 'Add Node',
            editApprovalTreeTitle: 'Edit Node',
            provinceName: 'Province',
            flowToBusinessName: 'Distributor Name',
            nodePersonnel: 'The current node contains members and can not be deleted.',
            nodeSubnode: 'The current node contains child nodes and can not be deleted.',
            nodeflowToBusiness: 'The current node contains distributors and can not be deleted.',
            nodeflowToReceiver: 'The current node contains receivers and can not be deleted.',
            whetherNode: 'Are you sure you want to delete it?',
            unselected: 'Please choose the data to be deleted.',
            whetherPreparatoryDistributorToBusiness: 'Whether to remove traceable commerce from the current node?',
            whetherflowToBusiness: 'Are you sure you want to remove this distributor form current node?',
            doYouWantRemoveReceiver: 'Are you sure you want to remove this receiver form current node?',
            whetherPersonnel: 'Do you want to remove this member from the current node?',
            nodeName: 'Name',
            nodeDescription: 'Description',
            dataPermissions: 'Data Authority',
            nodeRegion: 'Province',
            nodeRegionRequired: 'Required',
            productRequired: 'Required',
            nodeNameBeEmpty: 'Required',
            nodeNameLength: 'Name is too long.',
            nodeDescriptionLength: 'Description is too long.',
            roleListNameBeEmpty: 'Required',
            nodeTips: 'The current node has child nodes, which affect the data scope of the child nodes and confirm whether the operation is performed.',
            hasReceivers: 'The Distributor that is removed from the current node will also be removed.',
            chooseDistributor: 'Choose Distributor',
            choosePreparatoryDistributor: 'Choose Preparatory Distributor',
            thisPersonClear: 'This person has been assigned other roles. Do you want to clear other roles??',
            userCDMS: 'CDMS User',
            groupNameBeEmpty: 'Required',
            groupNameLength: 'ProductGroup name is too long.',
            groupNameProductID: 'Please choose the product to be added.',
            whetherRoleListEnumRoleType: 'Hierarchy Role',
            functionList: 'Authority',
            roleCodeBeEmpty: 'Required',
            roleCodeLength: 'Too long role coding',
            roleNameBeEmpty: 'Required',
            roleNameLength: 'Role name is too long.',
            roleDescriptionLength: 'Role description is too long.',
            roleEnumRoleType: 'Required',
            selectFunction: 'Required',
            saveRoleSuccessfully: 'Success',
            roleHints: 'Members are already included in this role. Adjusting their permissions will affect their permissions. Are you sure you want to make changes?',
            selectRole: 'Choose Role',
            assignedRole: 'Assigned Role',
            deleteRole: 'Delete roles',
            configuringDataPermissions: 'Configuring data permissions',
            addRole: 'Add role',
            deselected: 'Are you sure you want to remove this role from the current user?',
            removeRoles: 'Remove Roles',
            configurationRole: 'Configuration role',
            isAllProduct: 'Whether the whole product',
            addRoleTag: 'Add labels :',
            roleTagLabel: 'Label :',
            selectedTags: 'Selected Tags',
            wordlimit: 'Enter up to 10 words',
            contentHint: 'Enter the label content and click the Add button to add the label.',
            preparatoryDistributor: 'Preparatory Distributor',
            addPreparatoryDistributor: 'Add Preparatory Distributor',
            preparatoryDistributorName: 'Preparatory Distributor Name',
            dataRightsManagement: 'Data rights management',
            whetherToSaveDataPermissions: 'Whether to save data permissions',
            saveDataPermissionSuccessfully: 'Save data permission successfully',
            MUID: 'User ID',
            removeAllDistributor: '移除全部流向商业',
            removeAllHospital: '移除全部医院',
            batchAddDistributor: '批量添加流向商业',
            batchAddHospital: '批量添加医院',
            addHospital: '添加医院',
            removeCheckedHospital: '移除选中医院',
            removeCheckedDistributor: '移除选中流向商业',
            distributorRequired: '请选择流向商业'
        }
    }
}
export default system
