/**
 * iView到Element Plus组件映射
 * 保持原有API和样式风格
 */

// 组件名称映射
export const componentMapping = {
  // 布局组件
  'Row': 'ElRow',
  'Col': 'ElCol',
  'Layout': 'ElContainer',
  'Header': 'ElHeader',
  'Content': '<PERSON><PERSON>ain',
  'Footer': 'ElFooter',
  'Sider': 'ElAside',
  
  // 表单组件
  'Form': 'ElForm',
  'FormItem': 'ElFormItem',
  'Input': 'ElInput',
  'Button': 'ElButton',
  'ButtonGroup': 'ElButtonGroup',
  'Select': 'ElSelect',
  'Option': 'ElOption',
  'OptionGroup': 'ElOptionGroup',
  'Checkbox': 'ElCheckbox',
  'CheckboxGroup': 'ElCheckboxGroup',
  'Radio': 'ElRadio',
  'RadioGroup': 'ElRadioGroup',
  'Switch': 'ElSwitch',
  'DatePicker': 'ElDatePicker',
  'TimePicker': 'ElTimePicker',
  'Cascader': 'ElCascader',
  'InputNumber': 'ElInputNumber',
  'Rate': 'ElRate',
  'Upload': 'ElUpload',
  
  // 数据展示组件
  'Table': 'ElTable',
  'TableColumn': 'ElTableColumn',
  'Tag': 'ElTag',
  'Progress': 'ElProgress',
  'Tree': 'ElTree',
  'Pagination': 'ElPagination',
  'Badge': 'ElBadge',
  'Avatar': 'ElAvatar',
  'Card': 'ElCard',
  'Collapse': 'ElCollapse',
  'CollapsePanel': 'ElCollapseItem',
  'Carousel': 'ElCarousel',
  'CarouselItem': 'ElCarouselItem',
  
  // 导航组件
  'Menu': 'ElMenu',
  'MenuItem': 'ElMenuItem',
  'Submenu': 'ElSubmenu',
  'MenuGroup': 'ElMenuItemGroup',
  'Breadcrumb': 'ElBreadcrumb',
  'BreadcrumbItem': 'ElBreadcrumbItem',
  'Dropdown': 'ElDropdown',
  'DropdownMenu': 'ElDropdownMenu',
  'DropdownItem': 'ElDropdownItem',
  'Steps': 'ElSteps',
  'Step': 'ElStep',
  'Tabs': 'ElTabs',
  'TabPane': 'ElTabPane',
  
  // 反馈组件
  'Alert': 'ElAlert',
  'Loading': 'ElLoading',
  'Message': 'ElMessage',
  'MessageBox': 'ElMessageBox',
  'Notification': 'ElNotification',
  'Popover': 'ElPopover',
  'Tooltip': 'ElTooltip',
  'Popconfirm': 'ElPopconfirm',
  
  // 其他组件
  'Modal': 'ElDialog',
  'Drawer': 'ElDrawer',
  'Divider': 'ElDivider',
  'BackTop': 'ElBacktop',
  'Spin': 'ElLoading',
  'Anchor': 'ElAnchor',
  'AnchorLink': 'ElAnchorLink',
}

// 属性名称映射
export const propMapping = {
  // Modal -> Dialog
  'v-model': 'v-model',
  'title': 'title',
  'width': 'width',
  'mask-closable': 'close-on-click-modal',
  'closable': 'show-close',
  'ok-text': 'confirm-button-text',
  'cancel-text': 'cancel-button-text',
  
  // Table相关
  'data': 'data',
  'columns': 'columns', // 需要转换为el-table-column
  'loading': 'v-loading',
  'border': 'border',
  'stripe': 'stripe',
  'size': 'size',
  
  // Form相关
  'model': 'model',
  'rules': 'rules',
  'label-width': 'label-width',
  'inline': 'inline',
  
  // Button相关
  'type': 'type',
  'size': 'size',
  'loading': 'loading',
  'disabled': 'disabled',
  'icon': 'icon',
  'ghost': 'plain',
  
  // Input相关
  'placeholder': 'placeholder',
  'clearable': 'clearable',
  'disabled': 'disabled',
  'readonly': 'readonly',
  'maxlength': 'maxlength',
  'show-word-limit': 'show-word-limit',
  
  // Select相关
  'multiple': 'multiple',
  'filterable': 'filterable',
  'remote': 'remote',
  'remote-method': 'remote-method',
  'loading': 'loading',
  
  // 分页相关
  'total': 'total',
  'current': 'current-page',
  'page-size': 'page-size',
  'page-size-opts': 'page-sizes',
  'show-total': 'layout',
  'show-sizer': 'layout',
  'show-elevator': 'layout',
}

// 事件名称映射
export const eventMapping = {
  // 通用事件
  'on-click': 'click',
  'on-change': 'change',
  'on-input': 'input',
  'on-focus': 'focus',
  'on-blur': 'blur',
  
  // Modal/Dialog事件
  'on-ok': 'confirm',
  'on-cancel': 'close',
  'on-visible-change': 'opened',
  
  // Table事件
  'on-selection-change': 'selection-change',
  'on-sort-change': 'sort-change',
  'on-row-click': 'row-click',
  'on-row-dblclick': 'row-dblclick',
  
  // Form事件
  'on-submit': 'submit',
  'on-reset': 'reset',
  'on-validate': 'validate',
  
  // 分页事件
  'on-change': 'current-change',
  'on-page-size-change': 'size-change',
}

// 样式类名映射
export const classMapping = {
  'ivu-': 'el-',
  'ivu-row': 'el-row',
  'ivu-col': 'el-col',
  'ivu-button': 'el-button',
  'ivu-input': 'el-input',
  'ivu-table': 'el-table',
  'ivu-form': 'el-form',
  'ivu-card': 'el-card',
  'ivu-modal': 'el-dialog',
  'ivu-select': 'el-select',
  'ivu-option': 'el-option',
  'ivu-checkbox': 'el-checkbox',
  'ivu-radio': 'el-radio',
  'ivu-switch': 'el-switch',
  'ivu-tabs': 'el-tabs',
  'ivu-tab-pane': 'el-tab-pane',
  'ivu-menu': 'el-menu',
  'ivu-menu-item': 'el-menu-item',
  'ivu-breadcrumb': 'el-breadcrumb',
  'ivu-breadcrumb-item': 'el-breadcrumb-item',
  'ivu-pagination': 'el-pagination',
  'ivu-tag': 'el-tag',
  'ivu-alert': 'el-alert',
  'ivu-progress': 'el-progress',
  'ivu-tree': 'el-tree',
  'ivu-dropdown': 'el-dropdown',
  'ivu-dropdown-menu': 'el-dropdown-menu',
  'ivu-dropdown-item': 'el-dropdown-item',
}

// 图标映射
export const iconMapping = {
  'ios-checkmark-outline': 'Check',
  'ios-close-outline': 'Close',
  'ios-search': 'Search',
  'ios-add': 'Plus',
  'ios-remove': 'Minus',
  'ios-arrow-up': 'ArrowUp',
  'ios-arrow-down': 'ArrowDown',
  'ios-arrow-back': 'ArrowLeft',
  'ios-arrow-forward': 'ArrowRight',
  'ios-refresh': 'Refresh',
  'ios-settings': 'Setting',
  'ios-trash': 'Delete',
  'ios-create': 'Edit',
  'ios-eye': 'View',
  'ios-download': 'Download',
  'ios-upload': 'Upload',
  'ios-copy': 'CopyDocument',
  'ios-share': 'Share',
  'ios-information-circle': 'InfoFilled',
  'ios-checkmark-circle': 'SuccessFilled',
  'ios-close-circle': 'CircleCloseFilled',
  'ios-warning': 'WarningFilled',
}
