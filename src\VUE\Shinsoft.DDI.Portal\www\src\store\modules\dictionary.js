/* eslint-disable space-before-function-paren */
import http from '../../utils/axios'
import constDefinition from '../../utils/constDefinition'
const dictionary = {
    state: {
        productTypes: [],
        dosageForms: [],
        licenseTypes: [],
        dutys: [],
        projectStatusList: [],
        biddingTypeList: [],
        biddingProjectScopeList: [] // 项目执行范围
    },
    mutations: {
        GetDosageForm(state, data) {
            state.dosageForms = data
        },
        GetLicenseTypes(state, data) {
            state.licenseTypes = data
        },
        GetDutys(state, data) {
            state.dutys = data
        },
        getProjectStatus(state, data) {
            state.projectStatusList = data
        },

        getBiddingType(state, data) {
            state.biddingTypeList = data
        },
        getBiddingProjectScope(state, data) {
            state.biddingProjectScopeList = data
        }
    },
    actions: {
        getDosageForm({
            commit
        }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    'dictionaryCode': constDefinition.receiver.dictionary.DOSAGEFORM
                }
            }).then(function (response) {
                commit('GetDosageForm', response.data)
            })
        },
        getLicenseTypes({
            commit
        }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    'dictionaryCode': constDefinition.receiver.dictionary.LICENSETYPE
                }
            }).then(function (response) {
                commit('GetLicenseTypes', response.data)
            })
        },
        getDutys({
            commit
        }) {
            http.get('/Dictionary/QueryDictionaryItemCodeSelectList', {
                params: {
                    'dictionaryCode': constDefinition.receiver.dictionary.DUTY
                }
            }).then(function (response) {
                commit('GetDutys', response.data)
            })
        },
        getProjectStatus({
            commit
        }) {
            http.get('/Dictionary/QueryDictionaryItemSelectListByParent', {
                params: {
                    'parentCode': constDefinition.receiver.dictionary.PROJECTSTATUS
                }
            }).then(function (response) {
                commit('getProjectStatus', response.data)
            })
        },
        getBiddingType({
            commit
        }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    'dictionaryCode': constDefinition.receiver.dictionary.BIDDINGTYPE
                }
            }).then(function (response) {
                commit('getBiddingType', response.data)
            })
        },
        getBiddingProjectScope({
            commit
        }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    'dictionaryCode': constDefinition.receiver.dictionary.PROJECTSCOPE
                }
            }).then(function (response) {
                commit('getBiddingProjectScope', response.data)
            })
        }
    }
}
export default dictionary
