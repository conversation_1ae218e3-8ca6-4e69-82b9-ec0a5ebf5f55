﻿<!--常用文档管理 作者：周伟-->
<template>
  <div>
    <div class="ivu-row-BG">
      <!--导航-->
      <Row class="ivu-row-BG ivu-row-Padding ivu-row-Top">
        <Col span="24">
        <Breadcrumb>
          <!-- 系统管理 -->
          <BreadcrumbItem> {{ $t('main.mainSystemManagement') }}</BreadcrumbItem>
          <!-- 首页设置 -->
          <BreadcrumbItem> {{ $t('main.mainHomeSettings') }}</BreadcrumbItem>
          <!-- 文档管理 -->
          <BreadcrumbItem> {{ $t('main.documentManagement') }}</BreadcrumbItem>
        </Breadcrumb>
        </Col>
      </Row>
      <!--筛选条件-->
      <Row class="ivu-row-Top ivu-row-Padding">
        <Col span="4">
        <Input v-model="filter.Name" :placeholder="this.$t('system.annexName')" clearable="" />
        </Col>
        <Col span="6" v-if="behaviors.serverSrc.documentList.documentList_Search">
        <Button icon="ios-search-strong" @click="search" class="helpCircledBut">{{$t('system.search')}}</Button>
        <tips></tips>
        </Col>
      </Row>
      <Row class="ivu-row-BG ivu-row-Padding ivu-row-Bottom">
        <Col span="24" v-if="behaviors.serverSrc.documentList.documentList_Add">
        <Button class="primary_R" icon="ios-plus-outline" @click="onUploadAttachment">{{$t('system.add')}}</Button>
        </Col>
      </Row>
    </div>
    <div>
      <Row>
        <Col span="24">
        <!-- stripe属性  隔行换色 -->
        <Table stripe="" size="small" :columns="columns" :data="dataList"></Table>
        </Col>
      </Row>
    </div>
    <Row class="ivu-row-Top pageStyle">
      <Col span="24">
      <Page class="primary_R" :page-size-opts="pageSizeOpts" :total="totalCount" :current="filter.Page"
        :page-size="filter.Per" size="small" show-total="" show-sizer="" show-elevator=""
        @on-page-size-change="changePageSize" @on-change="changePage"></Page>
      </Col>
    </Row>
    <!--上传附件 窗口-->
    <Modal v-model="attachmentModel" :title="$t('system.uploadFile')" :mask-closable="false"
      class-name="vertical-center-modal" :transfer="false">
      <!-- model 表单数据对象 -->
      <Form ref="formAttachmentItem" :model="formAttachmentItem" :label-width="80">
        <Row class="ivu-row-Bottom">
          <Col span="6">
          <Upload type="select" :show-upload-list="false" :action="uploadAction" :headers="uploadHeaders" ref="upload"
            :format="uploadFormat" :max-size="this.$constDefinition.uploadSize.uploadSizeM20"
            :on-format-error="handleFormatError" :before-upload="handleBeforeUpload" :on-error="handleUploadError"
            :on-exceeded-size="handleMaxSize" :default-file-list="filesList" :on-success="handleSuccess">
            <!-- 上传 -->
            <Button type="ghost" icon="ios-cloud-upload-outline">{{$t('system.upload')}}</Button>
          </Upload>
          </Col>
          <Col span="18">
          <p>{{fileName}}</p>
          </Col>
        </Row>
        <Row>
          <Col span="24">
          <Input v-model="formAttachmentItem.Remark" type="textarea" :maxlength="512"
            :autosize="{minRows: 5,maxRows: 5}" :placeholder="$t('system.remark')"></Input>
          </Col>
        </Row>
      </form>
      <div slot="footer">
        <Row>
          <Col span="24">
          <Button @click="handleAttachmentSave('formAttachmentItem')"
            icon="ios-checkmark-outline">{{$t('system.save')}}</Button>
          <Button @click="cancelModal('formAttachmentItem')" icon="ios-close-outline"
            style="margin-left: 8px">{{$t('system.cancel')}}</Button>
          </Col>
        </Row>
      </div>
    </Modal>

    <div class="progressBarBox" v-show="visible">
      <div class="progressBarCon">
        <!-- 文件上传 -->
        <p>
          <b>{{$t('system.uploadFileProgressBar')}}</b>
        </p>
        <div id="progressCss">
          <span></span>
        </div>
        <!-- 文件名称 文件大小 字节 -->
        <p :title="fileAllData.name">{{$t('system.uploadFileName')}}{{fileAllData.name}}</p>
        <p>{{$t('system.uploadFileSize')}}{{fileAllData.size}}{{$t('system.uploadFileByte')}}</p>
      </div>
    </div>

  </div>
</template>
<script>
    import moment from 'moment';
    import customexport from '../../components/customexport.vue';
    export default {
        components: {
            customexport
        },
        data () {
            return {
                // 上传附件窗口 开关
                attachmentModel: false,
                uploadFormat: ['pdf', 'jpg', 'jpeg', 'png', 'tif', 'doc', 'xls', 'zip', '7z', 'xlsx', 'docx'],
                formAttachmentItem: {},
                filesList: [],
                fileName: '',
                visible: false,
                fileAllData: {},
                attachment: [],
                // 总行数
                totalCount: 0,
                filter: {
                    Page: 1,
                    Per: 10
                },
                pageSizeOpts: this.$constDefinition.pageSizeOpts,
                uploadAction: this.$http.defaults.baseURL + '/Attachment/UploadCommonlyUsedAttachment',
                uploadHeaders: {
                    'Accept-Language': localStorage.getItem('currentLanguage') === null ? 'zh-CN' : localStorage.getItem('currentLanguage') === 'mergeZH' ? 'zh-CN' : 'en-US',
                    Authorization: 'SperogenixAuthentication ' + localStorage.token,
                    route: sessionStorage.route
                },
                dataList: [],
                // 表格
                columns: [{
                              title: this.$t('system.no'),
                              width: 60,
                              align: 'center',
                              type: 'index'
                              // render: (h, params) => {
                              //   return h(
                              //     "span",
                              //     params.index + (this.filter.Page - 1) * this.filter.Per + 1
                              //   );
                              // }
                          },
                          {
                              title: this.$t('queryData.attachmentName'),
                              key: 'Name',
                              minWidth: 220,
                              render: (h, params) => {
                                  return h('div', [
                                      h(
                                          'a', {
                                              on: {
                                                  click: () => {
                                                      this.publicJS.DownloadFile(
                                                          params.row.FullAttachmentPath + params.row.Path
                                                      );
                                                  }
                                              }
                                          },
                                          params.row.Name
                                      )
                                  ]);
                              }
                          },
                          {
                              title: this.$t('system.remark'), // 备注
                              key: 'Remark',
                              minWidth: 140
                          },
                          {
                              title: this.$t('importData.uploader'), // 上传人员
                              key: 'FinalEditor',
                              align: 'center',
                              minWidth: 90
                          },
                          {
                              title: this.$t('queryData.uploadTime'), // 上传时间
                              key: 'FinalEditTime',
                              align: 'center',
                              render: function (h, params) {
                                  if (params.row.FinalEditTime !== null) {
                                      return h(
                                          'div',
                                          moment(params.row.FinalEditTime).format('YYYY-MM-DD HH:mm:ss')
                                      );
                                  }
                              },
                              minWidth: 130
                          },
                          {
                              title: this.$t('system.action'), // 操作
                              key: '',
                              width: 50,
                              align: 'center',
                              render: (h, params) => {
                                  return h('div', [
                                      h(
                                          'Tooltip', {
                                              props: {
                                                  transfer: true,
                                                  placement: 'top',
                                                  content: this.$t('system.delete')
                                              }
                                          },
                                          [
                                              h('Icon', {
                                                  props: {
                                                      type: 'ios-trash-outline',
                                                      size: '20'
                                                  },
                                                  style: {
                                                      cursor: 'pointer',
                                                      display: this.behaviors.serverSrc
                                                          .documentList
                                                          .documentList_Delete
                                                          ? 'block'
                                                          : 'none'
                                                  },
                                                  on: {
                                                      click: () => {
                                                          this.behaviors.serverSrc
                                                              .documentList
                                                          .documentList_Delete ? this.tableDeleteAttachment(params.row) : '';
                                                      }
                                                  }
                                              })
                                          ]
                                      )
                                  ]);
                              }
                          }
                ]
            };
        },
        created () {
            this.behaviors.behaviorsSession(
                localStorage.behaviors,
                'documentList'
            );
            this.search();
        },
        methods: {
            search () {
                this.filter.Page = 1;
                this.queryDocumentList(this.filter);
            },
            // 取得列表
            queryDocumentList (params) {
                this.$http
                    .get('/Document/QueryCommonlyUsedAttachmentList', {
                        params: params
                    })
                    .then(response => {
                        this.dataList = response.data.Models;
                        this.totalCount = response.data.TotalCount;
                });
            },
            // 分页
            changePage (value) {
                this.filter.Page = value;
                this.queryDocumentList(this.filter);
            },
            // 上传附件
            onUploadAttachment () {
                this.isNew = true;
                this.$refs['formAttachmentItem'].resetFields();
                this.attachmentModel = true;
                this.formAttachmentItem = {};
                this.fileName = '';
            },
            changePageSize (value) {
                this.filter.Per = value;
                this.filter.Page = 1;
                this.queryDocumentList(this.filter);
            },
            handleBeforeUpload (file) {
                this.fileAllData.name = file.name;
                this.fileAllData.size = file.size;
                this.visible = true;
            },
            handleSuccess (res, file, fileList) {
                this.fileName = file.name;
                this.attachment.push(res);
                this.formAttachmentItem.Name = file.name;
                this.visible = false;
            },
            // 附件格式错误
            handleFormatError (error, file, fileList) {
                this.visible = false;
                this.$Message.warning(this.$t('system.uploadFormatError') + this.uploadFormat.toString());
            },
            // 附件大小验证
            handleMaxSize (file, fileList) {
                this.visible = false;
                this.$Message.warning(this.$t('batchImport.uploadMaxSizeError'));
            },
            // 附件上传返回错误不走统一拦截处理
            handleUploadError (error, file, fileList) {
                this.visible = false;
                this.publicJS.handleUploadErrorPublic(file, this.$t('batchImport.uploadError'));
            },
            // 附件取消
            cancelModal () {
                this.attachmentModel = false;
                this.filesList = [];
                this.formAttachmentItem = {};
                this.attachment = [];
            },
            // 添加附件
            handleAttachmentSave () {
                if (this.attachment.length == 0) {
                    this.$Message.warning(this.$t('system.needUploadAttachment'));
                    return;
                }
                this.formAttachmentItem.attachment = this.attachment;
                this.$http
                    .post('/Document/SaveAttachment', this.formAttachmentItem)
                    .then(response => {
                        this.attachmentModel = false;
                        this.$Message.success(this.$t('system.saveSuccess'));

                        this.attachment = [];
                        this.search();
                });
            },
            // 排序
            handleSortChange (columns) {
                var sortName = columns.key;
                this.filter.OrderBy = [`${sortName} ${columns.order}`];
            },
            tableDeleteAttachment (row) {
                this.$confirm(
                    // "是否刪除附件"
                    this.$t('deleteData.delAllDialog'),
                    this.$t('system.alter'), {
                        cancelButtonClass: 'btn-custom-cancel',
                        confirmButtonText: this.$t('system.confirm'),
                        cancelButtonText: this.$t('system.cancel'),
                        type: 'warning'
                    }
                )
                    .then(() => {
                        this.$http
                            .post('/Document/DeleteCommonlyUsedAttachment', row)
                            .then(response => {
                                this.filter.Page = this.publicJS.handlePage(this.filter.Page, this.filter.Per, this.totalCount)
                                this.$Message.success(this.$t('system.deleteSuccess'));
                                this.queryDocumentList(this.filter)
                        });
                    })
                    .catch(() => {});
            }
        }
    };

</script>
