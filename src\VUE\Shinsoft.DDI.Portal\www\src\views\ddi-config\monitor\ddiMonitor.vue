<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>DDI配置与监控</el-breadcrumb-item>
        <el-breadcrumb-item>DDI监控</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-select v-model="filter.serviceType" placeholder="服务类型" clearable>
            <el-option label="数据同步服务" value="数据同步服务" />
            <el-option label="接口服务" value="接口服务" />
            <el-option label="监控服务" value="监控服务" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filter.status" placeholder="运行状态" clearable>
            <el-option label="正常" value="正常" />
            <el-option label="异常" value="异常" />
            <el-option label="停止" value="停止" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="filter.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            popper-class="date-picker-dropdown"
          />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="monitorList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" fixed="left" />
        <el-table-column prop="code" label="编号" width="100" fixed="left" />
        <el-table-column prop="distributorName" label="经销商名称" min-width="200" fixed="left" />
        <el-table-column prop="province" label="省份" width="100" />
        <el-table-column prop="city" label="城市" width="120" />
        <el-table-column prop="address" label="经销商地址" min-width="200" />
        <el-table-column prop="contactInfo" label="联系信息" width="150" />
        <el-table-column prop="canDirectConnect" label="可直连" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.canDirectConnect ? 'success' : 'danger'"
              size="small"
            >
              {{ row.canDirectConnect ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateDate" label="更新日期" width="120" />
        <el-table-column prop="progress" label="进展情况" width="120">
          <template #default="{ row }">
            <el-tag
              :type="row.progress === '已完成' ? 'success' : row.progress === '进行中' ? 'warning' : 'info'"
              size="small"
            >
              {{ row.progress }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" fixed="right" align="center">
          <template #default="{ row }">
            <el-tooltip content="沟通日志" placement="top">
              <el-button
                icon="ChatDotRound"
                circle
                size="small"
                @click="editCommunicationLog(row)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 沟通日志对话框 -->
    <communicationLogDialog
      v-model:visible="showCommunicationLogDialog"
      :distributor-info="currentDistributor"
    />
  </div>
</template>

<script>
import communicationLogDialog from './components/communicationLogDialog.vue'

export default {
  name: 'DdiMonitor',
  components: {
    communicationLogDialog
  },
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        serviceType: '',
        status: '',
        dateRange: []
      },
      totalCount: 0,
      // 沟通日志对话框控制
      showCommunicationLogDialog: false,
      currentDistributor: {},
      monitorList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          code: 'K0001',
          distributorName: '安徽太平洋药业软件有限公司新药药房',
          province: '安徽省',
          city: '合肥市',
          address: '安徽省合肥市蜀山区长江西路123号',
          contactInfo: '0551-12345678',
          canDirectConnect: true,
          updateDate: '2024-01-20',
          progress: '已完成',
          serviceName: 'DDI数据同步服务',
          serviceType: '数据同步服务',
          status: '正常',
          cpuUsage: '15.2%',
          memoryUsage: '68.5%',
          responseTime: '120',
          lastCheckTime: '2024-01-20 15:30:00'
        },
        {
          id: 2,
          code: 'K0002',
          distributorName: '合肥新药药大药房有限公司',
          province: '安徽省',
          city: '合肥市',
          address: '安徽省合肥市庐阳区淮河路456号',
          contactInfo: '0551-87654321',
          canDirectConnect: true,
          updateDate: '2024-01-19',
          progress: '进行中',
          serviceName: 'DDI接口网关',
          serviceType: '接口服务',
          status: '正常',
          cpuUsage: '8.7%',
          memoryUsage: '45.3%',
          responseTime: '85',
          lastCheckTime: '2024-01-20 15:29:45'
        },
        {
          id: 3,
          code: 'K0003',
          distributorName: '常州协众大药房有限公司',
          province: '江苏省',
          city: '常州市',
          address: '江苏省常州市天宁区和平路789号',
          contactInfo: '0519-88888888',
          canDirectConnect: false,
          updateDate: '2024-01-18',
          progress: '待开始',
          serviceName: 'DDI监控服务',
          serviceType: '监控服务',
          status: '异常',
          cpuUsage: '95.1%',
          memoryUsage: '89.2%',
          responseTime: '2500',
          lastCheckTime: '2024-01-20 15:25:12'
        },
        {
          id: 4,
          code: 'K0004',
          distributorName: '南京医药股份有限公司第一药店',
          province: '江苏省',
          city: '南京市',
          address: '江苏省南京市鼓楼区中山路321号',
          contactInfo: '025-66666666',
          canDirectConnect: true,
          updateDate: '2024-01-21',
          progress: '已完成',
          serviceName: 'DDI流向处理服务',
          serviceType: '数据同步服务',
          status: '正常',
          cpuUsage: '22.4%',
          memoryUsage: '72.1%',
          responseTime: '180',
          lastCheckTime: '2024-01-20 15:30:15'
        },
        {
          id: 5,
          code: 'K0005',
          distributorName: '天津同仁堂经销商',
          province: '天津市',
          city: '天津市',
          address: '天津市和平区南京路654号',
          contactInfo: '022-77777777',
          canDirectConnect: false,
          updateDate: '2024-01-17',
          progress: '进行中',
          serviceName: 'DDI报表服务',
          serviceType: '接口服务',
          status: '停止',
          cpuUsage: '0%',
          memoryUsage: '0%',
          responseTime: '-',
          lastCheckTime: '2024-01-20 14:45:30'
        }
      ]
    }
  },
  mounted() {
    this.loadMonitorList();
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadMonitorList();
    },

    // 加载监控列表数据
    loadMonitorList() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.monitorList;

        // 根据服务类型过滤
        if (this.filter.serviceType) {
          filteredList = filteredList.filter(item =>
            item.serviceType === this.filter.serviceType
          );
        }

        // 根据状态过滤
        if (this.filter.status) {
          filteredList = filteredList.filter(item =>
            item.status === this.filter.status
          );
        }
        
        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.monitorList = filteredList.slice(start, end);
        
        this.loading = false;
      }, 500);
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadMonitorList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.page = page;
      this.loadMonitorList();
    },
    // 查看详情
    viewDetail(row) {
      this.$message.info(`查看服务详情：${row.serviceName}`);
    },
    // 重启服务
    restartService(row) {
      this.$confirm(`确定要重启服务"${row.serviceName}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('服务重启成功');
      }).catch(() => {
        this.$message.info('已取消重启');
      });
    },
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },

    // 编辑沟通日志
    editCommunicationLog(row) {
      this.currentDistributor = row;
      this.showCommunicationLogDialog = true;
    }
  }
}
</script>
