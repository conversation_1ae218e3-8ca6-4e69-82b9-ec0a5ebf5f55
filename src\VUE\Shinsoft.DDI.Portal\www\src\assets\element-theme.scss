@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // 主色调保持橙色系
  $colors: (
    'primary': (
      'base': #fd9e00,
    ),
    'success': (
      'base': #19be6b,
    ),
    'warning': (
      'base': #ff9900,
    ),
    'danger': (
      'base': #ed4014,
    ),
    'error': (
      'base': #ed4014,
    ),
    'info': (
      'base': #2db7f5,
    ),
  )
);

// 自定义Element Plus主题，保持原有配色方案
// 导入Element Plus样式
@use 'element-plus/theme-chalk/src/index.scss' as *;

// 自定义样式覆盖，保持iView风格
.el-button--primary {
  background-color: #fd9e00;
  border-color: #fd9e00;
  
  &:hover {
    background-color: #ffad33;
    border-color: #ffad33;
  }
  
  &:active {
    background-color: #e68a00;
    border-color: #e68a00;
  }
}

.el-table {
  .el-table__header {
    th {
      background-color: #f8f8f9 !important;
      color: #495060;
    }
  }
  
  .ascending .sort-caret.ascending {
    border-bottom-color: #fd9e00 !important;
  }
  
  .descending .sort-caret.descending {
    border-top-color: #fd9e00 !important;
  }
}

// 保持原有的成功行样式
.el-table .success-row {
  background: #f0f9eb;
}

// 分页器样式
.el-pagination {
  font-size: 12px;
  .el-pagination__jump {
    color: #495060;
  }
  
  .btn-next,
  .btn-prev {
    color: #495060;
    
    &:hover {
      color: #fd9e00;
    }
  }
  .el-select__placeholder{
    font-size: 12px;
  }

  .el-pager li {
    color: #495060;
    font-size: 12px;
    
    &:hover {
      color: #fd9e00;
    }
    
    &.is-active {
      color: #fd9e00;
    }
  }
}

// 输入框样式
.el-input__inner {
  border-color: #dcdee2;
  color: #495060;
  
  &:focus {
    border-color: #fd9e00;
  }
}

// 选择器样式
.el-select {
  .el-input__inner {
    &:focus {
      border-color: #fd9e00;
    }
  }
}

// 日期选择器样式
.el-date-editor {
  .el-input__inner {
    &:focus {
      border-color: #fd9e00;
    }
  }
}


.el-button {
    font-size:12px !important;
}
