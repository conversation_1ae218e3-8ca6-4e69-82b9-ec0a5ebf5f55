import http from '../../utils/axios'
const materialgroup = {
    state: {
        materialGroupSelect: [],
        materialGroup: null,
        materialGrouplist: []
    },
    mutations: {
        QueryMaterialGroupSelect (state, data) {
            state.materialGroupSelect = data
        },
        GetMaterialGroup (state, data) {
            state.materialGroup = data
        },
        QueryMaterialGroupByProductId (state, data) {
            state.materialGrouplist = data
        }
    },
    actions: {
        queryMaterialGroupSelect ({ commit }, params) {
            http.get('/Product/QueryMaterialGroupSelectWithoutDataAuthority', { params: params }).then(function (response) {
                commit('QueryMaterialGroupSelect', response.data)
            })
        },
        getMaterialGroup ({ commit }, params) {
            http.get('/Product/GetMaterialGroupById', { params: params }).then(function (response) {
                commit('GetMaterialGroup', response.data)
            })
        },
        queryMaterialGroupByProductId ({commit}, params) {
            http.get('/Product/QueryMaterialGroup', { params: params }).then(function (response) {
                commit('QueryMaterialGroupByProductId', response.data)
            })
        }
    }
}
export default materialgroup
