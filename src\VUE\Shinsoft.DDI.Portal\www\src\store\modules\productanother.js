import http from '../../utils/axios'
const productanother = {
    state: {
        productanotherlist: [],
        totalCount: 1,
        ProductAnotherAttrAdd: [],
        ProductIDSelectAttr: [] // 产品简称--下拉数据
    },
    mutations: {
        QueryProductAnother(state, data) {
            state.productanotherlist = data.Models
            state.totalCount = data.TotalCount
        },
        // 产品简称--下拉数据
        InitProductIDSelectAttr(state, data) {
            state.ProductIDSelectAttr = data
        }
    },
    actions: {
        // 根据条件获取产品别名
        QueryProductAnother({ commit }, params) {
            http.get('/Product/QueryProductAnother', {
                // 参数params
                params: params
            }).then(function (response) {
                commit('QueryProductAnother', response.data)
            })
        },
        // 产品简称--下拉
        queryAllProductSelectAction({ commit }) {
            http.get('/Product/QueryProductSelectWithoutDataAuthority').then(function (response) {
                commit('InitProductIDSelectAttr', response.data)
            })
        }
    }
}
export default productanother
