import http from '../../utils/axios'
const importProductBatchLog = {
    state: {
        importProductBatchLogList: [],
        totalCount: 1
    },
    mutations: {
        InitStateImportProductBatchLogList (state, data) {
            state.importProductBatchLogList = data.Models
            state.totalCount = data.TotalCount
        }
    },
    actions: {
        queryImportProductBatchLogAction ({
            commit
        }, params) {
            http.get('/Product/QueryBatchImportLog', {
                params: params
            }).then(function (response) {
                commit('InitStateImportProductBatchLogList', response.data)
            })
        }
    }
}
export default importProductBatchLog
