import http from '../../utils/axios'
const productBatchAnother = {
    state: {
        productBatchAnotherList: [],
        productBatchList: [],
        material: {}
    },
    mutations: {
        QueryProductBatchAnotherList (state, data) {
            state.productBatchAnotherList = data.Models
            state.totalCount = data.TotalCount
        },
        QueryProductBatchList (state, data) {
            state.productBatchList = data.Models
        },
        GetBatchAnotherMaterial (state, data) {
            state.material = data
        },
        SetBatchAnotherMterial (state, payload) {
            if (payload.hasOwnProperty('Code')) {
                state.material.Code = payload.Code
            }
        }
    },
    actions: {
        queryProductBatchAnotherList ({ commit }, params) {
            http.get('/Product/QueryProductBatchAnother', { params: params }).then(function (response) {
                commit('QueryProductBatchAnotherList', response.data)
            })
        },
        queryBatch ({ commit }, params) {
            http.get('/Product/QueryProductBatchByMaterialId', { params: params }).then(function (response) {
                commit('QueryProductBatchList', response.data)
            })
        },
        getBatchAnotherMaterial ({ commit }, params) {
            if (params.materialId !== undefined) {
                http.get('/Product/GetMaterial', { params: params }).then(function (response) {
                    commit('GetBatchAnotherMaterial', response.data)
                })
            } else {
                commit('SetMterial', { Code: '' })
            }
        }
    }
}
export default productBatchAnother
