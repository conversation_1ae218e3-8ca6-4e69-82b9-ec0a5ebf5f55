<template></template>
<script>
    export default {
        created () {
            this.to();
        },
        methods: {
            to () {
                // this.$router.push({
                //   name: "login",
                //   params: { code: this.$route.params.code },
                // });
                this.$Message.destroy();
                this.$Message.error({
                    content: this.$t(this.$route.params.code),
                    duration: 3,
                    onClose: () => {
                        this.logout();
                    }
                });
            },
            logout () {
                // 清除用户数据
                this.clearUserData();
                window.location.href =
                    window.location.protocol +
                    '//' +
                    window.location.host +
                    '/#/login';
            },
            // 清除用户数据
            clearUserData () {
                // 清除token和用户信息
                localStorage.removeItem('token');
                localStorage.removeItem('tokenStorage');
                localStorage.removeItem('userId');
                localStorage.removeItem('account');
                localStorage.removeItem('name');
                localStorage.removeItem('userInfo');

                // 清除其他可能的用户相关数据
                sessionStorage.clear();
            }
        }
    };
</script>
