<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>数据清洗</el-breadcrumb-item>
        <el-breadcrumb-item>别名管理</el-breadcrumb-item>
        <el-breadcrumb-item>收货方别名</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 查询条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-input
            v-model="filter.distributorName"
            placeholder="收货方名称"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.aliasName"
            placeholder="收货方别名"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addDistributorAlias">新增别名</el-button>
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="distributorAliasList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" fixed="left" />
        <el-table-column prop="senderProvince" label="发货方省份" width="100" fixed="left" />
        <el-table-column prop="senderName" label="发货方名称" min-width="150" fixed="left" />
        <el-table-column prop="receiverProvince" label="收货方省份" width="100" fixed="left" />
        <el-table-column prop="receiverName" label="收货方名称" min-width="150" fixed="left" />
        <el-table-column prop="receiverAlias" label="收货方别名" min-width="150" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editDistributorAlias(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" type="danger" @click="deleteDistributorAlias(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 编辑对话框 -->
    <DistributorAliasEdit
      v-model:visible="showEditDialog"
      :edit-data="currentEditData"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script>
import DistributorAliasEdit from './components/distributorAliasEdit.vue'

export default {
  name: 'DistributorAlias',
  components: {
    DistributorAliasEdit
  },
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      filter: {
        page: 1,
        per: 10,
        distributorName: '',
        aliasName: '',
        status: ''
      },
      totalCount: 0,
      showEditDialog: false,
      currentEditData: {},
      distributorAliasList: [
        // 模拟数据，实际使用时需要从API获取
        {
          id: 1,
          senderProvince: '河北省',
          senderName: '华北制药集团',
          receiverProvince: '北京市',
          receiverName: '北京医药经销商',
          receiverAlias: '北京医药'
        },
        {
          id: 2,
          senderProvince: '江苏省',
          senderName: '扬子江药业集团',
          receiverProvince: '上海市',
          receiverName: '上海康健经销商',
          receiverAlias: '上海康健'
        },
        {
          id: 3,
          senderProvince: '山东省',
          senderName: '齐鲁制药',
          receiverProvince: '广东省',
          receiverName: '广州南方经销商',
          receiverAlias: '南方医药'
        },
        {
          id: 4,
          senderProvince: '广东省',
          senderName: '广药集团',
          receiverProvince: '广东省',
          receiverName: '深圳华润经销商',
          receiverAlias: '华润深圳'
        },
        {
          id: 5,
          senderProvince: '北京市',
          senderName: '同仁堂集团',
          receiverProvince: '天津市',
          receiverName: '天津同仁堂经销商',
          receiverAlias: '同仁堂'
        }
      ]
    }
  },
  mounted() {
    this.loadDistributorAliasList();
  },
  methods: {
    // 查询方法
    search() {
      this.filter.page = 1;
      this.loadDistributorAliasList();
    },

    // 加载收货方别名列表数据
    loadDistributorAliasList() {
      this.loading = true;

      // 模拟API调用
      setTimeout(() => {
        // 模拟根据查询条件过滤数据
        let filteredList = this.distributorAliasList;

        // 根据收货方名称过滤
        if (this.filter.distributorName) {
          filteredList = filteredList.filter(item =>
            item.receiverName.includes(this.filter.distributorName)
          );
        }

        // 根据收货方别名过滤
        if (this.filter.aliasName) {
          filteredList = filteredList.filter(item =>
            item.receiverAlias.includes(this.filter.aliasName)
          );
        }

        // 根据状态过滤
        if (this.filter.status) {
          filteredList = filteredList.filter(item =>
            item.status === this.filter.status
          );
        }

        // 模拟分页
        this.totalCount = filteredList.length;
        const start = (this.filter.page - 1) * this.filter.per;
        const end = start + this.filter.per;
        this.distributorAliasList = filteredList.slice(start, end);

        this.loading = false;
      }, 500);
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadDistributorAliasList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.page = page;
      this.loadDistributorAliasList();
    },
    // 新增别名
    addDistributorAlias() {
      this.$message.info('新增别名功能开发中...');
    },
    // 新增别名
    addDistributorAlias() {
      this.currentEditData = {};
      this.showEditDialog = true;
    },
    // 编辑别名
    editDistributorAlias(row) {
      this.currentEditData = { ...row };
      this.showEditDialog = true;
    },
    // 删除别名
    deleteDistributorAlias(row) {
      this.$confirm(`确定要删除别名"${row.receiverAlias}"吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功');
        // 这里应该调用API删除数据，然后重新加载列表
        this.loadDistributorAliasList();
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },

    // 编辑成功处理
    handleEditSuccess(data) {
      if (data.id && this.distributorAliasList.find(item => item.id === data.id)) {
        // 更新现有记录
        const index = this.distributorAliasList.findIndex(item => item.id === data.id);
        if (index !== -1) {
          this.distributorAliasList.splice(index, 1, data);
        }
      } else {
        // 新增记录
        this.distributorAliasList.unshift(data);
        this.totalCount++;
      }

      // 重新加载数据以保持分页正确
      this.loadDistributorAliasList();
    }
  }
}
</script>
