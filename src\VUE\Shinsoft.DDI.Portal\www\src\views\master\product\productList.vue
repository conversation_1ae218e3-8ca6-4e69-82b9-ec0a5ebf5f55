<!--产品管理页面 - 基于Element Plus组件-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>主数据管理</el-breadcrumb-item>
        <el-breadcrumb-item>产品管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-cascader
            v-model="selectedProduct"
            :options="productCascaderOptions"
            placeholder="药企\产品\规格"
            clearable
            :props="cascaderProps"
            @change="handleProductCascaderChange"
          />
        </el-col>
        <el-col :span="6">
          <el-input
            v-model="filter.commonName"
            placeholder="通用名"
            clearable
          />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="OfficeBuilding" @click="showManufacturerManagementDialog">药企管理</el-button>
        <el-button icon="Setting" @click="showProductManagementDialog">产品管理</el-button>
        <el-button icon="CirclePlus" @click="addSpec">新增规格</el-button>
        <el-button icon="Download" @click="exportProductList">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="productList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="code" label="规格编码" width="120" />
        <el-table-column prop="productName" label="产品名称" min-width="180" />
        <el-table-column prop="spec" label="规格" width="120" />
        <el-table-column prop="commonName" label="通用名" width="150" />
        <el-table-column prop="productNameEn" label="英文名称" width="150" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="manufacturerName" label="药企" width="150" />
        <el-table-column prop="pharmaceuticalFactory" label="生产厂家" width="150" />
        <el-table-column prop="materialGroup" label="分型" width="120" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
              <el-tooltip content="编辑" placement="top">
                <el-button icon="Edit" circle size="small" @click="editProduct(row)" />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button icon="Delete" circle size="small" @click="removeProduct(row)" />
              </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="filter.page"
        v-model:page-size="filter.per"
        :page-sizes="pageSizeOpts"
        :total="totalCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize"
        @current-change="changePage"
      />
    </div>

    <!-- 产品管理对话框 -->
    <el-dialog
      v-model="showProductDialog"
      title="产品管理"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleCloseProductDialog"
    >
      <!-- 查询条件区域 -->
      <div class="dialog-search-container">
        <el-row :gutter="16" type="flex">
          <el-col :span="6">
            <el-input
              v-model="productDialogFilter.productName"
              placeholder="产品名称"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="searchProductDialog">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 对话框操作按钮区域 -->
      <div class="dialog-action-container">
        <div class="action-buttons">
          <el-button icon="CirclePlus" @click="addProductInDialog">新增产品</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table :data="productDialogList" stripe size="small" v-loading="productDialogLoading">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="nameCn" label="产品名称" min-width="200" />
          <el-table-column prop="manufacturerName" label="药企" width="180" />
          <el-table-column prop="commonName" label="通用名" width="180" />
          <el-table-column prop="nameEn" label="英文名称" width="200" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
                <el-tooltip content="编辑" placement="top">
                  <el-button icon="Edit" circle size="small" @click="editProductInDialog(row)" />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button icon="Delete" circle size="small" @click="removeProductInDialog(row)" />
                </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="productDialogFilter.page"
          v-model:page-size="productDialogFilter.per"
          :page-sizes="[10, 20, 50, 100]"
          :total="productDialogTotalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="changeProductDialogPageSize"
          @current-change="changeProductDialogPage"
        />
      </div>

      <template #footer>
        <div class="dialog-list-footer">
          <el-button @click="handleCloseProductDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 药企管理对话框 -->
    <el-dialog
      v-model="showManufacturerDialog"
      title="药企管理"
      width="1000px"
      :close-on-click-modal="false"
      @close="handleCloseManufacturerDialog"
    >
      <!-- 查询条件区域 -->
      <div class="dialog-search-container">
        <el-row :gutter="16" type="flex">
          <el-col :span="6">
            <el-input
              v-model="manufacturerDialogFilter.manufacturerName"
              placeholder="药企名称"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-button icon="Search" @click="searchManufacturerDialog">查询</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 对话框操作按钮区域 -->
      <div class="dialog-action-container">
        <div class="action-buttons">
          <el-button icon="CirclePlus" @click="addManufacturerInDialog">新增药企</el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <el-table :data="manufacturerDialogList" stripe size="small" v-loading="manufacturerDialogLoading">
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="manufacturerCode" label="药企编号" width="150" />
          <el-table-column prop="manufacturerName" label="药企名称" min-width="180" />
          <el-table-column prop="shortName" label="简称" width="120" />
          <el-table-column prop="nationality" label="国籍" width="120" />
          <el-table-column prop="enumStatusDesc" label="状态" width="100" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
                <el-tooltip content="编辑" placement="top">
                  <el-button icon="Edit" circle size="small" @click="editManufacturerInDialog(row)" />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button icon="Delete" circle size="small" @click="removeManufacturerInDialog(row)" />
                </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="manufacturerDialogFilter.page"
          v-model:page-size="manufacturerDialogFilter.per"
          :page-sizes="[10, 20, 50, 100]"
          :total="manufacturerDialogTotalCount"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="changeManufacturerDialogPageSize"
          @current-change="changeManufacturerDialogPage"
        />
      </div>

      <template #footer>
        <div class="dialog-list-footer">
          <el-button @click="handleCloseManufacturerDialog">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 药企新增编辑对话框组件 -->
    <manufacturerDialog
      v-model:visible="showManufacturerAddDialog"
      :record-id="currentManufacturerEditId"
      @success="handleManufacturerDialogSuccess"
    />

    <!-- 产品新增编辑对话框组件 -->
    <productDialog
      v-model:visible="showProductAddDialog"
      :record-id="currentProductEditId"
      @success="handleProductDialogSuccess"
    />

    <!-- 产品规格新增编辑对话框组件 -->
    <productSpecDialog
      v-model:visible="showProductSpecAddDialog"
      :record-id="currentProductSpecEditId"
      @success="handleProductSpecDialogSuccess"
    />
  </div>
</template>

<script>
import manufacturerDialog from './components/manufacturerDialog.vue'
import productDialog from './components/productDialog.vue'
import productSpecDialog from './components/productSpecDialog.vue'
import { manufacturerApi } from '@/api/manufacturerApi'
import { productApi } from '@/api/productApi'

export default {
  name: 'ProductList',
  components: {
    manufacturerDialog,
    productDialog,
    productSpecDialog
  },
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      selectedProduct: [],
      cascaderProps: {
        value: 'value',
        label: 'label',
        children: 'children',
        checkStrictly: true // 允许选择任意级别
      },
      productCascaderOptions: [], // 从后端动态加载
      filter: {
        page: 1,
        per: 10,
        commonName: '',
        productCategory: '',
        productType: '',
        productSpec: ''
      },
      totalCount: 0,
      productList: [],         
      // 产品管理对话框相关数据
      showProductDialog: false,
      productDialogLoading: false,
      productDialogFilter: {
        page: 1,
        per: 10,
        productName: ''
      },
      productDialogTotalCount: 0,
      productDialogList: [],
      // 药企管理对话框相关数据
      showManufacturerDialog: false,
      manufacturerDialogLoading: false,
      manufacturerDialogFilter: {
        page: 1,
        per: 10,
        manufacturerName: ''
      },
      manufacturerDialogTotalCount: 0,
      // 新增编辑对话框控制
      showManufacturerAddDialog: false,
      currentManufacturerEditId: null,
      showProductAddDialog: false,
      currentProductEditId: null,
      showProductSpecAddDialog: false,
      currentProductSpecEditId: null,
      manufacturerDialogList: []           
    };
  },
  mounted() {
    this.loadProductList();
    this.loadProductCascaderOptions();
  },
  methods: {
    /**
     * 加载产品规格列表数据
     */
    async loadProductList() {
      this.loading = true;

      try {
        // 准备查询参数
        const params = {
          pageIndex: this.filter.page,
          pageSize: this.filter.per,
          commonName: this.filter.commonName,
          order: 'CreateTime desc' // 按创建时间倒序
        };

        // 处理级联筛选参数 - 直接使用Filter中的ID字段
        if (this.filter.productCategory) {
          params.manufacturerId = this.filter.productCategory; // 药企ID
        }
        if (this.filter.productType) {
          params.productId = this.filter.productType; // 产品ID
        }
        if (this.filter.productSpec) {
          params.productSpecId = this.filter.productSpec; // 产品规格ID
        }

        // 调用API接口查询产品规格列表
        const response = await productApi.queryProductSpec(params);

        if (response.data && response.data.success === true) {
          // 直接使用后端返回的数据
          this.productList = response.data.datas || [];
          this.totalCount = response.data.total || 0;
        } else {
          // 处理错误信息，显示messages数组中的信息
          let errorMessage = '查询产品规格列表失败'
          if (response.data?.messages && response.data.messages.length > 0) {
            errorMessage = response.data.messages.join('; ')
          }
          this.$message.error(errorMessage);
          this.productList = [];
          this.totalCount = 0;
        }
      } catch (error) {
        this.$message.error('查询产品规格列表失败：' + (error.message || '网络错误'));
        this.productList = [];
        this.totalCount = 0;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 产品级联选择器改变事件
     * @param {Array} value - 选中的值数组
     */
    handleProductCascaderChange(value) {
      if (value && value.length > 0) {
        this.filter.productCategory = value[0] || '';
        this.filter.productType = value[1] || '';
        this.filter.productSpec = value[2] || '';
      } else {
        this.filter.productCategory = '';
        this.filter.productType = '';
        this.filter.productSpec = '';
      }
    },

    /**
     * 查询按钮点击事件
     */
    search() {
      this.filter.page = 1;
      this.loadProductList();
    },

    /**
     * 分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changePageSize(size) {
      this.filter.per = size;
      this.filter.page = 1;
      this.loadProductList();
    },

    /**
     * 页码改变事件
     * @param {number} page - 新的页码
     */
    changePage(page) {
      this.filter.page = page;
      this.loadProductList();
    },

    /**
     * 显示产品管理对话框
     */
    showProductManagementDialog() {
      this.showProductDialog = true;
      this.loadProductDialogList();
    },

    /**
     * 新增规格
     */
    addSpec() {
      this.currentProductSpecEditId = null
      this.showProductSpecAddDialog = true
    },

    /**
     * 编辑产品
     * @param {Object} row - 产品数据行
     */
    editProduct(row) {
      // 打开产品管理对话框并设置为编辑模式
      this.showProductDialog = true;
      this.loadProductDialogList();

      // 设置当前编辑的产品ID，用于产品对话框
      this.currentProductEditId = row.id;
      this.showProductAddDialog = true;
    },

    /**
     * 删除产品
     * @param {Object} row - 产品数据行
     */
    removeProduct(row) {
      // 显示确认对话框
      this.$confirm(
        `确定要删除产品规格"${row.productName} - ${row.spec}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      ).then(async () => {
        try {
          // 调用删除产品规格API
          const response = await productApi.deleteProductSpec(row.id);

          if (response.data && response.data.success === true) {
            this.$message.success('产品规格删除成功');
            // 刷新主列表
            this.loadProductList();
          } else {
            // 处理错误信息，显示messages数组中的信息
            let errorMessage = '删除失败'
            if (response.data?.messages && response.data.messages.length > 0) {
              errorMessage = response.data.messages.join('; ')
            }
            this.$message.error(errorMessage);
          }
        } catch (error) {
          this.$message.error('删除失败：' + (error.message || '网络错误'));
        }
      }).catch(() => {
        // 用户取消删除，不做任何操作
      });
    },

    /**
     * 加载产品级联选择器数据
     */
    async loadProductCascaderOptions() {
      try {
        const response = await productApi.getProductCascader();

        if (response.data && response.data.success === true) {
          const cascaderData = response.data.data || [];

          // 映射后端数据到前端级联选择器格式
          this.productCascaderOptions = cascaderData.map(manufacturer => ({
            value: manufacturer.value,
            label: manufacturer.label,
            children: manufacturer.children ? manufacturer.children.map(product => ({
              value: product.value,
              label: product.label,
              children: product.children ? product.children.map(spec => ({
                value: spec.value,
                label: spec.label
              })) : []
            })) : []
          }));
        } else {
          console.error('获取产品级联数据失败');
          this.productCascaderOptions = [];
        }
      } catch (error) {
        console.error('获取产品级联数据失败:', error);
        this.productCascaderOptions = [];
      }
    },

    /**
     * 导出产品列表
     */
    exportProductList() {
      console.log('导出产品列表');
      // TODO: 实现导出功能
    },

    /**
     * 加载产品管理对话框列表数据
     */
    async loadProductDialogList() {
      this.productDialogLoading = true;

      try {
        // 准备查询参数
        const params = {
          pageIndex: this.productDialogFilter.page,
          pageSize: this.productDialogFilter.per,
          nameCn: this.productDialogFilter.productName,
          order: 'CreateTime desc' // 按创建时间倒序
        };

        // 调用API接口查询产品列表
        const response = await productApi.queryProduct(params);

        if (response.data && response.data.success === true) {
          // 直接使用后端返回的数据
          this.productDialogList = response.data.datas || [];
          this.productDialogTotalCount = response.data.total || 0;
        } else {
          // 处理错误信息，显示messages数组中的信息
          let errorMessage = '查询产品列表失败'
          if (response.data?.messages && response.data.messages.length > 0) {
            errorMessage = response.data.messages.join('; ')
          }
          this.$message.error(errorMessage);
          this.productDialogList = [];
          this.productDialogTotalCount = 0;
        }
      } catch (error) {
        this.$message.error('查询产品列表失败：' + (error.message || '网络错误'));
        this.productDialogList = [];
        this.productDialogTotalCount = 0;
      } finally {
        this.productDialogLoading = false;
      }
    },

    /**
     * 产品管理对话框查询按钮点击事件
     */
    searchProductDialog() {
      this.productDialogFilter.page = 1;
      this.loadProductDialogList();
    },

    /**
     * 产品管理对话框分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changeProductDialogPageSize(size) {
      this.productDialogFilter.per = size;
      this.productDialogFilter.page = 1;
      this.loadProductDialogList();
    },

    /**
     * 产品管理对话框页码改变事件
     * @param {number} page - 新的页码
     */
    changeProductDialogPage(page) {
      this.productDialogFilter.page = page;
      this.loadProductDialogList();
    },

    /**
     * 关闭产品管理对话框
     */
    handleCloseProductDialog() {
      this.showProductDialog = false;
      // 重置查询条件
      this.productDialogFilter.productName = '';
      this.productDialogFilter.page = 1;
    },

    /**
     * 对话框中新增产品
     */
    addProductInDialog() {
      this.currentProductEditId = null
      this.showProductAddDialog = true
    },

    /**
     * 对话框中编辑产品
     * @param {Object} row - 产品数据行
     */
    editProductInDialog(row) {
      this.currentProductEditId = row.id
      this.showProductAddDialog = true
    },

    /**
     * 对话框中删除产品
     * @param {Object} row - 产品数据行
     */
    removeProductInDialog(row) {
      // 显示确认对话框
      this.$confirm(
        `确定要删除产品"${row.productName}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      ).then(async () => {
        try {
          // 调用删除API
          const response = await productApi.deleteProduct(row.id);

          if (response.data && response.data.success === true) {
            this.$message.success('产品删除成功');
            // 刷新列表
            this.loadProductDialogList();
          } else {
            // 处理错误信息，显示messages数组中的信息
            let errorMessage = '删除失败'
            if (response.data?.messages && response.data.messages.length > 0) {
              errorMessage = response.data.messages.join('; ')
            }
            this.$message.error(errorMessage);
          }
        } catch (error) {
          this.$message.error('删除失败：' + (error.message || '网络错误'));
        }
      }).catch(() => {
        // 用户取消删除，不做任何操作
      });
    },

    /**
     * 显示药企管理对话框
     */
    showManufacturerManagementDialog() {
      this.showManufacturerDialog = true;
      this.loadManufacturerDialogList();
    },

    /**
     * 加载药企管理对话框列表数据
     */
    async loadManufacturerDialogList() {
      this.manufacturerDialogLoading = true;

      try {
        // 准备查询参数
        const params = {
          pageIndex: this.manufacturerDialogFilter.page,
          pageSize: this.manufacturerDialogFilter.per,
          name: this.manufacturerDialogFilter.manufacturerName,
          order: 'CreateTime desc' // 按创建时间倒序
        };

        // 调用API接口查询药企列表
        const response = await manufacturerApi.queryManufacturer(params);

        if (response.data && response.data.success === true) {
          // 处理返回的数据
          const result = response.data.data || response.data;
          const rawList = result.datas || result.items || [];

          // 映射后端返回的字段到表格需要的字段名
          this.manufacturerDialogList = rawList.map(item => ({
            id: item.id,
            manufacturerCode: item.code,
            manufacturerName: item.name,
            shortName: item.shortName,
            nationality: item.country,
            enumStatus: item.enumStatus,
            enumStatusDesc: item.enumStatusDesc,
            remark: item.remark
          }));

          this.manufacturerDialogTotalCount = result.total || 0;
        } else {
          // 处理错误信息，显示messages数组中的信息
          let errorMessage = '查询药企列表失败'
          if (response.data?.messages && response.data.messages.length > 0) {
            errorMessage = response.data.messages.join('; ')
          }
          this.$message.error(errorMessage);
          this.manufacturerDialogList = [];
          this.manufacturerDialogTotalCount = 0;
        }
      } catch (error) {
        console.error('查询药企列表失败:', error);
        this.$message.error('查询药企列表失败：' + (error.message || '网络错误'));
        this.manufacturerDialogList = [];
        this.manufacturerDialogTotalCount = 0;
      } finally {
        this.manufacturerDialogLoading = false;
      }
    },

    /**
     * 药企管理对话框查询按钮点击事件
     */
    searchManufacturerDialog() {
      this.manufacturerDialogFilter.page = 1;
      this.loadManufacturerDialogList();
    },

    /**
     * 药企管理对话框分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changeManufacturerDialogPageSize(size) {
      this.manufacturerDialogFilter.per = size;
      this.manufacturerDialogFilter.page = 1;
      this.loadManufacturerDialogList();
    },

    /**
     * 药企管理对话框页码改变事件
     * @param {number} page - 新的页码
     */
    changeManufacturerDialogPage(page) {
      this.manufacturerDialogFilter.page = page;
      this.loadManufacturerDialogList();
    },

    /**
     * 关闭药企管理对话框
     */
    handleCloseManufacturerDialog() {
      this.showManufacturerDialog = false;
      // 重置查询条件
      this.manufacturerDialogFilter.manufacturerName = '';
      this.manufacturerDialogFilter.page = 1;
    },

    /**
     * 对话框中新增药企
     */
    addManufacturerInDialog() {
      this.currentManufacturerEditId = null
      this.showManufacturerAddDialog = true
    },

    /**
     * 对话框中编辑药企
     * @param {Object} row - 药企数据行
     */
    editManufacturerInDialog(row) {
      this.currentManufacturerEditId = row.id
      this.showManufacturerAddDialog = true
    },

    /**
     * 对话框中删除药企
     * @param {Object} row - 药企数据行
     */
    removeManufacturerInDialog(row) {
      // 显示确认对话框
      this.$confirm(
        `确定要删除药企"${row.manufacturerName}"吗？`,
        '删除确认',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      ).then(async () => {
        try {
          // 调用删除API
          const response = await manufacturerApi.deleteManufacturer(row.id);

          if (response.data && response.data.success === true) {
            this.$message.success('药企删除成功');
            // 刷新列表
            this.loadManufacturerDialogList();
          } else {
            // 处理错误信息，显示messages数组中的信息
            let errorMessage = '删除失败'
            if (response.data?.messages && response.data.messages.length > 0) {
              errorMessage = response.data.messages.join('; ')
            } 
            
            this.$message.error(errorMessage);
          }
        } catch (error) {
          this.$message.error('删除失败：' + (error.message || '网络错误'));
        }
      }).catch(() => {
      });
    },

    /**
     * 药企对话框成功事件处理
     */
    handleManufacturerDialogSuccess() {
      // 刷新药企列表数据
      this.loadManufacturerDialogList()
      console.log('药企保存成功，刷新列表')
    },

    /**
     * 产品对话框成功事件处理
     */
    handleProductDialogSuccess() {
      // 刷新产品列表数据
      this.loadProductDialogList()
      console.log('产品保存成功，刷新列表')
    },

    /**
     * 产品规格对话框成功事件处理
     */
    handleProductSpecDialogSuccess() {
      // 刷新产品规格列表数据（如果有的话）
      // this.loadProductSpecList()
      console.log('产品规格保存成功')
    }
  }
};
</script>
