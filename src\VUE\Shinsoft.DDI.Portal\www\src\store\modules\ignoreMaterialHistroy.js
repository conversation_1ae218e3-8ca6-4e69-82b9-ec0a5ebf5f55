import http from '../../utils/axios'
const ignoreMaterialHistroy = {
    state: {
        ignoreMaterialHistroyList: [],
        totalCount: 1
    },
    mutations: {
        InitStateIgnoreMaterialHistroyList (state, data) {
            state.ignoreMaterialHistroyList = data.Models
            state.totalCount = data.TotalCount
        }
    },
    actions: {
        queryIgnoreMaterialHistroyAction ({
            commit
        }, params) {
            http.get('/Product/QueryIgnoreMaterial', {
                params: params
            }).then(function (response) {
                commit('InitStateIgnoreMaterialHistroyList', response.data)
            })
        }
    }
}
export default ignoreMaterialHistroy
