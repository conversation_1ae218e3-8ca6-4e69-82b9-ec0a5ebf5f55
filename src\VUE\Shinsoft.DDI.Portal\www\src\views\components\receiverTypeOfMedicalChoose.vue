<template>
  <div style="width: 100%" class="ivuInput">
    <Poptip
      placement="bottom-start"
      @on-popper-hide="chooseEnd"
      trigger="hover"
    >
      <Input
        v-model="checkTypeNames"
        readonly
        :placeholder="$t('receiver.receiverType')"
        @on-focus="allMateriallFocus"
      ></Input>
      <div slot="content" class="tree-slot-content">
        <el-tree
          :data="receiverTypes"
          show-checkbox
          default-expand-all
          node-key="value"
          ref="tree"
          highlight-current
          check-on-click-node
          :expand-on-click-node="false"
          @check="checkChange"
        >
        </el-tree>
      </div>
    </Poptip>
  </div>
</template>
<script>
    export default {
        data () {
            return {
                checkTypeNames: '',
                receiverTypes: [],
                checkedNodes: []
            }
        },
        created () {
            this.initReceiver()
        },
        methods: {
            initReceiver () {
                if (!sessionStorage.getItem('receiverTypeOfMedicals')) {
                    this.$http
                        .get('/ReceiverType/QueryReceiverTypeOfMedicalCascader')
                        .then(response => {
                            this.receiverTypes = response.data;
                            sessionStorage.setItem('receiverTypeOfMedicals', JSON.stringify(this.receiverTypes))
                    });
                } else {
                    this.receiverTypes = JSON.parse(sessionStorage.getItem('receiverTypeOfMedicals'))
                }
            },
            checkChange (currentNode, checkParams) {
                this.checkedNodes = checkParams.checkedNodes.filter(item => {
                    return !item.children
                });
                this.checkTypeNames = this.checkedNodes.map(item => { return item.label }).join(',');
            },
            chooseEnd () {
                let checkNodesID = this.checkedNodes.map(item => { return item.value });
                this.$emit('input', checkNodesID);
            },
            // 得到焦点
            allMateriallFocus () {
                this.checkedNodes = [];
                let checkNodesID = [];
                this.checkTypeNames = '';
                this.$refs.tree.setCheckedNodes([]);
                this.$emit('input', checkNodesID);
            }
        }
    }
</script>
<style >
.tree-slot-content > .el-tree > .el-tree-node .el-tree-node__expand-icon {
  display: none !important;
}
/* .ivu-poptip-popper {
  display: block !important;
} */
.tree-slot-content
  > .el-tree
  > .el-tree-node
  > .el-tree-node__children
  > .el-tree-node
  > .el-tree-node__children {
  display: flex !important;
  flex-direction: row !important;
  justify-content: start;
  flex-wrap: wrap;
}
.tree-slot-content {
  width: 400px;
  max-height: 300px;
  /* overflow-y: scroll; */
}
.tree-slot-content
  > .el-tree
  > .el-tree-node
  > .el-tree-node__children
  > .el-tree-node {
  border-bottom: 1px solid #ccc;
}
.tree-slot-content > .el-tree > .el-tree-node > .el-tree-node__content {
  font-weight: bolder;
}
.tree-slot-content
  > .el-tree
  > .el-tree-node
  > .el-tree-node__children
  > .el-tree-node
  > .el-tree-node__content {
  font-weight: 600;
}
.el-tree-node__label {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, \\5fae\8f6f\96c5\9ed1, Arial, sans-serif;
  font-size: 12px !important;
}
</style>
