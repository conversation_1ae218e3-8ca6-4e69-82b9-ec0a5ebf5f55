import http from '../../utils/axios'
const material = {
    state: {
        materialDetails: [],
        materialDetailsTotalcount: 1,
        defaultMaterialBatchNumber: null
    },
    mutations: {
        QueryMaterial (state, data) {
            state.materialDetails = data.Models
            state.materialDetailsTotalcount = data.TotalCount
        },
        RefurbishMaterialBatchNumber (state, data) {
            state.defaultMaterialBatchNumber = data + '_Default'
        }
    },
    actions: {
        queryMaterial ({ commit }, params) {
            http.get('/Product/QueryMaterial', { params: params }).then(response =>
                commit('QueryMaterial', response.data)
            )
        }
    }
}
export default material
