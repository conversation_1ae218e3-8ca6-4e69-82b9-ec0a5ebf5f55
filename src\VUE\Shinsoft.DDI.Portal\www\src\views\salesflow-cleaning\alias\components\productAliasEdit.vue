<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="产品别名管理"
    width="550px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="150px"
      size="default"
      class="alias-form"
    >
      <!-- 三级下拉框：药企、产品、规格 -->
      <el-form-item label="药企/产品/规格" prop="productCascader" class="required-field">
        <el-cascader
          v-model="formData.productCascader"
          :options="productCascaderOptions"
          placeholder="请选择药企/产品/规格"
          clearable
          @change="handleProductCascaderChange"
          :props="{
            expandTrigger: 'hover',
            checkStrictly: false,
            emitPath: true
          }"
          style="width: 100%"
        />
      </el-form-item>

      <!-- 只读通用名 -->
      <el-form-item label="通用名" prop="genericName" class="required-field">
        <el-input
          v-model="formData.genericName"
          placeholder="通用名"
          readonly
        />
      </el-form-item>

      <!-- 通用名别名 -->
      <el-form-item label="通用名别名" prop="genericNameAlias" class="required-field">
        <el-input
          v-model="formData.genericNameAlias"
          placeholder="通用名别名"
        />
      </el-form-item>

      <!-- 规格别名 -->
      <el-form-item label="规格别名" prop="specAlias" class="required-field">
        <el-input
          v-model="formData.specAlias"
          placeholder="规格别名"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" :icon="Close">
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading" :icon="Check">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { Check, Close } from '@element-plus/icons-vue'

export default {
  name: 'ProductAliasEdit',
  components: {
    Check,
    Close
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'success'],
  data() {
    return {
      loading: false,
      Check,
      Close,
      formData: {
        id: null,
        productCascader: [], // 三级联动选择值
        companyId: '',      // 药企ID
        companyName: '',    // 药企名称
        productId: '',      // 产品ID
        productName: '',    // 产品名称
        specId: '',         // 规格ID
        specName: '',       // 规格名称
        genericName: '',    // 通用名（只读）
        genericNameAlias: '', // 通用名别名
        specAlias: ''       // 规格别名
      },
      // 三级联动选项数据
      productCascaderOptions: [
        {
          value: 'company1',
          label: '辉瑞制药',
          children: [
            {
              value: 'product1',
              label: '阿托伐他汀钙片',
              children: [
                {
                  value: 'spec1',
                  label: '20mg*7片',
                  genericName: '阿托伐他汀'
                },
                {
                  value: 'spec2',
                  label: '10mg*14片',
                  genericName: '阿托伐他汀'
                }
              ]
            },
            {
              value: 'product2',
              label: '氨氯地平片',
              children: [
                {
                  value: 'spec3',
                  label: '5mg*14片',
                  genericName: '氨氯地平'
                }
              ]
            }
          ]
        },
        {
          value: 'company2',
          label: '拜耳医药',
          children: [
            {
              value: 'product3',
              label: '阿司匹林肠溶片',
              children: [
                {
                  value: 'spec4',
                  label: '100mg*30片',
                  genericName: '阿司匹林'
                }
              ]
            }
          ]
        }
      ],
      rules: {
        productCascader: [
          { required: true, message: '请选择药企/产品/规格', trigger: 'change' }
        ],
        genericNameAlias: [
          { required: true, message: '请输入通用名别名', trigger: 'blur' }
        ],
        specAlias: [
          { required: true, message: '请输入规格别名', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    isEdit() {
      return this.editData && this.editData.id;
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData();
      } else {
        this.resetForm();
      }
    },
    editData: {
      handler() {
        if (this.visible) {
          this.initFormData();
        }
      },
      deep: true
    }
  },
  methods: {
    // 处理三级联动选择变化
    handleProductCascaderChange(value) {
      if (value && value.length === 3) {
        const [companyId, productId, specId] = value;

        // 查找对应的数据
        const company = this.productCascaderOptions.find(c => c.value === companyId);
        if (company) {
          const product = company.children.find(p => p.value === productId);
          if (product) {
            const spec = product.children.find(s => s.value === specId);
            if (spec) {
              // 更新表单数据
              this.formData.companyId = companyId;
              this.formData.companyName = company.label;
              this.formData.productId = productId;
              this.formData.productName = product.label;
              this.formData.specId = specId;
              this.formData.specName = spec.label;
              this.formData.genericName = spec.genericName;
            }
          }
        }
      } else {
        // 清空相关数据
        this.formData.companyId = '';
        this.formData.companyName = '';
        this.formData.productId = '';
        this.formData.productName = '';
        this.formData.specId = '';
        this.formData.specName = '';
        this.formData.genericName = '';
      }
    },

    // 初始化表单数据
    initFormData() {
      if (this.isEdit) {
        // 编辑模式，填充现有数据
        this.formData = {
          id: this.editData.id,
          productCascader: this.editData.productCascader || [],
          companyId: this.editData.companyId || '',
          companyName: this.editData.companyName || '',
          productId: this.editData.productId || '',
          productName: this.editData.productName || '',
          specId: this.editData.specId || '',
          specName: this.editData.specName || '',
          genericName: this.editData.genericName || '',
          genericNameAlias: this.editData.genericNameAlias || '',
          specAlias: this.editData.specAlias || ''
        };
      } else {
        // 新增模式，重置表单
        this.resetForm();
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        id: null,
        productCascader: [],
        companyId: '',
        companyName: '',
        productId: '',
        productName: '',
        specId: '',
        specName: '',
        genericName: '',
        genericNameAlias: '',
        specAlias: ''
      };

      // 清除表单验证
      this.$nextTick(() => {
        if (this.$refs.formRef) {
          this.$refs.formRef.clearValidate();
        }
      });
    },

    // 提交表单
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 模拟API调用
          setTimeout(() => {
            const currentTime = new Date().toISOString().replace('T', ' ').substring(0, 19);

            const submitData = {
              ...this.formData,
              lastModifyTime: currentTime
            };

            if (!this.isEdit) {
              // 新增模式，设置创建时间
              submitData.createTime = currentTime;
              submitData.id = Date.now(); // 模拟生成ID
            }

            this.loading = false;
            this.$message.success(this.isEdit ? '更新成功' : '保存成功');
            this.$emit('success', submitData);
            this.handleClose();
          }, 1000);
        } else {
          this.$message.error('请检查表单输入');
        }
      });
    },

    // 关闭弹窗
    handleClose() {
      this.$emit('update:visible', false);
    }
  }
}
</script>
