<!--首页 作者：-->
<template>
  <div>
    <div class="content home-bg">
      <el-row class="home-row-top">
        <el-col :span="16" class="home-row-padding">
          <el-card
            :body-style="{
              padding: '16px',
            }"
            style="height: 280px;"
          >
            <div class="dashboard-container">
              <!-- 合并所有数据项 -->
              <div
                class="dashboard-item"
                v-for="(item, index) in allDashboardItems"
                :key="index"
                :style="getDashboardItemStyle(index)"
              >
                <p class="dashboard-value" v-if="item.IsInt">
                  {{ item.Value }}
                </p>
                <p class="dashboard-value" v-else>
                  {{ item.Value }}
                </p>
                <p class="dashboard-title">{{ item.Title }}</p>
              </div>
            </div>
          </el-card>

        </el-col>
        <el-col :span="8" class="pageStyle cardStyleHeader">
          <el-card class="box-card cardStyle" style="height: 280px;" >
            <el-tabs v-model="activeName">
              <!-- 通知公告 -->
              <el-tab-pane name="first">
                <template #label>
                  <span style="margin: 0 8px">{{
                    $t("home.announcement")
                  }}</span>
                </template>
                <div class="home-Announcement-div-box">
                  <div
                    v-for="(item, index) in homeAnnouncementList"
                    :key="index"
                  >
                    <div class="home-Announcement-div">
                      <div class="home-Announcement-div-left">
                        <p @click="homeAnnouncementDetails(item)">
                          {{ item.Title }}
                        </p>
                      </div>
                      <div class="home-Announcement-div-right">
                        <p>{{ formatDate(item.CreateTime) }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <!-- 常用文档下载 -->
              <el-tab-pane name="second">
                <template #label>
                  <span style="margin: 0 8px">{{ $t("home.commonDocumentDownload") }}</span>
                </template>
                <div class="home-Announcement-div-box">
                  <div
                    v-for="(item, index) in homeCommonlyUsedAttachmentsList"
                    :key="index"
                  >
                    <div class="home-Announcement-div">
                      <div class="home-Announcement-div-left">
                        <p
                          @click="
                            publicJS.DownloadAppointName(item.Path, item.Name)
                          "
                        >
                          {{ item.Name }}
                        </p>
                      </div>
                      <div class="home-Announcement-div-right">
                        <p>{{ formatDate(item.ModifyTime) }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

            </el-tabs>
          </el-card>
        </el-col>
      </el-row>

      <!-- 第二行：待办、预警、清洗任务卡片区域 -->
      <el-row class="home-row-bottom" :gutter="16" style="margin-top: 16px;">
        <!-- 待办卡片区域 -->
        <el-col :span="8" class="home-row-padding">
          <el-card style="height: 280px;">
            <template #header>
              <div class="card-header">
                <span>待办事项</span>
                <el-badge :value="totalPendingCount" :max="99" class="item" v-if="totalPendingCount > 0" />
              </div>
            </template>
            <div class="pending-container">
              <div
                v-for="(item, index) in pendingItems"
                :key="index"
                class="pending-item"
                @click="handlePendingClick(item)"
              >
                <div class="pending-content">
                  <div class="pending-title">{{ item.title }}</div>
                  <div class="pending-description">{{ item.description }}</div>
                </div>
                <div class="pending-count">
                  <el-badge :value="item.count" :max="99" class="pending-badge" />
                </div>
                <div class="pending-arrow">
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 预警卡片区域 -->
        <el-col :span="8" class="home-row-padding">
          <el-card style="height: 280px;">
            <template #header>
              <div class="card-header">
                <span>系统预警</span>
                <el-badge :value="totalWarningCount" :max="99" class="item" v-if="totalWarningCount > 0" />
              </div>
            </template>
            <div class="warning-container">
              <div
                v-for="(item, index) in warningItems"
                :key="index"
                class="warning-item"
                :class="item.level"
                @click="handleWarningClick(item)"
              >
                <div class="warning-content">
                  <div class="warning-title">{{ item.title }}</div>
                  <div class="warning-description">{{ item.description }}</div>
                  <div class="warning-time">{{ formatDate(item.lastUpdateTime) }}</div>
                </div>
                <div class="warning-count">
                  <el-badge :value="item.count" :max="99" class="warning-badge" />
                </div>
                <div class="warning-arrow">
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 清洗任务卡片区域 -->
        <el-col :span="8" class="home-row-padding">
          <el-card style="height: 280px;">
            <template #header>
              <div class="card-header">
                <span>清洗任务</span>
                <el-badge :value="totalCleaningCount" :max="99" class="item" v-if="totalCleaningCount > 0" />
              </div>
            </template>
            <div class="cleaning-container">
              <div
                v-for="(item, index) in cleaningItems"
                :key="index"
                class="cleaning-item"
                :class="item.status"
                @click="handleCleaningClick(item)"
              >
                <div class="cleaning-content">
                  <div class="cleaning-title">{{ item.title }}</div>
                  <div class="cleaning-description">{{ item.description }}</div>
                  <div class="cleaning-progress" v-if="item.status === 'processing'">
                    <el-progress :percentage="item.progress" :stroke-width="4" />
                  </div>
                  <div class="cleaning-time">{{ formatDate(item.createTime) }}</div>
                </div>
                <div class="cleaning-count">
                  <el-badge :value="item.count" :max="99" class="cleaning-badge" />
                </div>
                <div class="cleaning-arrow">
                  <el-icon><ArrowRight /></el-icon>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

    </div>
    <homeAnnouncementDetails
      ref="homeAnnouncementDetails"
    ></homeAnnouncementDetails>
  </div>
</template>
<script>
    import moment from 'moment'
    import homeAnnouncementDetails from './components/homeAnnouncementDetails.vue'
    import { ArrowRight } from '@element-plus/icons-vue'
    export default {
        components: {
            homeAnnouncementDetails,
            ArrowRight
        },
        data () {
            return {
                dashboardList: [], // 首页块状统计数据
                updateDateList: [], // 首页日期数据
                homeAnnouncementList: [], // 首页公告
                userAnalysisList: [], // 统计信息
                count: 0,
                homeCommonlyUsedAttachmentsList: [],
                activeName: 'first',
                isDataManager: false,
                color: ['rgb(253,158,0)', 'rgb(251,128,10)', 'rgb(255,241,35)', 'rgb(234,85,20)', 'rgb(214,253,30)'],

                // 待办事项数据
                pendingItems: [],
                totalPendingCount: 0,

                // 预警数据
                warningItems: [],
                totalWarningCount: 0,

                // 清洗任务数据
                cleaningItems: [],
                totalCleaningCount: 0
            }
        },
        created () {
            this.isDataManager = localStorage.isDataManager === 'true';
            this.handleDashboard() // 首页仪表盘数据
            this.handleHomeAnnouncement() // 首页公告
            this.handleHomeCommonlyUsedAttachments() // 常用文档下载
            this.handlePendingItems() // 待办事项
            this.handleWarningItems() // 系统预警
            this.handleCleaningItems() // 清洗任务
        },
        mounted () {
            // 图表已移除
        },
        computed: {
            // 合并所有仪表盘数据
            allDashboardItems() {
                return this.dashboardList;
            }
        },
        methods: {
            // 获取仪表盘项目样式 - 保持原有橙色色调
            getDashboardItemStyle(index) {
                // 使用原有的橙色系色调
                return 'background: linear-gradient(135deg, #fd9e00 0%, #fdae2b 100%); color: #fff;';
            },
            // 首页公告 - 使用模拟数据
            handleHomeAnnouncement () {
                // 模拟公告数据
                this.homeAnnouncementList = [
                    {
                        ID: 1,
                        Title: '药品销售数据采集平台上线通知',
                        CreateTime: '2024-01-15T10:30:00',
                        Content: '系统正式上线，请各经销商及时录入销售数据。'
                    },
                    {
                        ID: 2,
                        Title: '数据录入规范说明',
                        CreateTime: '2024-01-10T14:20:00',
                        Content: '请严格按照数据录入规范进行操作，确保数据准确性。'
                    },
                    {
                        ID: 3,
                        Title: '系统维护通知',
                        CreateTime: '2024-01-08T09:15:00',
                        Content: '系统将于本周末进行例行维护，请提前做好数据备份。'
                    },
                    {
                        ID: 4,
                        Title: '新功能发布公告',
                        CreateTime: '2024-01-05T16:45:00',
                        Content: '新增数据统计分析功能，欢迎体验使用。'
                    }
                ];
            },
            // 常用文档下载 - 使用模拟数据
            handleHomeCommonlyUsedAttachments () {
                // 模拟常用文档数据
                this.homeCommonlyUsedAttachmentsList = [
                    {
                        ID: 1,
                        Title: '数据录入操作手册',
                        FileName: '数据录入操作手册.pdf',
                        FileSize: '2.5MB',
                        DownloadCount: 156,
                        CreateTime: '2024-01-01T00:00:00'
                    },
                    {
                        ID: 2,
                        Title: '系统使用指南',
                        FileName: '系统使用指南.pdf',
                        FileSize: '3.2MB',
                        DownloadCount: 89,
                        CreateTime: '2024-01-01T00:00:00'
                    },
                    {
                        ID: 3,
                        Title: '数据规范标准',
                        FileName: '数据规范标准.docx',
                        FileSize: '1.8MB',
                        DownloadCount: 234,
                        CreateTime: '2024-01-01T00:00:00'
                    },
                    {
                        ID: 4,
                        Title: '常见问题解答',
                        FileName: '常见问题解答.pdf',
                        FileSize: '1.2MB',
                        DownloadCount: 67,
                        CreateTime: '2024-01-01T00:00:00'
                    }
                ];
            },
            // 首页仪表盘数据 - 使用模拟数据
            handleDashboard () {
                // 模拟仪表盘统计数据 - 8个项目，每行4个
                const mockDashboardData = [
                    {
                        Title: '经销商总数',
                        Value: 1256,
                        IsInt: true
                    },
                    {
                        Title: '本月销售额',
                        Value: 8567890,
                        IsInt: true
                    },
                    {
                        Title: '产品种类',
                        Value: 89,
                        IsInt: true
                    },
                    {
                        Title: '活跃用户',
                        Value: 456,
                        IsInt: true
                    },
                    {
                        Title: '数据完整率',
                        Value: '95.6%',
                        IsInt: false
                    },
                    {
                        Title: '待处理订单',
                        Value: 23,
                        IsInt: true
                    },
                    {
                        Title: '系统运行',
                        Value: '365天',
                        IsInt: false
                    },
                    {
                        Title: '最后更新',
                        Value: '今天',
                        IsInt: false
                    }
                ];

                this.dashboardList = mockDashboardData.map((item, index) => {
                    item.Color = this.color[index % this.color.length];
                    item.Value = !isNaN(item.Value) ? parseInt(item.Value) : item.Value;
                    item.IsInt = !isNaN(item.Value);
                    return item;
                });

                // 不再需要单独的updateDateList，所有数据都在dashboardList中
                this.updateDateList = [];
            },

            // 处理待办事项数据 - 使用模拟数据
            handlePendingItems() {
                // 模拟待办事项数据
                this.pendingItems = [
                    {
                        id: 1,
                        title: '流向清洗错误数据处理',
                        description: '需要处理流向数据清洗过程中发现的错误数据',
                        count: 15,
                        type: 'data_cleaning_error',
                        route: '/data-cleaning/error-handling',
                        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小时前
                    },
                    {
                        id: 2,
                        title: '数据审核待办',
                        description: '待审核的数据录入申请',
                        count: 8,
                        type: 'data_review',
                        route: '/data-review/pending',
                        createTime: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4小时前
                    },
                    {
                        id: 3,
                        title: '异常数据处理',
                        description: '系统检测到的异常数据需要人工处理',
                        count: 3,
                        type: 'exception_handling',
                        route: '/data-exception/handling',
                        createTime: new Date(Date.now() - 1 * 60 * 60 * 1000) // 1小时前
                    }
                ];

                // 计算总待办数量
                this.totalPendingCount = this.pendingItems.reduce((total, item) => total + item.count, 0);
            },

            // 处理系统预警数据 - 使用模拟数据
            handleWarningItems() {
                // 模拟预警数据
                this.warningItems = [
                    {
                        id: 1,
                        title: 'DDI客户端通信异常',
                        description: '超过24小时未通信的DDI客户端',
                        count: 5,
                        level: 'high', // high, medium, low
                        type: 'client_communication',
                        route: '/monitoring/client-status',
                        lastUpdateTime: new Date(Date.now() - 26 * 60 * 60 * 1000), // 26小时前
                        createTime: new Date(Date.now() - 26 * 60 * 60 * 1000)
                    },
                    {
                        id: 2,
                        title: '数据同步延迟',
                        description: '数据同步出现延迟，可能影响数据时效性',
                        count: 2,
                        level: 'medium',
                        type: 'data_sync_delay',
                        route: '/monitoring/sync-status',
                        lastUpdateTime: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3小时前
                        createTime: new Date(Date.now() - 6 * 60 * 60 * 1000)
                    },
                    {
                        id: 3,
                        title: '存储空间不足',
                        description: '系统存储空间使用率超过85%',
                        count: 1,
                        level: 'medium',
                        type: 'storage_warning',
                        route: '/monitoring/storage',
                        lastUpdateTime: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1小时前
                        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000)
                    }
                ];

                // 计算总预警数量
                this.totalWarningCount = this.warningItems.reduce((total, item) => total + item.count, 0);
            },

            // 处理清洗任务数据 - 使用模拟数据
            handleCleaningItems() {
                // 模拟清洗任务数据
                this.cleaningItems = [
                    {
                        id: 1,
                        title: '流向数据清洗',
                        description: '正在清洗2024年第一季度流向数据',
                        count: 1,
                        status: 'processing', // pending, processing, completed, failed
                        progress: 65,
                        type: 'flow_data_cleaning',
                        route: '/data-cleaning/flow-data',
                        createTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
                        estimatedTime: '预计还需1小时'
                    },
                    {
                        id: 2,
                        title: '销售数据清洗',
                        description: '待清洗的销售数据批次',
                        count: 3,
                        status: 'pending',
                        progress: 0,
                        type: 'sales_data_cleaning',
                        route: '/data-cleaning/sales-data',
                        createTime: new Date(Date.now() - 30 * 60 * 1000), // 30分钟前
                        estimatedTime: '预计需要2小时'
                    },
                    {
                        id: 3,
                        title: '库存数据清洗',
                        description: '待清洗的库存数据',
                        count: 2,
                        status: 'pending',
                        progress: 0,
                        type: 'inventory_data_cleaning',
                        route: '/data-cleaning/inventory-data',
                        createTime: new Date(Date.now() - 45 * 60 * 1000), // 45分钟前
                        estimatedTime: '预计需要1.5小时'
                    }
                ];

                // 计算总清洗任务数量
                this.totalCleaningCount = this.cleaningItems.reduce((total, item) => total + item.count, 0);
            },

            // 处理待办事项点击
            handlePendingClick(item) {
                console.log('点击待办事项:', item);
                // 这里可以根据item.route进行路由跳转
                if (item.route) {
                    this.$router.push(item.route);
                }
            },

            // 处理预警点击
            handleWarningClick(item) {
                console.log('点击预警项:', item);
                // 这里可以根据item.route进行路由跳转
                if (item.route) {
                    this.$router.push(item.route);
                }
            },

            // 处理清洗任务点击
            handleCleaningClick(item) {
                console.log('点击清洗任务:', item);
                // 这里可以根据item.route进行路由跳转
                if (item.route) {
                    this.$router.push(item.route);
                }
            },











            // 首页公告详情
            homeAnnouncementDetails (item) {
                this.$refs.homeAnnouncementDetails.init(item)
            },
            // 格式化日期方法，替代Vue 3中移除的过滤器
            formatDate(dateString) {
                if (!dateString) return '';
                return moment(dateString).format('YYYY-MM-DD');
            }
        }
    }
</script>
<style>
/* 确保首页最外层容器没有滚动条 */
.content {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

/* 强制移除Element Plus组件可能产生的滚动条 */
.el-row {
  overflow: visible !important;
}

.el-col {
  overflow: visible !important;
}

.el-card {
  overflow: visible !important;
}

.el-card__body {
  overflow: visible !important;
}

/* 仪表盘容器 - 两行四列布局 */
.dashboard-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px;
  padding: 8px;
}

/* 仪表盘项目样式 */
.dashboard-item {
  height: 90px;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.dashboard-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* 仪表盘数值样式 */
.dashboard-value {
  font-weight: bold;
  font-size: 22px;
  margin: 0 0 6px 0;
  line-height: 1.2;
}

/* 仪表盘标题样式 */
.dashboard-title {
  font-size: 13px;
  font-weight: 500;
  margin: 0;
  line-height: 1.2;
  opacity: 0.95;
}
/* .boxCardItem:nth-child(1) {
  margin-left: 0px;
} */
.boxCardItem p:nth-child(1) {
  font-weight: bold;
  font-size: 20px;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.boxCardItem .contentTitle {
  font-size: 12px;
  font-weight: 500;
  margin: 0;
  line-height: 1.2;
  opacity: 0.9;
}
.cardStyle {
  min-height: 14rem;
}


.cardStyle .el-card__body {
  padding: 10px 0 !important;
}
.el-tabs__item {
  padding: 0 10px !important;
}
.cardStyle .el-tabs__item.is-active {
  color: #000 !important;
}

.cardStyle
  .el-tabs--border-card
  > .el-tabs__header
  .el-tabs__item:not(.is-disabled):hover {
  color: #000 !important;
}

.pageStyle .el-button {
  padding: 0 !important;
  color: #495060 !important;
}

.pageStyle .pageStyle3 {
  background: #fff !important;
  border-radius: 4px;
}

.pageStyle .pageStyle5 {
  max-height: 298px;
  overflow-y: auto;
  overflow-x: hidden;
}

.pageStyle .pageStyle5::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.pageStyle .pageStyle5 .pageStyleChildren {
  display: block;
}

.pageStyle .pageStyle4 {
  height: 38px;
}

.pageStyle .pageStyle4 .pageStyleChildren {
  display: none;
}



.cardStyleHeader .el-card__header {
  padding: 8px 20px !important;
}


</style>
<style scoped lang="less">
.home-bg {
  min-height: auto;
  background-color: #fff !important;
  width: 100%;
  overflow-x: hidden;
  padding-bottom: 20px;
}

.home-row-top {
  margin-top: 1rem;
  width: 100%;
}

.home-row-padding {
  padding: 0 1rem;
  width: 100%;
}
.home-Announcement-div-box {
  max-height: 160px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 隐藏内部滚动条 */
.home-Announcement-div-box::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.home-Announcement-div {
  width: 100%;
  height: 32px;
  line-height: 32px;
}

.home-Announcement-div-left {
  display: inline-block;
  width: 77%;
  height: 32px;
}

.home-Announcement-div p {
  width: 98%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-left: 10px;
  cursor: pointer;
  font-size: 14px;
}

.home-Announcement-div-left p:hover {
  color: #409eff;
}

.home-Announcement-div-right {
  float: right;
  display: inline-block;
  width: 22%;
  height: 32px;
  font-size: 14px;
}

.collapseTitle {
  font-size: 15px;
  // font-weight: 700;
}

.small-table,
.small-table td {
  border: 0 !important;
}

.content {
  width: 100%;
}

.demo-Circle-custom {
  & h1 {
    color: #3f414d;
    font-size: 28px;
    font-weight: normal;
  }

  & p {
    color: #657180;
    font-size: 14px;
    margin: 10px 0 15px;
  }

  & span {
    display: block;
    padding-top: 15px;
    color: #657180;
    font-size: 14px;

    &:before {
      content: "";
      display: block;
      width: 50px;
      height: 1px;
      margin: 0 auto;
      background: #e6e0e0;
      position: relative;
      top: -15px;
    }
  }

  & span i {
    font-style: normal;
    color: #3f414d;
  }
}

.item {
  margin-top: 10px;
  margin-right: 40px;
}

.pendingDetails {
  width: 100%;
  height: 32px;
  line-height: 32px;
  color: #3f414d;
}

.pendingDetailsOne {
  width: 84%;
  display: inline-block;
  padding-left: 10px;
}

.pendingDetailsTwo {
  width: 10%;
  display: inline-block;
}

.pendingDetailsTwo .badge {
  margin-right: 5px;
}

.pendingDetailsThree {
  width: 2%;
  display: inline-block;
}
.pendingDetailsBox {
  max-height: 160px;
  overflow-y: auto;
  overflow-x: hidden;
}

.pendingDetailsBox::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}
.pendingDetailsItemHover:hover {
  background: #f2f4f5;
  cursor: pointer;
}

.pendingDetailsItemHover:hover .pendingDetailsOne {
  color: #fd9e00;
}

.flex-container {
  display: -webkit-flex;
  display: flex;
  width: 94%;
  height: 50px;
  line-height: 50px;
  margin: 0 3%;
}

.flex-container:nth-of-type(1) {
  border-top: 1px solid #e0e3e6;
}

.cardStyleFlexItem {
  max-height: 162px;
  overflow-y: auto;
  overflow-x: hidden;
}

.cardStyleFlexItem::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.flexItem {
  border-bottom: 1px solid #e0e3e6;
}

.flexItem1 {
  padding-left: 10px;
  width: 35%;
}

.flexItem1 .p1 {
  font-size: 14px;
}

.flexItem4 {
  width: 35%;
}

.flexItem4 .p2 {
  font-size: 14px;
}

.flexItem2 {
  // font-family: 'Arial' !important;
  font-size: 14px;
  width: 20%;
}

.flexItem3 {
  font-size: 24px;
  width: 10%;
}

.cdms_logo {
  width: 100%;
  height: 38px;
  background: #fff;
  margin-bottom: 15px;
  border-radius: 4px;
}

.cdms_logo img {
  display: block;
  height: 100%;
  margin: 0 auto;
}

::-webkit-scrollbar {
  width: 8px;
  height: 16px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
}

.contentQuantity_span_p {
  line-height: 20px;
}

/* 新增卡片区域样式 */
.home-row-bottom {
  margin-top: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

/* 待办事项样式 */
.pending-container {
  height: 200px;
  overflow-y: auto;
}

.pending-item {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.pending-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.pending-content {
  flex: 1;
  margin-right: 8px;
}

.pending-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.pending-description {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.pending-count {
  margin-right: 8px;
}

.pending-arrow {
  color: #c0c4cc;
  transition: color 0.3s ease;
}

.pending-item:hover .pending-arrow {
  color: #409eff;
}

/* 预警样式 */
.warning-container {
  height: 200px;
  overflow-y: auto;
}

.warning-item {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.warning-item:hover {
  background-color: #f5f7fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.warning-item.high {
  border-left: 4px solid #f56c6c;
}

.warning-item.high:hover {
  border-color: #f56c6c;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
}

.warning-item.medium {
  border-left: 4px solid #e6a23c;
}

.warning-item.medium:hover {
  border-color: #e6a23c;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.2);
}

.warning-item.low {
  border-left: 4px solid #67c23a;
}

.warning-item.low:hover {
  border-color: #67c23a;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);
}

.warning-content {
  flex: 1;
  margin-right: 8px;
}

.warning-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.warning-description {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.warning-time {
  color: #909399;
  font-size: 11px;
}

.warning-count {
  margin-right: 8px;
}

.warning-arrow {
  color: #c0c4cc;
  transition: color 0.3s ease;
}

.warning-item:hover .warning-arrow {
  color: #409eff;
}

/* 清洗任务样式 */
.cleaning-container {
  height: 200px;
  overflow-y: auto;
}

.cleaning-item {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  margin-bottom: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cleaning-item:hover {
  background-color: #f5f7fa;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cleaning-item.pending {
  border-left: 4px solid #909399;
}

.cleaning-item.pending:hover {
  border-color: #909399;
  box-shadow: 0 2px 8px rgba(144, 147, 153, 0.2);
}

.cleaning-item.processing {
  border-left: 4px solid #409eff;
}

.cleaning-item.processing:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.cleaning-item.completed {
  border-left: 4px solid #67c23a;
}

.cleaning-item.completed:hover {
  border-color: #67c23a;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.2);
}

.cleaning-item.failed {
  border-left: 4px solid #f56c6c;
}

.cleaning-item.failed:hover {
  border-color: #f56c6c;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.2);
}

.cleaning-content {
  flex: 1;
  margin-right: 8px;
}

.cleaning-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.cleaning-description {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.cleaning-progress {
  margin: 6px 0;
}

.cleaning-time {
  color: #909399;
  font-size: 11px;
}

.cleaning-count {
  margin-right: 8px;
}

.cleaning-arrow {
  color: #c0c4cc;
  transition: color 0.3s ease;
}

.cleaning-item:hover .cleaning-arrow {
  color: #409eff;
}
</style>
