<!--首页 作者：-->
<template>
  <div>
    <div class="content home-bg">
      <el-row class="home-row-top">
        <el-col :span="16" class="home-row-padding">
          <el-card
            :body-style="{
              padding: '16px',
            }"
          >
            <div class="dashboard-container">
              <!-- 合并所有数据项 -->
              <div
                class="dashboard-item"
                v-for="(item, index) in allDashboardItems"
                :key="index"
                :style="getDashboardItemStyle(index)"
              >
                <p class="dashboard-value" v-if="item.IsInt">
                  {{ item.Value }}
                </p>
                <p class="dashboard-value" v-else>
                  {{ item.Value }}
                </p>
                <p class="dashboard-title">{{ item.Title }}</p>
              </div>
            </div>
          </el-card>

        </el-col>
        <el-col :span="8" class="pageStyle cardStyleHeader">
          <el-card class="box-card cardStyle" >
            <el-tabs v-model="activeName">
              <!-- 通知公告 -->
              <el-tab-pane name="first">
                <template #label>
                  <span style="margin: 0 8px">{{
                    $t("home.announcement")
                  }}</span>
                </template>
                <div class="home-Announcement-div-box">
                  <div
                    v-for="(item, index) in homeAnnouncementList"
                    :key="index"
                  >
                    <div class="home-Announcement-div">
                      <div class="home-Announcement-div-left">
                        <p @click="homeAnnouncementDetails(item)">
                          {{ item.Title }}
                        </p>
                      </div>
                      <div class="home-Announcement-div-right">
                        <p>{{ formatDate(item.CreateTime) }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <!-- 常用文档下载 -->
              <el-tab-pane name="second">
                <template #label>
                  <span style="margin: 0 8px">{{ $t("home.commonDocumentDownload") }}</span>
                </template>
                <div class="home-Announcement-div-box">
                  <div
                    v-for="(item, index) in homeCommonlyUsedAttachmentsList"
                    :key="index"
                  >
                    <div class="home-Announcement-div">
                      <div class="home-Announcement-div-left">
                        <p
                          @click="
                            publicJS.DownloadAppointName(item.Path, item.Name)
                          "
                        >
                          {{ item.Name }}
                        </p>
                      </div>
                      <div class="home-Announcement-div-right">
                        <p>{{ formatDate(item.ModifyTime) }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane
                    v-for="(item, index) in pendingList"
                    :key="index"
                  >
                    <template #label>
                      <span style="margin: 0 8px">{{ item.Item }}</span>
                       <el-badge
                         v-if="item.Type==1 && approvalCount>0"
                         :value="approvalCount"
                         :max="99"
                         class="badge"
                       />
                       <el-badge
                         v-if="item.Type==2 && feedBackCount>0"
                         :value="feedBackCount"
                         :max="99"
                         class="badge"
                       />
                    </template>
                    <div class="pendingDetailsBox">
                    <div
                      v-for="(itemOne, index) in item.PendingDetails"
                      :key="index"
                      class="pendingDetailsItemHover"
                    >
                      <router-link :to="itemOne.Route" v-if="item.Type === 1">
                        <div class="pendingDetails">
                          <div class="pendingDetailsOne">
                            {{ itemOne.Title }}
                          </div>
                          <div class="pendingDetailsTwo">
                            <el-badge
                              :value="itemOne.Quantity"
                              :max="99"
                              class="badge"
                            />
                          </div>
                          <div class="pendingDetailsThree">
                            <el-icon><ArrowRight /></el-icon>
                          </div>
                        </div>
                      </router-link>
                      <router-link :to="itemOne.Route" v-else>
                        <div class="pendingDetails">
                          <div class="pendingDetailsOne">
                            {{ itemOne.Title }}
                          </div>
                          <div class="pendingDetailsTwo">
                            <el-badge
                              :value="itemOne.Quantity"
                              :max="99"
                              class="badge"
                            />
                          </div>
                          <div class="pendingDetailsThree">
                            <el-icon><ArrowRight /></el-icon>
                          </div>
                        </div>
                      </router-link>
                    </div>
                    </div>
                  </el-tab-pane>

            </el-tabs>
          </el-card>
        </el-col>
      </el-row>
         <el-card style="height:75vh; margin: 1rem 0.7rem 0rem 1rem">
            <el-row>
              <el-col :span="8">
                <div
                  id="salesAmount"
                  ref="salesAmount"
                  :style="{ width: '100%', height: '60vh' }"
                ></div>
              </el-col>
              <el-col :span="16">
              <el-row>
                <el-col :span="12">
                  <div
                    id="exFactoryProductAmount"
                    ref="exFactoryProductAmount"
                    style="width: 100%; height: 35vh; margin-left: 1rem"
                  ></div>
                </el-col>
                <el-col :span="12">
                  <div
                    id="exFactoryAreaAmount"
                    ref="exFactoryAreaAmount"
                    style="width: 100%; height: 35vh; margin-left: 1rem"
                  ></div>
                </el-col>
              </el-row>
              <el-row>
                <el-col>
                  <div
                    id="productSalesAmount"
                    ref="productSalesAmount"
                    style="width: 100%; height: 35vh; margin-top: 1rem; margin-left: 1rem"
                  ></div>
                </el-col>
              </el-row>
                </el-col>

            </el-row>
          </el-card>
    </div>
    <homeAnnouncementDetails
      ref="homeAnnouncementDetails"
    ></homeAnnouncementDetails>
  </div>
</template>
<script>
    import moment from 'moment'
    import homeAnnouncementDetails from './components/homeAnnouncementDetails.vue'
    import * as echarts from 'echarts';
    import { ArrowRight } from '@element-plus/icons-vue'
    // import './../../assets/china.js'
    // import 'echarts/map/js/china.js'
    import chinaMap from './../../assets/china.json'
    export default {
        components: {
            homeAnnouncementDetails,
            ArrowRight
        },
        data () {
            return {
                pendingShow: false,
                pendingValue: ['1'],
                approvalCount: 0,
                feedBackCount: 0,
                dashboardList: [], // 首页块状统计数据
                updateDateList: [], // 首页日期数据
                homeAnnouncementList: [], // 首页公告
                pendingList: [], // 代办事项
                userAnalysisList: [], // 统计信息
                count: 0,
                homeCommonlyUsedAttachmentsList: [],
                activeName: 'first',
                isDataManager: false,
                color: ['rgb(253,158,0)', 'rgb(251,128,10)', 'rgb(255,241,35)', 'rgb(234,85,20)', 'rgb(214,253,30)'],
                salesAmountGroupByProviceDataList: []
            }
        },
        created () {
            this.isDataManager = localStorage.isDataManager === 'true';
            this.handleDashboard() // 首页仪表盘数据
            this.handleHomeAnnouncement() // 首页公告
            this.handlePending() // 代办事项
            this.handleHomeCommonlyUsedAttachments() // 常用文档下载
        },
        mounted () {
            // 等待DOM渲染完成后再初始化图表
            this.$nextTick(() => {
                // 根据省份统计产品销售金额
                this.querySalesAmountGroupByProvice();
                // 按产品统计销售金额
                this.querySalesAmountGroupByProduct();
                // 按大区统计销售金额
                this.querySalesAmountGroupByRegion();
                // 按月统计销售金额
                this.querySalesAmountGroupByMonth();
            });
        },
        computed: {
            // 合并所有仪表盘数据
            allDashboardItems() {
                return this.dashboardList;
            }
        },
        methods: {
            // 获取仪表盘项目样式 - 保持原有橙色色调
            getDashboardItemStyle(index) {
                // 使用原有的橙色系色调
                return 'background: linear-gradient(135deg, #fd9e00 0%, #fdae2b 100%); color: #fff;';
            },
            // 首页公告 - 使用模拟数据
            handleHomeAnnouncement () {
                // 模拟公告数据
                this.homeAnnouncementList = [
                    {
                        ID: 1,
                        Title: '药品销售数据采集平台上线通知',
                        CreateTime: '2024-01-15T10:30:00',
                        Content: '系统正式上线，请各经销商及时录入销售数据。'
                    },
                    {
                        ID: 2,
                        Title: '数据录入规范说明',
                        CreateTime: '2024-01-10T14:20:00',
                        Content: '请严格按照数据录入规范进行操作，确保数据准确性。'
                    },
                    {
                        ID: 3,
                        Title: '系统维护通知',
                        CreateTime: '2024-01-08T09:15:00',
                        Content: '系统将于本周末进行例行维护，请提前做好数据备份。'
                    },
                    {
                        ID: 4,
                        Title: '新功能发布公告',
                        CreateTime: '2024-01-05T16:45:00',
                        Content: '新增数据统计分析功能，欢迎体验使用。'
                    }
                ];
            },
            // 常用文档下载 - 使用模拟数据
            handleHomeCommonlyUsedAttachments () {
                // 模拟常用文档数据
                this.homeCommonlyUsedAttachmentsList = [
                    {
                        ID: 1,
                        Title: '数据录入操作手册',
                        FileName: '数据录入操作手册.pdf',
                        FileSize: '2.5MB',
                        DownloadCount: 156,
                        CreateTime: '2024-01-01T00:00:00'
                    },
                    {
                        ID: 2,
                        Title: '系统使用指南',
                        FileName: '系统使用指南.pdf',
                        FileSize: '3.2MB',
                        DownloadCount: 89,
                        CreateTime: '2024-01-01T00:00:00'
                    },
                    {
                        ID: 3,
                        Title: '数据规范标准',
                        FileName: '数据规范标准.docx',
                        FileSize: '1.8MB',
                        DownloadCount: 234,
                        CreateTime: '2024-01-01T00:00:00'
                    },
                    {
                        ID: 4,
                        Title: '常见问题解答',
                        FileName: '常见问题解答.pdf',
                        FileSize: '1.2MB',
                        DownloadCount: 67,
                        CreateTime: '2024-01-01T00:00:00'
                    }
                ];
            },
            // 首页仪表盘数据 - 使用模拟数据
            handleDashboard () {
                // 模拟仪表盘统计数据 - 8个项目，每行4个
                const mockDashboardData = [
                    {
                        Title: '经销商总数',
                        Value: 1256,
                        IsInt: true
                    },
                    {
                        Title: '本月销售额',
                        Value: 8567890,
                        IsInt: true
                    },
                    {
                        Title: '产品种类',
                        Value: 89,
                        IsInt: true
                    },
                    {
                        Title: '活跃用户',
                        Value: 456,
                        IsInt: true
                    },
                    {
                        Title: '数据完整率',
                        Value: '95.6%',
                        IsInt: false
                    },
                    {
                        Title: '待处理订单',
                        Value: 23,
                        IsInt: true
                    },
                    {
                        Title: '系统运行',
                        Value: '365天',
                        IsInt: false
                    },
                    {
                        Title: '最后更新',
                        Value: '今天',
                        IsInt: false
                    }
                ];

                this.dashboardList = mockDashboardData.map((item, index) => {
                    item.Color = this.color[index % this.color.length];
                    item.Value = !isNaN(item.Value) ? parseInt(item.Value) : item.Value;
                    item.IsInt = !isNaN(item.Value);
                    return item;
                });

                // 不再需要单独的updateDateList，所有数据都在dashboardList中
                this.updateDateList = [];
            },
            // 代办事项 - 使用模拟数据
            handlePending () {
                // 模拟待办事项数据
                this.pendingList = [
                    {
                        Type: 1,
                        Title: '待审批事项',
                        PendingDetails: [
                            {
                                Title: '数据录入审批',
                                Quantity: 12,
                                Route: '/approval/data'
                            },
                            {
                                Title: '经销商申请审批',
                                Quantity: 5,
                                Route: '/approval/distributor'
                            },
                            {
                                Title: '产品信息审批',
                                Quantity: 8,
                                Route: '/approval/product'
                            }
                        ]
                    },
                    {
                        Type: 2,
                        Title: '待处理反馈',
                        PendingDetails: [
                            {
                                Title: '系统问题反馈',
                                Quantity: 3,
                                Route: '/feedback/system'
                            },
                            {
                                Title: '数据异常反馈',
                                Quantity: 7,
                                Route: '/feedback/data'
                            },
                            {
                                Title: '功能建议反馈',
                                Quantity: 2,
                                Route: '/feedback/suggestion'
                            }
                        ]
                    }
                ];

                // 计算统计数据
                this.count = 0;
                this.approvalCount = 0;
                this.feedBackCount = 0;

                this.pendingList.forEach(item => {
                    item.PendingDetails.forEach(itemOne => {
                        this.count += Number(itemOne.Quantity);
                    });
                });

                this.pendingList.filter(filter => { return filter.Type === 1 })[0].PendingDetails.forEach(item => {
                    this.approvalCount += Number(item.Quantity);
                });

                this.pendingList.filter(filter => { return filter.Type === 2 })[0].PendingDetails.forEach(item => {
                    this.feedBackCount += Number(item.Quantity);
                });
            },
            // 根据省份统计产品销售金额 - 使用模拟数据
            querySalesAmountGroupByProvice () {
                // 模拟省份销售数据
                this.salesAmountGroupByProviceDataList = [
                    { name: '北京', value: 1250000 },
                    { name: '上海', value: 1180000 },
                    { name: '广东', value: 2350000 },
                    { name: '江苏', value: 1890000 },
                    { name: '浙江', value: 1650000 },
                    { name: '山东', value: 1420000 },
                    { name: '河南', value: 980000 },
                    { name: '四川', value: 1120000 },
                    { name: '湖北', value: 890000 },
                    { name: '湖南', value: 760000 },
                    { name: '安徽', value: 680000 },
                    { name: '河北', value: 720000 },
                    { name: '福建', value: 850000 },
                    { name: '江西', value: 560000 },
                    { name: '辽宁', value: 640000 },
                    { name: '陕西', value: 580000 },
                    { name: '重庆', value: 520000 },
                    { name: '云南', value: 480000 },
                    { name: '贵州', value: 420000 },
                    { name: '广西', value: 390000 }
                ];

                this.handleSalesAmount();
            },
            handleSalesAmount () {
                this.$nextTick(() => {
                    const salesAmountElement = document.getElementById('salesAmount');
                    if (!salesAmountElement) {
                        console.error('销售金额图表容器不存在');
                        return;
                    }

                    let salesAmountOption = {};
                    let salesAmountMap = null;
                    echarts.registerMap('china', { geoJSON: chinaMap })
                    salesAmountMap = echarts.init(salesAmountElement);
                // salesAmountMap.clear();
                salesAmountOption = {
                    title: {
                        text: '全国产品销售金额'
                    },
                    tooltip: {
                        trigger: 'item',
                        // formatter: "{b} {c} 万"
                        formatter: function (params) {
                            let value = parseFloat(params.value).toString() === 'NaN' ? '-' : params.value;
                            return params.name + ': ' + value + ' 万';// 自行定义formatter格式
                        }
                    },
                    visualMap: {
                        min: 0,
                        max: 1500,
                        left: 'left',
                        top: 'bottom',
                        text: ['高', '低'], // 取值范围的文字
                        inRange: {
                            color: ['rgb(253, 238, 0)', 'rgb(253, 85, 0)']// 取值范围的颜色
                        },
                        show: true// 图注
                    },
                    geo: {
                        map: 'china',
                        roam: false, // 不开启缩放和平移
                        zoom: 1.23, // 视角缩放比例
                        label: {
                            show: false, // 是否显示省份名称
                            fontSize: '10',
                            color: 'rgba(0,0,0,0.7)'
                        },
                        itemStyle: {
                            // color: 'rgb(124,214,207,0.2)',
                            borderColor: 'rgba(0, 0, 0, 0.3)' // 省份边界颜色
                            // areaColor: 'rgb(124,214,207,0.2)',

                            // shadowColor: 'rgba(0, 0, 0, 0.5)',
                        },
                        emphasis: {
                            label: {
                                color: '#fff'
                            },
                            itemStyle: {
                                shadowBlur: 20,
                                areaColor: 'rgb(253,158,0)', // 鼠标选择区域颜色
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    },
                    series: [
                        {
                            name: '销售金额',
                            type: 'map',
                            geoIndex: 0,
                            data: this.salesAmountGroupByProviceDataList
                        }
                    ]
                }

                    salesAmountMap.setOption(salesAmountOption);
                });
            },
            // 按产品统计销售金额 - 使用模拟数据
            querySalesAmountGroupByProduct () {
                // 模拟产品销售数据
                const mockProductData = [
                    { name: '阿莫西林胶囊', value: 2850000 },
                    { name: '头孢克肟片', value: 2340000 },
                    { name: '布洛芬缓释胶囊', value: 1980000 },
                    { name: '奥美拉唑肠溶胶囊', value: 1750000 },
                    { name: '氯雷他定片', value: 1420000 },
                    { name: '复方甘草片', value: 1280000 },
                    { name: '维生素C片', value: 980000 },
                    { name: '感冒灵颗粒', value: 850000 },
                    { name: '板蓝根颗粒', value: 720000 },
                    { name: '健胃消食片', value: 650000 }
                ];

                this.handleexFactoryProductAmount(mockProductData);
            },
            handleexFactoryProductAmount (dataList) {
                this.$nextTick(() => {
                    const exFactoryProductAmountElement = document.getElementById('exFactoryProductAmount');
                    if (!exFactoryProductAmountElement) {
                        console.error('产品YTD销售金额图表容器不存在');
                        return;
                    }

                    let exFactoryProductAmountOption = {};
                    let exFactoryProductAmount = echarts.init(exFactoryProductAmountElement);
                let legendData = dataList.map(item => { return item.name });
                exFactoryProductAmountOption = {
                    title: {
                        text: '产品YTD销售金额统计'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c}万 ({d}%)'
                    },
                    legend: {
                        data: legendData,
                        top: '30px'
                    },
                    color: this.color,
                    series: [
                        {
                            name: '销售金额',
                            top: '70px',
                            type: 'pie',
                            radius: ['50%', '80%'],
                            label: {
                                fontSize: 10,
                                width: 150,
                                formatter: '{b}\n{c}万',
                                overflow: 'breakAll',
                                lineHeight: 13
                            },
                            labelLine: {
                                show: true
                            },
                            data: dataList
                        }
                    ]
                }
                    exFactoryProductAmount.setOption(exFactoryProductAmountOption);
                });
            },

            // 按大区统计销售金额 - 使用模拟数据
            querySalesAmountGroupByRegion () {
                // 模拟大区销售数据
                const mockRegionData = [
                    { name: '华东大区', value: 8950000 },
                    { name: '华南大区', value: 7680000 },
                    { name: '华北大区', value: 6420000 },
                    { name: '华中大区', value: 5890000 },
                    { name: '西南大区', value: 4750000 },
                    { name: '东北大区', value: 3280000 },
                    { name: '西北大区', value: 2950000 }
                ];

                this.handleexFactoryAreaAmount(mockRegionData);
            },
            handleexFactoryAreaAmount (dataList) {
                this.$nextTick(() => {
                    const exFactoryAreaAmountElement = document.getElementById('exFactoryAreaAmount');
                    if (!exFactoryAreaAmountElement) {
                        console.error('大区YTD销售金额图表容器不存在');
                        return;
                    }

                    let exFactoryAreaAmountOption = {};
                    let exFactoryAreaAmount = echarts.init(exFactoryAreaAmountElement);
                let legendData = dataList.map(item => { return item.name });
                exFactoryAreaAmountOption = {
                    title: {
                        text: '大区YTD销售金额统计'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c}万 ({d}%)'
                    },
                    legend: {
                        data: legendData,
                        top: '30px'
                    },
                    color: this.color,
                    series: [
                        {
                            name: '销售金额',
                            top: '70px',
                            type: 'pie',
                            selectedMode: 'single',
                            radius: ['50%', '80%'],
                            labelLine: {
                                length: 30
                            },
                            label: {
                                fontSize: 10,
                                formatter: '{b}\n{c}万'
                            },
                            data: dataList
                        }

                    ]
                }
                    exFactoryAreaAmount.setOption(exFactoryAreaAmountOption);
                });
            },

            // 按月统计销售金额 - 使用模拟数据
            querySalesAmountGroupByMonth () {
                // 模拟月度销售数据
                const mockMonthData = [
                    { month: '2023-07', value: 3250000 },
                    { month: '2023-08', value: 3680000 },
                    { month: '2023-09', value: 4120000 },
                    { month: '2023-10', value: 4580000 },
                    { month: '2023-11', value: 4950000 },
                    { month: '2023-12', value: 5320000 },
                    { month: '2024-01', value: 5680000 },
                    { month: '2024-02', value: 4890000 },
                    { month: '2024-03', value: 5450000 },
                    { month: '2024-04', value: 5820000 },
                    { month: '2024-05', value: 6180000 },
                    { month: '2024-06', value: 6540000 }
                ];

                this.handleProductSalesAmount(mockMonthData);
            },
            handleProductSalesAmount (dataList) {
                this.$nextTick(() => {
                    const productSalesAmountElement = document.getElementById('productSalesAmount');
                    if (!productSalesAmountElement) {
                        console.error('产品销售金额图表容器不存在');
                        return;
                    }

                    let productSalesAmountOption = {};
                    let productSalesAmount = echarts.init(productSalesAmountElement);

                let xAxisData = dataList.map(item => { return item.name });
                let amountData = dataList.map(item => { return item.value });
                let rateData = dataList.map((item, index) => {
                    if (index === 0) {
                        return 0
                    } else if (dataList[index - 1].value === 0) {
                        return 0;
                    } else {
                        return (((item.value - dataList[index - 1].value) / Math.abs(dataList[index - 1].value)) * 100).toFixed(2)
                    }
                });
                productSalesAmountOption = {
                    title: {
                        text: '全产品销售金额统计'
                    },
                    tooltip: {
                        trigger: 'axis'
                        // formatter: '{a0}: {c0} 万 <br/> {a1}: {c1} %'
                        // formatter: function (params) {
                        //   console.log(params);
                        // }
                    },
                    legend: {
                        data: ['销售金额', '增长率']
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xAxisData,
                            axisPointer: {
                                type: 'shadow'
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '销售金额',
                            // min: 0,
                            // max: 250,
                            // interval: 50,
                            axisLabel: {
                                formatter: '{value} 万'
                            }
                        },
                        {
                            type: 'value',
                            name: '增长率',

                            axisLabel: {
                                formatter: '{value} %'
                            }
                        }
                    ],
                    color: this.color,
                    series: [
                        {
                            name: '销售金额',
                            type: 'bar',
                            data: amountData
                        },
                        {
                            name: '增长率',
                            type: 'line',
                            yAxisIndex: 1,
                            data: rateData
                        }
                    ]
                }
                    productSalesAmount.setOption(productSalesAmountOption);
                });
            },
            // 首页公告详情
            homeAnnouncementDetails (item) {
                this.$refs.homeAnnouncementDetails.init(item)
            },
            PendingChange () {
                if (this.pendingValue.length <= 1) {
                    this.pendingShow = false
                } else {
                    this.pendingShow = true
                }
            },
            // 格式化日期方法，替代Vue 3中移除的过滤器
            formatDate(dateString) {
                if (!dateString) return '';
                return moment(dateString).format('YYYY-MM-DD');
            }
        }
    }
</script>
<style>
/* 确保首页最外层容器没有滚动条 */
.content {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

/* 强制移除Element Plus组件可能产生的滚动条 */
.el-row {
  overflow: visible !important;
}

.el-col {
  overflow: visible !important;
}

.el-card {
  overflow: visible !important;
}

.el-card__body {
  overflow: visible !important;
}

/* 仪表盘容器 - 两行四列布局 */
.dashboard-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px;
  padding: 8px;
}

/* 仪表盘项目样式 */
.dashboard-item {
  height: 90px;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.dashboard-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* 仪表盘数值样式 */
.dashboard-value {
  font-weight: bold;
  font-size: 22px;
  margin: 0 0 6px 0;
  line-height: 1.2;
}

/* 仪表盘标题样式 */
.dashboard-title {
  font-size: 13px;
  font-weight: 500;
  margin: 0;
  line-height: 1.2;
  opacity: 0.95;
}
/* .boxCardItem:nth-child(1) {
  margin-left: 0px;
} */
.boxCardItem p:nth-child(1) {
  font-weight: bold;
  font-size: 20px;
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.boxCardItem .contentTitle {
  font-size: 12px;
  font-weight: 500;
  margin: 0;
  line-height: 1.2;
  opacity: 0.9;
}
.cardStyle {
  min-height: 14rem;
}


.cardStyle .el-card__body {
  padding: 10px 0 !important;
}
.el-tabs__item {
  padding: 0 10px !important;
}
.cardStyle .el-tabs__item.is-active {
  color: #000 !important;
}

.cardStyle
  .el-tabs--border-card
  > .el-tabs__header
  .el-tabs__item:not(.is-disabled):hover {
  color: #000 !important;
}

.pageStyle .el-button {
  padding: 0 !important;
  color: #495060 !important;
}

.pageStyle .pageStyle3 {
  background: #fff !important;
  border-radius: 4px;
}

.pageStyle .pageStyle5 {
  max-height: 298px;
  overflow-y: auto;
  overflow-x: hidden;
}

.pageStyle .pageStyle5::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.pageStyle .pageStyle5 .pageStyleChildren {
  display: block;
}

.pageStyle .pageStyle4 {
  height: 38px;
}

.pageStyle .pageStyle4 .pageStyleChildren {
  display: none;
}



.cardStyleHeader .el-card__header {
  padding: 8px 20px !important;
}


</style>
<style scoped lang="less">
.home-bg {
  min-height: auto;
  background-color: #fff !important;
  width: 100%;
  overflow-x: hidden;
  padding-bottom: 20px;
}

.home-row-top {
  margin-top: 1rem;
  width: 100%;
}

.home-row-padding {
  padding: 0 1rem;
  width: 100%;
}
.home-Announcement-div-box {
  max-height: 160px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 隐藏内部滚动条 */
.home-Announcement-div-box::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.home-Announcement-div {
  width: 100%;
  height: 32px;
  line-height: 32px;
}

.home-Announcement-div-left {
  display: inline-block;
  width: 77%;
  height: 32px;
}

.home-Announcement-div p {
  width: 98%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-left: 10px;
  cursor: pointer;
  font-size: 14px;
}

.home-Announcement-div-left p:hover {
  color: #409eff;
}

.home-Announcement-div-right {
  float: right;
  display: inline-block;
  width: 22%;
  height: 32px;
  font-size: 14px;
}

.collapseTitle {
  font-size: 15px;
  // font-weight: 700;
}

.small-table,
.small-table td {
  border: 0 !important;
}

.content {
  width: 100%;
}

.demo-Circle-custom {
  & h1 {
    color: #3f414d;
    font-size: 28px;
    font-weight: normal;
  }

  & p {
    color: #657180;
    font-size: 14px;
    margin: 10px 0 15px;
  }

  & span {
    display: block;
    padding-top: 15px;
    color: #657180;
    font-size: 14px;

    &:before {
      content: "";
      display: block;
      width: 50px;
      height: 1px;
      margin: 0 auto;
      background: #e6e0e0;
      position: relative;
      top: -15px;
    }
  }

  & span i {
    font-style: normal;
    color: #3f414d;
  }
}

.item {
  margin-top: 10px;
  margin-right: 40px;
}

.pendingDetails {
  width: 100%;
  height: 32px;
  line-height: 32px;
  color: #3f414d;
}

.pendingDetailsOne {
  width: 84%;
  display: inline-block;
  padding-left: 10px;
}

.pendingDetailsTwo {
  width: 10%;
  display: inline-block;
}

.pendingDetailsTwo .badge {
  margin-right: 5px;
}

.pendingDetailsThree {
  width: 2%;
  display: inline-block;
}
.pendingDetailsBox {
  max-height: 160px;
  overflow-y: auto;
  overflow-x: hidden;
}

.pendingDetailsBox::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}
.pendingDetailsItemHover:hover {
  background: #f2f4f5;
  cursor: pointer;
}

.pendingDetailsItemHover:hover .pendingDetailsOne {
  color: #fd9e00;
}

.flex-container {
  display: -webkit-flex;
  display: flex;
  width: 94%;
  height: 50px;
  line-height: 50px;
  margin: 0 3%;
}

.flex-container:nth-of-type(1) {
  border-top: 1px solid #e0e3e6;
}

.cardStyleFlexItem {
  max-height: 162px;
  overflow-y: auto;
  overflow-x: hidden;
}

.cardStyleFlexItem::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.flexItem {
  border-bottom: 1px solid #e0e3e6;
}

.flexItem1 {
  padding-left: 10px;
  width: 35%;
}

.flexItem1 .p1 {
  font-size: 14px;
}

.flexItem4 {
  width: 35%;
}

.flexItem4 .p2 {
  font-size: 14px;
}

.flexItem2 {
  // font-family: 'Arial' !important;
  font-size: 14px;
  width: 20%;
}

.flexItem3 {
  font-size: 24px;
  width: 10%;
}

.cdms_logo {
  width: 100%;
  height: 38px;
  background: #fff;
  margin-bottom: 15px;
  border-radius: 4px;
}

.cdms_logo img {
  display: block;
  height: 100%;
  margin: 0 auto;
}

::-webkit-scrollbar {
  width: 8px;
  height: 16px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  border-radius: 8px;
  -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
}

.contentQuantity_span_p {
  line-height: 20px;
}
</style>
