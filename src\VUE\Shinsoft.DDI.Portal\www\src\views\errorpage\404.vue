<template>
  <div class="content">
    <div class="boxAll">
    <Row>
        <Col span="24">
            <Card>
              <p slot="title" class="CardP">
                 <img src="../../assets/favicon.png" alt="" width="83" height="40">
                Channel Data Management System
              </p>
                <Row class="assets404">
                  <Col span="24">
                  <img src="../../assets/404.png" alt="" width="306" height="132">
                  <!-- 无法找到该资源页面 -->
                  <span>{{$t('system.unableResources404')}}</span>
                  </Col>
                </Row>
                <Row class="assetsFont">
                  <Col span="24">
                  <!-- 可能原因： -->
                    <span>{{$t('system.possibleReasons')}}</span>
                  </Col>
                </Row>
                <Row class="assetsFont" style="margin-top:10px;">
                  <Col span="21">
                  <!-- 网络信号差 -->
                    <Icon type="stop"></Icon>&nbsp;{{$t('system.networkSignalDifference404')}}
                  </Col>
                  <Col span="3">
                  <!-- 你可以尝试： -->
                    <span>{{$t('system.youCanTry')}}</span>
                  </Col>
                </Row>
                <Row class="assetsFont">
                  <Col span="21">
                  <!-- 输入的网站不正确 -->
                    <Icon type="stop"></Icon>&nbsp;{{$t('system.inputSiteIncorrect404')}}
                  </Col>
                  <Col span="3">
                  <!-- 返回首页 -->
                    <router-link to="/home/<USER>" style="color:#0067b4;width:100%；height:100%;display: block;text-decoration: underline;">{{$t('system.backToHomePage404')}}</router-link>
                  </Col>
                </Row>
            </Card>
        </Col>
    </Row>
    </div>
  </div>
</template>
<script>
    export default {
    };
</script>
<style scoped>
  .content{
    width: 100%;
    height: 100vh;
    min-width: 1200px;
  }
  .boxAll{
    width: 90%;
    height: 400px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -45%;
    margin-top: -200px;
  }
  .CardP{
    height: 48px;
    font-size: 40px;
    color: #0067b4;
  }
  .CardP img{
    margin-right: 15px;
  }
  .assets404{
    margin-top:30px;
  }
  .assets404 span{
    font-size: 36px;
    margin-left:20px;
  }
  .assetsFont{
    font-size: 16px;
    /* margin-top:10px; */
  }
  .assetsFont span{
    font-size: 24px;

  }
</style>
