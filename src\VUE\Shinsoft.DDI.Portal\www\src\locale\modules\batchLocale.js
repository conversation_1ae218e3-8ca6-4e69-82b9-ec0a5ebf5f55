// 主数据管理-->批号-->中英文
export const batchLocale = {
    'zhCN': {
        ignoreProductBatchHistroy: {
            batchNumber: '批号',
            ignoreFromToDate: '忽略时间',
            cancelFromToDate: '取消忽略时间',
            material: '物料号',
            materialDescription: '物料说明',
            plant: '仓库',
            batch: '批号',
            expiryDate: '有效期',
            manufactureDate: '生产日期',
            createTime: '忽略时间',
            creator: '忽略人',
            cancelTime: '取消忽略时间',
            cancelUser: '取消忽略人'
        },
        productBatchAnother: {
            productReferred: '产品简称',
            materialGroup: '分型',
            materialCode: '物料号',
            material: '物料',
            batch: '批号',
            productMaterialCodeMaterial: '产品/分型/物料',
            name: '别名',
            addBatchAnOther: '批号别名',
            alias: '別名',
            batchRequired: '批号必填',
            aliasRequired: '別名必填',
            createTime: '创建日期',
            aliasNumberLength: '别名过长',
            batchNumberLength: '批号过长',
            businessLicenseRequired: '营业执照必填'
        },
        batchImport: {
            materialToIgnoreHistroy: '物料忽略记录',
            batchToIgnoreHistroy: '批号忽略记录',
            importTime: '导入时间',
            importor: '导入人',
            importFile: '导入文件',
            totalCount: '总数(行)',
            rightCount: '成功(行)',
            repeatCount: '重复(行)',
            ignoreCount: '忽略(行)',
            differentCount: '异常(行)',
            importNormalData: '成功数据',
            importAbnormalData: '异常数据',
            ignore: '忽略物料',
            addIgnore: '新增物料',
            confirmUpload: '确认上传',
            uploadFormatError: '仅支持图片格式和PDF的文件.',
            uploadFormatXMLError: '文件类型错误，仅支持xlsx类型文件.',
            uploadFormatXMLAllError: '文件类型错误，仅支持xls、xlsx类型文件.',
            uploadMaxSizeError: '超出上传文件最大限制，文件不能超过20M.',
            uploadMaxSizeError40M: '超出上传文件最大限制，文件不能超过40M.',
            standardError: '请选择数据类型，记录月份，流向商业省份/城市，流向商业名称，商业公司类型',
            ddiDataTypeError: '请选择数据类型',
            ddiReceiverTypeError: '请选择记录月份',
            ddiError: '请选择记录月份，是否加密',
            needDecryptError: '请选择是否加密',
            encryption: '是否加密',
            uploadProcessingError: '文件类型错误，仅支持xls、xlsx、csv、pgp类型文件.',
            uploadExcelError: '文件类型错误，仅支持xls、xlsx、csv类型文件.',
            uploadError: '处理上传数据时出错，请联系管理员'
        },
        productAnother: {
            productAlias: '产品别名',
            addproductAlias: '添加产品别名成功',
            saveproductAlias: '保存产品别名成功',
            delproductAlias: '删除产品别名成功',
            productAliasBeEmpty: '产品别名不能为空',
            productAliasNumberLength: '产品别名过长'
        }
    },
    'enUS': {
        ignoreProductBatchHistroy: {
            batchNumber: 'Batch Number',
            ignoreFromToDate: 'Ignore Date',
            cancelFromToDate: 'Cancel Ignore Date',
            material: 'Material',
            materialDescription: 'Description',
            plant: 'Plant',
            batch: 'Batch',
            expiryDate: 'Expiry',
            manufactureDate: 'Manufacture',
            createTime: 'Ignore Time',
            creator: 'Ignore User',
            cancelTime: 'Cancel Time',
            cancelUser: 'Cancel User'
        },
        productBatchAnother: {
            productReferred: 'Product Referred',
            materialGroup: 'Material Group',
            materialCode: 'Material Code',
            material: 'Material',
            batch: 'Batch',
            productMaterialCodeMaterial: 'Product/Material Group/Material',
            name: 'Alias',
            addBatchAnOther: 'Batch Alias',
            alias: 'Alias',
            batchRequired: 'Required',
            aliasRequired: 'Required',
            createTime: 'CreateTime',
            aliasNumberLength: 'The alias number is too long',
            batchNumberLength: 'The batch number is too long',
            businessLicenseRequired: 'Business License Required'
        },
        batchImport: {
            materialToIgnoreHistroy: 'Material To Ignore Histroy',
            batchToIgnoreHistroy: 'Batch To Ignore Histroy',
            importTime: 'Import Time',
            importor: 'Importor',
            importFile: 'File',
            totalCount: 'Total',
            rightCount: 'Normal',
            repeatCount: 'Repeat',
            ignoreCount: 'Ignore',
            differentCount: 'Error',
            importNormalData: 'Normal Data',
            importAbnormalData: 'Error Data',
            ignore: 'Ignore Material',
            addIgnore: 'Add Material',
            confirmUpload: 'Confirm Upload',
            uploadFormatError: 'The file format is incorrect, please select picture or PDF.',
            uploadFormatXMLError: 'The file format is incorrect, please select xlsx.',
            uploadFormatXMLAllError: 'The file format is incorrect, please select xls,xlsx.',
            uploadMaxSizeError: 'Exceeding file size limit, no more than 20M.',
            standardError: 'Please select data type, record month, flow to commercial province/city, flow to business name, business company type',
            ddiDataTypeError: 'Please select data type，Whether the encryption',
            ddiError: 'Please select record month, commercial company type',
            ddiMonthError: 'Please select record month',
            needDecryptError: 'Please select is Encryption',
            encryption: 'Is Encryption',
            uploadProcessingError: 'Only support only XLS, XLSX, CSV, PGP type files.',
            uploadExcelError: 'Only support only XLS, XLSX, CSV type files.',
            uploadError: 'Error uploading, please contact administrator'
        },
        productAnother: {
            productAlias: 'Product Alias',
            addproductAlias: 'Success',
            saveproductAlias: 'Success',
            delproductAlias: 'Success',
            productAliasBeEmpty: 'Success',
            productAliasNumberLength: 'Success'
        }
    }
}
export default batchLocale
