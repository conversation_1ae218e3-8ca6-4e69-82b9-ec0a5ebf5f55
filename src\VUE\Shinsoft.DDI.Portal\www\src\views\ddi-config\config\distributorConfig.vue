<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>DDI配置与监控</el-breadcrumb-item>
        <el-breadcrumb-item>DDI配置</el-breadcrumb-item>
        <el-breadcrumb-item>经销商配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="8" type="flex">
        <el-col :span="4">
          <el-input v-model="filter.code" placeholder="编号" clearable />
        </el-col>
        <el-col :span="4">
          <el-input v-model="filter.name" placeholder="名称" clearable />
        </el-col>
        <el-col :span="4">
          <el-select v-model="filter.status" placeholder="状态" clearable class="custom-select">
            <el-option label="正常" value="正常" />
            <el-option label="锁定" value="锁定" />
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-input v-model="filter.authCode" placeholder="授权码" clearable />
        </el-col>
        <el-col :span="4">
          <el-select v-model="filter.targetType" placeholder="采集方式" clearable class="custom-select">
            <el-option label="客户端" value="客户端" />
            <el-option label="FTP" value="FTP" />
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="openNewDistributorConfig">新增经销商配置</el-button>
        <el-button icon="Download" @click="exportData">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="distributorList" stripe size="small" v-loading="loading">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="distributorCode" label="编号" min-width="90" />
        <el-table-column prop="distributorName" label="经销商名称" min-width="220" />
        <el-table-column prop="provinceName" label="省份" width="100" />
        <el-table-column prop="cityName" label="城市" width="100" />
        <el-table-column prop="lastCollectTime" label="最新采集时间" width="160" />
        <el-table-column prop="targetType" label="采集方式" width="120" />
        <el-table-column prop="updateToVersion" label="版本" width="180" />
        <el-table-column prop="enumStatusDesc" label="状态" width="80">
          <template #default="{ row }">
            {{ row.enumStatusDesc || '正常' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="配置" placement="top">
              <el-button icon="Setting" circle size="small" @click="openDistributorDetail(row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button icon="Delete" circle size="small" @click="removeDistributor(row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="filter.pageIndex" v-model:page-size="filter.pageSize"
        :page-sizes="pageSizeOpts" :total="totalCount" layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize" @current-change="changePage" />
    </div>

    <!-- 经销商详细配置弹窗 -->
    <DistributorConfigDialog 
      v-model="showDetailDialog" 
      :dialog-title="dialogTitle" 
      :detail-form="detailForm"
      :save-loading="saveDetailLoading" 
      :current-distributor-id="currentDistributor?.id" 
      @save="handleSaveDetail"
      @close="handleCloseDetailDialog" 
      @edit-mapping="handleEditMapping" 
      @detail-loaded="handleDetailLoaded" 
    />




  </div>
</template>

<script>
import DistributorConfigDialog from './components/distributorConfigDialog.vue'
import { configApi } from '@/api/configApi'
import { Plus } from '@element-plus/icons-vue'

export default {
  name: 'DistributorConfig',
  components: {
    DistributorConfigDialog,
    Plus
  },

  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      // 详细配置弹窗相关
      showDetailDialog: false,
      saveDetailLoading: false,

      dialogTitle: '',
      currentDistributor: null,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        code: '',
        name: '',
        targetType: '',
        order: 'ID desc'
      },
      totalCount: 0,
      distributorList: [],
      // 详细配置表单数据
      detailForm: {
        code: '',
        name: '',
        // 基本配置字段
        autoUpdate: true,
        dbConnect: '',
        dbConnectType: '',
        dbSqlB: '',
        dbSqlI: '',
        dbSqlS: '',
        deleteClientData: '',
        distributorAdress: '',
        distributorCode: '',
        distributorName: '',
        environmentVariable: '',
        fileEncoding: '',
        fixFileB: '',
        fixFileI: '',
        fixFileS: '',
        fixPathB: '',
        fixPathI: '',
        fixPathS: '',
        frequency: null,
        ftpPassword: '',
        ftpPath: '',
        ftpPort: '',
        ftpProxy: '',
        ftpProxyPassword: '',
        ftpProxyUsername: '',
        ftpServer: '',
        ftpType: '',
        ftpUsername: '',
        httpPassword: '',
        httpProxy: '',
        httpProxyPassword: '',
        httpProxyUsername: '',
        httpUrl: '',
        httpUsername: '',
        id: '',
        logHttpPassword: '',
        logHttpUrl: '',
        logHttpUsername: '',
        logType: '',
        mode: '',
        otherXml: '',
        restartTime: '',
        savePath: '',
        scheduleDay: '',
        scheduleTime: '',
        scheduleType: '',
        sourceType: '',
        status: '',
        targetType: '',
        updateToVersion: '',
        updateUrl: '',
        verifyFrequency: '',
        version: '',
        wsPassword: '',
        wsUrl: '',
        wsUsername: '',

        // 规则配置字段 - 销售
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 规则配置字段 - 库存
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 规则配置字段 - 购进
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false,

        // 列映射
        ColumnMappings: []
      }
    }
  },
  mounted() {
    this.loadDistributorList();
  },
  methods: {
    // 查询方法
    search() {
      this.filter.pageIndex = 1;
      this.loadDistributorList();
    },

    // 加载经销商列表数据
    async loadDistributorList() {
      this.loading = true;

      try {
        // 构建查询参数
        const params = {
          pageIndex: this.filter.pageIndex,
          pageSize: this.filter.pageSize,
          order: this.filter.order
        };

        // 添加过滤条件
        if (this.filter.code) {
          params.code = this.filter.code;
        }
        if (this.filter.name) {
          params.name = this.filter.name;
        }
        if (this.filter.targetType) {
          params.targetType = this.filter.targetType;
        }

        // 调用后端API
        const response = await configApi.queryReceiverClient(params);

        if (response.data && response.data.success !== false) {
          this.distributorList = response.data.datas || [];
          this.totalCount = response.data.total || 0;
        } else {
          this.$message.error(response.data.messages?.[0] || '查询失败');
          this.distributorList = [];
          this.totalCount = 0;
        }
      } catch (error) {
        console.error('查询经销商配置失败:', error);
        this.$message.error('查询失败，请稍后重试');
        this.distributorList = [];
        this.totalCount = 0;
      } finally {
        this.loading = false;
      }
    },

    // 分页大小改变事件
    changePageSize(size) {
      this.filter.pageSize = size;
      this.filter.pageIndex = 1;
      this.loadDistributorList();
    },

    // 页码改变事件
    changePage(page) {
      this.filter.pageIndex = page;
      this.loadDistributorList();
    },

    // 删除经销商
    async removeDistributor(row) {
      try {
        await this.$confirm(`确定要删除经销商"${row.distributorName}"配置吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        console.log(row);
        // 调用删除API
        const response = await configApi.deleteReceiverClient(row.id);

        if (response.data && response.data.success !== false) {
          this.$message.success('删除成功');
          // 重新加载列表
          this.loadDistributorList();
        } else {
          this.$message.error(response.data.messages?.[0] || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除经销商配置失败:', error);
          this.$message.error('删除失败，请稍后重试');
        }
      }
    },

    // 新增经销商配置
    openNewDistributorConfig() {
      // 重置表单数据
      this.resetDetailForm();

      // 设置为新增模式
      this.currentDistributor = null;
      this.dialogTitle = '新增经销商配置';

      // 显示对话框
      this.showDetailDialog = true;
    },

    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...');
    },

    // 打开经销商详细配置
    openDistributorDetail(row) {
      this.currentDistributor = row;
      this.dialogTitle = `${row.distributorCode}-${row.distributorName}`;

      // 先重置表单并显示基本信息
      this.resetDetailForm();
      this.detailForm.code = row.distributorCode;
      this.detailForm.name = row.distributorName;

      // 显示对话框，详细数据将由对话框组件自动加载
      this.showDetailDialog = true;
    },

    // 处理详细数据加载完成
    handleDetailLoaded(detailData) {
      // 更新表单数据
      this.detailForm = {
        // 基本信息
        ownerName: detailData.ownerName || '科盟贸易', // 货主名称
        code: detailData.distributorCode,
        name: detailData.distributorName,
        distributorAdress: detailData.distributorAdress,

        // 基本配置字段
        autoUpdate: detailData.autoUpdate,
        dbConnect: detailData.dbConnect,
        dbConnectType: detailData.dbConnectType,
        dbSqlB: detailData.dbSqlB,
        dbSqlI: detailData.dbSqlI,
        dbSqlS: detailData.dbSqlS,
        deleteClientData: detailData.deleteClientData,
        distributorAdress: detailData.distributorAdress,
        distributorCode: detailData.distributorCode,
        distributorName: detailData.distributorName,
        environmentVariable: detailData.environmentVariable,
        fileEncoding: detailData.fileEncoding,
        fixFileB: detailData.fixFileB,
        fixFileI: detailData.fixFileI,
        fixFileS: detailData.fixFileS,
        fixPathB: detailData.fixPathB,
        fixPathI: detailData.fixPathI,
        fixPathS: detailData.fixPathS,
        frequency: detailData.frequency,
        ftpPassword: detailData.ftpPassword,
        ftpPath: detailData.ftpPath,
        ftpPort: detailData.ftpPort,
        ftpProxy: detailData.ftpProxy,
        ftpProxyPassword: detailData.ftpProxyPassword,
        ftpProxyUsername: detailData.ftpProxyUsername,
        ftpServer: detailData.ftpServer,
        ftpType: detailData.ftpType,
        ftpUsername: detailData.ftpUsername,
        httpPassword: detailData.httpPassword,
        httpProxy: detailData.httpProxy,
        httpProxyPassword: detailData.httpProxyPassword,
        httpProxyUsername: detailData.httpProxyUsername,
        httpUrl: detailData.httpUrl,
        httpUsername: detailData.httpUsername,
        id: detailData.id,
        logHttpPassword: detailData.logHttpPassword,
        logHttpUrl: detailData.logHttpUrl,
        logHttpUsername: detailData.logHttpUsername,
        logType: detailData.logType,
        mode: detailData.mode || 'Normal',
        otherXml: detailData.otherXml,
        //restartTime: detailData.restartTime,
        savePath: detailData.savePath,
        scheduleDay: detailData.scheduleDay,
        scheduleTime: detailData.scheduleTime,
        scheduleType: detailData.scheduleType,
        sourceType: detailData.sourceType,
        status: detailData.status || '正常',
        targetType: detailData.targetType,
        updateToVersion: detailData.updateToVersion,
        updateUrl: detailData.updateUrl,
        verifyFrequency: detailData.verifyFrequency,
        version: detailData.version,
        wsPassword: detailData.wsPassword,
        wsUrl: detailData.wsUrl,
        wsUsername: detailData.wsUsername,
        serverCheckFrequency: detailData.serverCheckFrequency,

        // 规则配置字段
        salesPreRuleCheck: detailData.salesPreRuleCheck || false,
        salesFileValidation: detailData.salesFileValidation || false,
        salesFileValidationExecution: detailData.salesFileValidationExecution || false,
        salesFileValidationSplicing: detailData.salesFileValidationSplicing || false,
        salesCleanRuleCheck: detailData.salesCleanRuleCheck || false,
        salesStoreNameValidation: detailData.salesStoreNameValidation || false,
        salesProductValidation: detailData.salesProductValidation || false,
        salesQuantityValidation: detailData.salesQuantityValidation || false,
        salesTerminalNameValidation: detailData.salesTerminalNameValidation || false,

        // 库存规则配置
        inventoryPreRuleCheck: detailData.inventoryPreRuleCheck || false,
        inventoryFileValidation: detailData.inventoryFileValidation || false,
        inventoryFileValidationExecution: detailData.inventoryFileValidationExecution || false,
        inventoryFileValidationSplicing: detailData.inventoryFileValidationSplicing || false,
        inventoryCleanRuleCheck: detailData.inventoryCleanRuleCheck || false,
        inventoryStoreNameValidation: detailData.inventoryStoreNameValidation || false,
        inventoryProductValidation: detailData.inventoryProductValidation || false,
        inventoryQuantityValidation: detailData.inventoryQuantityValidation || false,
        inventoryTerminalNameValidation: detailData.inventoryTerminalNameValidation || false,

        // 购进规则配置
        purchasePreRuleCheck: detailData.purchasePreRuleCheck || false,
        purchaseFileValidation: detailData.purchaseFileValidation || false,
        purchaseFileValidationExecution: detailData.purchaseFileValidationExecution || false,
        purchaseFileValidationSplicing: detailData.purchaseFileValidationSplicing || false,
        purchaseCleanRuleCheck: detailData.purchaseCleanRuleCheck || false,
        purchaseStoreNameValidation: detailData.purchaseStoreNameValidation || false,
        purchaseProductValidation: detailData.purchaseProductValidation || false,
        purchaseQuantityValidation: detailData.purchaseQuantityValidation || false,
        purchaseTerminalNameValidation: detailData.purchaseTerminalNameValidation || false
      };
    },

    // 重置详细表单
    resetDetailForm() {
      this.detailForm = {
        // 基本配置字段
        ownerName: '科盟贸易', // 货主名称
        code: '',
        name: '',
        fullName: '',
        address: '',
        customerCode1: '',
        customerCode2: '',
        customerCode3: '',
        level: '',
        batch: '',
        ruleStatus: '科盟贸易',
        region: '',
        status: '正常',
        collectMethod: '',
        fileFormat: 'CSV',
        fileDirectory: '',
        authCode: '',
        distributorContact: '',
        keyuanContact: '',

        // 客户端配置字段
        runMode: 'auto',
        serverCheckFrequency: 60,
        currentVersion: '',
        autoUpgrade: 'false',
        targetVersion: '',
        upgradeDownloadUrl: '',
        repeatMode: 'daily',
        runTime: new Date(2023, 0, 1, 23, 0),
        restartTime: new Date(2023, 0, 1, 3, 0),
        dataSourceType: 'database',
        dbConnectionType: 'sqlserver',
        dbConnectionString: '',
        dbBuyInSql: '',
        dbSellOutSql: '',
        dbInventorySql: '',
        uploadMethod: 'http',
        httpUploadUrl: '',
        httpUploadUsername: '',
        httpUploadPassword: '',
        logRecordMethod: 'webservice',
        logWebServiceUrl: '',
        logWebServiceUsername: '',
        logWebServicePassword: '',
        otherParameters: '',

        // 规则配置字段
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 库存规则配置
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 购进规则配置
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false
      };
    },

    // 关闭详细配置弹窗
    handleCloseDetailDialog() {
      this.showDetailDialog = false;
      this.currentDistributor = null;
      this.resetDetailForm();
    },

    // 重置详细配置表单
    resetDetailForm() {
      this.detailForm = {
        // 基本配置字段
        ownerName: '科盟贸易', // 货主名称
        // 基本配置字段
        autoUpdate: true,
        dbConnect: '',
        dbConnectType: '',
        dbSqlB: '',
        dbSqlI: '',
        dbSqlS: '',
        deleteClientData: '',
        distributorAdress: '',
        distributorCode: '',
        distributorName: '',
        environmentVariable: '',
        fileEncoding: '',
        fixFileB: '',
        fixFileI: '',
        fixFileS: '',
        fixPathB: '',
        fixPathI: '',
        fixPathS: '',
        frequency: null,
        ftpPassword: '',
        ftpPath: '',
        ftpPort: '',
        ftpProxy: '',
        ftpProxyPassword: '',
        ftpProxyUsername: '',
        ftpServer: '',
        ftpType: '',
        ftpUsername: '',
        httpPassword: '',
        httpProxy: '',
        httpProxyPassword: '',
        httpProxyUsername: '',
        httpUrl: '',
        httpUsername: '',
        id: '',
        logHttpPassword: '',
        logHttpUrl: '',
        logHttpUsername: '',
        logType: '',
        mode: '',
        otherXml: '',
        restartTime: '',
        savePath: '',
        scheduleDay: '',
        scheduleTime: '',
        scheduleType: '',
        sourceType: '',
        status: '',
        targetType: '',
        updateToVersion: '',
        updateUrl: '',
        verifyFrequency: '',
        version: '',
        wsPassword: '',
        wsUrl: '',
        wsUsername: '',

        serverCheckFrequency: 60,

        // 规则配置字段 - 销售
        salesPreRuleCheck: false,
        salesFileValidation: false,
        salesFileValidationExecution: false,
        salesFileValidationSplicing: false,
        salesCleanRuleCheck: false,
        salesStoreNameValidation: false,
        salesProductValidation: false,
        salesQuantityValidation: false,
        salesTerminalNameValidation: false,

        // 规则配置字段 - 库存
        inventoryPreRuleCheck: false,
        inventoryFileValidation: false,
        inventoryFileValidationExecution: false,
        inventoryFileValidationSplicing: false,
        inventoryCleanRuleCheck: false,
        inventoryStoreNameValidation: false,
        inventoryProductValidation: false,
        inventoryQuantityValidation: false,
        inventoryTerminalNameValidation: false,

        // 规则配置字段 - 购进
        purchasePreRuleCheck: false,
        purchaseFileValidation: false,
        purchaseFileValidationExecution: false,
        purchaseFileValidationSplicing: false,
        purchaseCleanRuleCheck: false,
        purchaseStoreNameValidation: false,
        purchaseProductValidation: false,
        purchaseQuantityValidation: false,
        purchaseTerminalNameValidation: false
      };
    },

    // 保存详细配置
    async handleSaveDetail() {
      this.saveDetailLoading = true;

      try {
        let response;
        // 判断是新增还是编辑
        if (this.currentDistributor?.id) {
          // 编辑模式
          response = await configApi.updateReceiverClient(saveData);
        } else {
          // 新增模式 - 需要从选中的经销商获取ID
          if (!this.detailForm.selectedDistributorId) {
            this.$message.error('请先选择经销商');
            return;
          }
          saveData.id = this.detailForm.selectedDistributorId;
          response = await configApi.addReceiverClient(saveData);
        }

        if (response.data && response.data.success !== false) {
          this.$message.success(this.currentDistributor?.id ? '配置更新成功' : '配置创建成功');
          this.handleCloseDetailDialog();
          // 重新加载列表
          this.loadDistributorList();
        } else {
          this.$message.error(response.data.messages?.[0] || '保存失败');
        }
      } catch (error) {
        console.error('保存经销商配置失败:', error);
        this.$message.error('保存失败，请稍后重试');
      } finally {
        this.saveDetailLoading = false;
      }
    },

    // 编辑列映射
    editMapping(row) {
      this.$message.info(`编辑映射：${row.fieldName}`);
      // TODO: 实现编辑映射功能
    },

    // 下载文件
    downloadFile(fileName) {
      this.$message.info(`下载文件：${fileName}`);
      // TODO: 实现文件下载功能
    },

    // 处理编辑列映射
    handleEditMapping(row, index) {
      // 这里可以添加编辑列映射的逻辑
      console.log('编辑列映射:', row, index);
      this.$message.info('编辑列映射功能待实现');
    },

    // 解析时间字符串为Date对象
    parseTime(timeStr) {
      if (!timeStr) return new Date(2023, 0, 1, 0, 0);

      try {
        // 假设时间格式为 "HH:mm" 或 "HH:mm:ss"
        const parts = timeStr.split(':');
        const hour = parseInt(parts[0]) || 0;
        const minute = parseInt(parts[1]) || 0;
        return new Date(2023, 0, 1, hour, minute);
      } catch (error) {
        console.warn('解析时间失败:', timeStr, error);
        return new Date(2023, 0, 1, 0, 0);
      }
    },

    // 格式化Date对象为时间字符串
    formatTime(dateObj) {
      if (!dateObj) return '';

      try {
        const hour = dateObj.getHours().toString().padStart(2, '0');
        const minute = dateObj.getMinutes().toString().padStart(2, '0');
        return `${hour}:${minute}`;
      } catch (error) {
        console.warn('格式化时间失败:', dateObj, error);
        return '';
      }
    }

  }
}
</script>

<style scoped>
/* 操作按钮样式 */
.el-table .el-button--small {
  padding: 5px 8px;
  font-size: 12px;
}

/* 详细配置弹窗样式 */
.detail-form {
  padding: 20px;
}

.detail-form .el-form-item {
  margin-bottom: 20px;
}

.detail-form .el-input,
.detail-form .el-select,
.detail-form .el-input-number,
.detail-form .el-time-picker {
  width: 100%;
}

/* 表单标签样式 */
.detail-form .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 禁用输入框样式 */
.detail-form .el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
}

/* 多行文本框样式 */
.detail-form .el-textarea .el-textarea__inner {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* SQL文本框特殊样式 */
.detail-form .el-form-item:has([placeholder*="SQL"]) .el-textarea__inner {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

/* 配置分组间距 */
.config-section:last-child {
  margin-bottom: 0;
}

/* 规则配置样式 */
.rule-section {
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.rule-options {
  margin-bottom: 15px;
}

.rule-button {
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  font-size: 13px;
  padding: 8px 16px;
}

.rule-button.el-button--primary {
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
}

.rule-button.el-button--default {
  background-color: #fff;
  border-color: #dcdfe6;
  color: #606266;
}

.rule-button.el-button--default:hover {
  background-color: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.validation-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.validation-checkbox {
  margin-right: 0;
  font-size: 13px;
}

.validation-checkbox .el-checkbox__label {
  color: #606266;
  font-weight: 500;
}

.validation-checkbox.is-checked .el-checkbox__label {
  color: #409eff;
}

/* 列映射配置样式 */
.mapping-toolbar {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-buttons .el-button {
  font-size: 12px;
  padding: 6px 12px;
}

.mapping-table {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.column-table {
  font-size: 12px;
  width: 100%;
}

.column-table .el-table__header {
  background-color: #f5f7fa;
}

.column-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}

.column-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.column-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.column-table .el-button.is-circle {
  width: 28px;
  height: 28px;
  padding: 0;
  margin: 0 2px;
}

.column-table .el-switch {
  --el-switch-on-color: #409eff;
  --el-switch-off-color: #dcdfe6;
}

/* 采集记录样式 */
.record-section {
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

.record-table {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.collection-table {
  font-size: 12px;
}

.collection-table .el-table__header {
  background-color: #f5f7fa;
}

.collection-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}

.collection-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.collection-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.collection-table .el-link {
  font-size: 12px;
  word-break: break-all;
}

.collection-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

.error-count {
  color: #f56c6c;
  font-weight: 600;
}

.record-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.record-pagination .el-pagination {
  --el-pagination-font-size: 12px;
}

/* 数据记录样式 */
.data-record-section {
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 20px;
}

.search-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.search-form .el-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}

.search-form .el-form-item {
  margin-bottom: 10px;
  margin-right: 15px;
}

.search-form .el-form-item__label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.search-form .el-input,
.search-form .el-date-picker,
.search-form .el-checkbox,
.search-form .el-button {
  font-size: 12px;
}

.search-form .el-button {
  padding: 6px 12px;
}

.data-table {
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
}

.record-table {
  font-size: 12px;
}

.record-table .el-table__header {
  background-color: #f5f7fa;
}

.record-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
}

.record-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.record-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.record-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

.data-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.data-pagination .el-pagination {
  --el-pagination-font-size: 12px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .search-form .el-form {
    flex-direction: column;
    align-items: flex-start;
  }

  .search-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }
}

/* 客户端日志样式 */
.client-log-section {
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.client-log-table {
  font-size: 12px;
  border: 1px solid #e4e7ed;
}

.client-log-table .el-table__header {
  background-color: #f5f7fa;
}

.client-log-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  text-align: center;
}

.client-log-table .el-table__body td {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.client-log-table .el-table__body tr {
  height: 40px;
}

.client-log-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.client-log-table .el-table__inner-wrapper::before {
  display: none;
}

.client-log-table .el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 客户端IP列样式 */
.client-log-table .el-table__body td:first-child {
  text-align: center;
  font-family: 'Courier New', monospace;
  color: #409eff;
  font-weight: 500;
}

/* 日志内容列样式 */
.client-log-table .el-table__body td:nth-child(2) {
  font-family: 'Courier New', monospace;
  color: #606266;
  line-height: 1.4;
}

/* 时间列样式 */
.client-log-table .el-table__body td:nth-child(3),
.client-log-table .el-table__body td:nth-child(4) {
  text-align: center;
  font-family: 'Courier New', monospace;
  color: #909399;
  font-size: 11px;
}

.client-log-pagination {
  margin-top: 0;
  display: flex;
  justify-content: center;
  padding: 15px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.client-log-pagination .el-pagination {
  --el-pagination-font-size: 12px;
}

/* 沟通日志样式 */
.communication-log-form {
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  padding: 20px;
  margin-bottom: 20px;
}

.communication-log-form .el-form-item__label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.communication-log-form .el-input,
.communication-log-form .el-date-picker,
.communication-log-form .el-textarea {
  font-size: 13px;
}

.communication-log-form .el-textarea__inner {
  resize: vertical;
  min-height: 80px;
}

.communication-log-form .el-button {
  font-size: 13px;
  padding: 8px 16px;
}

.communication-log-list {
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.communication-log-table {
  font-size: 13px;
  border: 1px solid #e4e7ed;
}

.communication-log-table .el-table__header {
  background-color: #f5f7fa;
}

.communication-log-table .el-table__header th {
  background-color: #f5f7fa;
  color: #333;
  font-weight: 600;
  border-bottom: 1px solid #e4e7ed;
  text-align: center;
}

.communication-log-table .el-table__body td {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.communication-log-table .el-table__body tr {
  height: 50px;
}

.communication-log-table .el-table__row:hover {
  background-color: #f5f7fa;
}

.communication-log-table .el-table__inner-wrapper::before {
  display: none;
}

/* 日志时间列样式 */
.communication-log-table .el-table__body td:first-child {
  text-align: center;
  font-family: 'Courier New', monospace;
  color: #909399;
  font-size: 12px;
}

/* 日志内容列样式 */
.communication-log-table .el-table__body td:nth-child(2) {
  color: #606266;
  line-height: 1.5;
  padding-left: 15px;
  padding-right: 15px;
}

/* 操作列样式 */
.communication-log-table .el-table__body td:last-child {
  text-align: center;
}

.communication-log-table .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

.communication-log-table .el-button--warning {
  background-color: #e6a23c;
  border-color: #e6a23c;
  color: #fff;
}

.communication-log-table .el-button--warning:hover {
  background-color: #ebb563;
  border-color: #ebb563;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .communication-log-form .el-col {
    margin-bottom: 10px;
  }

  .communication-log-table .el-table__body td:first-child {
    font-size: 11px;
  }
}

/* 下拉框样式统一 */
.custom-select :deep(.el-input__wrapper),
.custom-select :deep(.el-input__inner),
.custom-select :deep(.el-select__placeholder),
.custom-select :deep(.el-select__selected-item),
.custom-select :deep(.el-select .el-input .el-input__wrapper .el-input__inner) {
  font-size: 12px !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  font-size: 12px !important;
}
</style>
