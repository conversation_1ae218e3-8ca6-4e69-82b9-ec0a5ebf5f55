<template>
    <Modal
    v-model="showEditModal"
    :transfer="false"
    :mask-closable="false"
    width="1000"
    :title="$t('userEdit.title')"
    @on-cancel="handlecancel"
  >
    <div class="ivu-row-Padding">
    <Form
      ref="addEmployeeForm"
      :model="employeeModel"
      :rules="ruleValidate"
      :label-width="150"
    >
      <Row>
        <Col span="11">
          <FormItem
            :label="$t('userlist.muid')"
            prop="MUID"
          >
          <Input
              v-model="employeeModel.MUID"
              max="16"
              :placeholder="$t('userlist.muid')"
            ></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.loginName')"
            prop="LoginName"
          >
          <Input
              v-model="employeeModel.LoginName"
              max="16"
              :placeholder="$t('userlist.loginName')"
            ></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.searchname')"
            prop="NameCn"
          >
          <Input
              v-model="employeeModel.NameCn"
              max="50"
              :placeholder="$t('userlist.searchname')"
            ></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.gender')"
            prop="Sex"
          >
          <Select v-model="employeeModel.Sex" :placeholder="$t('userlist.gender')">
            <Option v-for="item in selectGenderList" :value="item.value" :key="item.value">{{ item.label }}
            </Option>
          </Select>
         </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.department')"
            prop="DepartmentID"
          >
          <Select
                  v-model="employeeModel.DepartmentID"
                  :placeholder="$t('userlist.department')"
                  clearable
                  readonly
                >
                  <Option
                    v-for="item in departmentList"
                    :value="item.value"
                    :key="item.value"
                    >{{ item.label }}</Option
                  >
              </Select>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.position')"
            prop="Title"
          >
          <Input
              v-model="employeeModel.Title"
              max="50"
              :placeholder="$t('userlist.position')"
            ></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.telephone')"
            prop="Mobile"
          >
          <Input
              v-model="employeeModel.Mobile"
              max="20"
              :placeholder="$t('userlist.telephone')"
            ></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.email')"
            prop="Email"
          >
          <Input
              v-model="employeeModel.Email"
              max="50"
              :placeholder="$t('userlist.email')"
            ></Input>
          </FormItem>
        </Col>
        <Col span="11">
          <FormItem
            :label="$t('userlist.entryDate')"
            prop="HireDate"
          >
          <DatePicker
            v-model="employeeModel.HireDate"
            placement="bottom-end"
            type="date"
            :placeholder="$t('userlist.entryDate')"
            style="width: 100%"
            onc
          ></DatePicker>
        </FormItem>
        </Col>
        <Col span="22">
          <FormItem :label="$t('system.note')" prop="Remark">
                    <Input
                      type="textarea"
                      v-model="employeeModel.Remark"
                      :placeholder="$t('system.note')"
                      style="width: 100%"
                    ></Input>
                  </FormItem>
        </Col>
      </Row>
    </Form>
  </div>
    <div slot="footer">
      <Row class="ivu-row-Padding-5">
        <Col span="24">
          <Row type="flex" justify="end" :gutter="10">
            <Col span="2.5">
              <Button
                @click="handleSubmit()"
                icon="ios-checkmark-outline"
                >{{ $t("system.save") }}</Button
              >
            </Col>
            <Col span="2.5">
              <Button
                @click="handlecancel()"
                icon="ios-close-outline"
                style="margin-left: 8px"
                >{{ $t("system.cancel") }}</Button
              >
            </Col>
          </Row>
        </Col>
      </Row>
    </div>
  </Modal>
</template>
<script>
    import moment from 'moment';
    export default {
        data () {
            return {
                showEditModal: false,
                employeeModel: {},
                departmentList: [],
                ruleValidate: {
                    MUID: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validmuid'),
                            trigger: 'blur,change'
                        }
                    ],
                    LoginName: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validLoginName'),
                            trigger: 'blur,change'
                        }
                    ],
                    NameCn: [
                        {
                            required: true,
                            type: 'string',
                            message: this.$t('userEdit.validNameCn'),
                            trigger: 'blur,change'
                        }
                    ]
                },
                selectGenderList: [
                    {
                        value: 1,
                        label: this.$t('userEdit.male')
                    },
                    {
                        value: 0,
                        label: this.$t('userEdit.female')
                    }
                ]
            };
        },
        created () {
            if (localStorage.currentLanguage === 'mergeEN') {
                this.currentLanguage = true;
            } else {
                this.currentLanguage = false;
            }
            this.$http.get('/Employee/QueryAllDepartmentSelect').then((response) => {
                this.departmentList = response.data;
            });
        },
        methods: {
            initPage (employee) {
                this.showEditModal = true
                if (employee === null) {
                    this.employeeModel = {}
                } else {
                    this.$http.get('/Employee/GetEmployeeById', {params: {id: employee.ID}}).then((response) => {
                        this.employeeModel = response.data;
                        if (this.employeeModel.Sex != null) {
                            this.employeeModel.Sex = this.employeeModel.Sex ? 1 : 0;
                        }
                    });
                }
            },
            handleSubmit () {
                this.$refs['addEmployeeForm'].validate((valid) => {
                    if (valid) {
                        this.employeeModel.HireDate = moment(this.employeeModel.HireDate).format(
                            'YYYY-MM-DD'
                        );
                        if (this.employeeModel.ID !== null && this.employeeModel.ID !== undefined) {
                            this.$http.post('/Employee/UpdateEmployee', this.employeeModel).then((response) => {
                                if (response.data.Message) {
                                    this.$alert(response.data.Message, this.$t('system.alter'), {
                                        dangerouslyUseHTMLString: true
                                    });
                                } else {
                                    this.$Message.success(this.$t('system.submitSuccess'));
                                    this.handlecancel()
                                    this.$emit('on-success');
                                }
                            });
                        } else {
                            this.$http.post('/Employee/CreateEmployee', this.employeeModel).then((response) => {
                                if (response.data.Message) {
                                    this.$alert(response.data.Message, this.$t('system.alter'), {
                                        dangerouslyUseHTMLString: true
                                    });
                                } else {
                                    this.$Message.success(this.$t('system.submitSuccess'));
                                    this.handlecancel()
                                    this.$emit('on-success');
                                }
                            });
                        }
                    }
                });
            },
            handlecancel () {
                this.$refs.addEmployeeForm.resetFields();
                this.employeeModel.Remark = null;
                this.showEditModal = false
            }

        }
    };
</script>
<style scoped>

</style>
