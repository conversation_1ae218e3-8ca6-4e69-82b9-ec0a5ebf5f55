// 公共
import system from '../locale/modules/system'
// 首页
import home from '../locale/modules/home'
import doctor from '../locale/modules/doctor'
// 权限
import permission from '../locale/modules/permission'
// 产品
import productLocale from '../locale/modules/productLocale'
// 别名
import batchLocale from '../locale/modules/batchLocale'

import notice from '../locale/modules/noticeLocale'
// 导航
import main from '../locale/modules/main'
// 地理信息
import locationLocal from '../locale/modules/locationLocal'
// 流向商业
import distributor from '../locale/modules/distributorList'
// 收货方主数据
import receiverLocal from './modules/receiverLocal'
// 流向管理
import salseFlowLocale from '../locale/modules/salseFlowLocale'
// 进销存管理
import inventory from '../locale/modules/inventory'

import productGroup from './modules/productGroupLocal'
// 市场数据
import marketData from './modules/marketData'

import sales from './modules/sales'

export default {
    'zh-CN': {
        main: main.zhCN.main,
        system: system.zhCN.system,
        home: home.zhCN.home,
        homeSetting: home.zhCN.homeSetting,
        userlist: permission.zhCN.userlist,
        userEdit: permission.zhCN.userEdit,
        permission: permission.zhCN.permission,
        role: permission.zhCN.role,
        notice: notice.zhCN.notice,
        // 产品
        public: productLocale.zhCN.public,
        productBatch: productLocale.zhCN.productBatch,
        bu: productLocale.zhCN.bu,
        productLine: productLocale.zhCN.productLine,
        brand: productLocale.zhCN.brand,
        ignoreMaterialHistroy: productLocale.zhCN.ignoreMaterialHistroy,
        product: productLocale.zhCN.product,
        accountingPrice: productLocale.zhCN.accountingPrice,
        // 别名
        ignoreProductBatchHistroy: batchLocale.zhCN.ignoreProductBatchHistroy,
        batchImport: batchLocale.zhCN.batchImport,
        productAnother: batchLocale.zhCN.productAnother,
        productBatchAnother: batchLocale.zhCN.productBatchAnother,
        doctor: doctor.zhCN.doctor,
        speaker: doctor.zhCN.speaker,
        // 流向商业
        distributor: distributor.zhCN.distriButorSystem,
        // 流向商业---全部
        distributorAll: distributor.zhCN.distributorAll,
        // 地理信息
        locationConfirm: locationLocal.zhCN.locationConfirm,
        // 地理信息---城市
        city: locationLocal.zhCN.city,
        // 地理信息---区县
        county: locationLocal.zhCN.county,
        // 地理信息---大区
        area: locationLocal.zhCN.area,
        // 军区管理-
        military: locationLocal.zhCN.military,
        // 系统管理--收货方类型
        receiverType: receiverLocal.zhCN.receiverType,
        // 院边店
        drugstoreNearHospital: receiverLocal.zhCN.drugstoreNearHospital,
        // 收货方主数据
        receiver: receiverLocal.zhCN.receiver,
        receiverAliasChangeRequestForm: receiverLocal.zhCN.receiverAliasChangeRequestForm,
        // 目标客户主数据
        targetReceiver: receiverLocal.zhCN.targetReceiver,
        // 院外药店
        drugstoreOfHospital: receiverLocal.zhCN.drugstoreOfHospital,
        // 流向管理-数据导入
        importData: salseFlowLocale.zhCN.importData,
        // 流向管理-数据删除
        deleteData: salseFlowLocale.zhCN.deleteData,
        // 流向管理-数据查询
        queryData: salseFlowLocale.zhCN.queryData,
        // 流向管理-流向核查
        deficiencyInspect: salseFlowLocale.zhCN.deficiencyInspect,
        // 进销存管理导航
        inventoryMain: inventory.zhCN.inventoryMain,
        // 进销存管理-流向商业库存天数
        distributorInventoryDay: inventory.zhCN.distributorInventoryDay,
        // 进销存管理
        inventoryList: inventory.zhCN.inventoryList,
        // 库存差异核查
        inventoryInspect: inventory.zhCN.inventoryInspect,
        // 进销存报表
        inventoryReport: inventory.zhCN.inventoryReport,
        // 安全库存范围设置
        safetyStockRange: inventory.zhCN.safetyStockRange,
        // 日库存差异率设置
        discrepancyRateSetting: inventory.zhCN.discrepancyRateSetting,
        // 经销商库存预估设置
        inventoryEstimateSetting: inventory.zhCN.inventoryEstimateSetting,
        // 自动服务运行状态查询
        taskStatus: system.zhCN.taskStatus,
        productGroup: productGroup.zhCN.productGroup,
        inventoryBatchNumber: inventory.zhCN.inventoryBatchNumber,
        expiringQualification: system.zhCN.expiringQualification,
        sfelog: system.zhCN.syncSFELog,
        crmData: receiverLocal.zhCN.crmData,
        cPASaleData: marketData.zhCN.cPASaleData,
        quota: sales.zhCN.quota,
        procurement: receiverLocal.zhCN.procurement

    },
    'en-US': {
        main: main.enUS.main,
        system: system.enUS.system,
        home: home.enUS.home,
        notice: notice.enUS.notice,
        homeSetting: home.zhCN.homeSetting,
        userlist: permission.enUS.userlist,
        userEdit: permission.enUS.userEdit,
        permission: permission.enUS.permission,
        role: permission.enUS.role,
        // 产品
        public: productLocale.enUS.public,
        productBatch: productLocale.enUS.productBatch,
        bu: productLocale.enUS.bu,
        productLine: productLocale.enUS.productLine,
        brand: productLocale.enUS.brand,
        accountingPrice: productLocale.enUS.accountingPrice,
        ignoreMaterialHistroy: productLocale.enUS.ignoreMaterialHistroy,
        product: productLocale.enUS.product,
        doctor: doctor.enUS.doctor,
        // 别名
        ignoreProductBatchHistroy: batchLocale.enUS.ignoreProductBatchHistroy,
        batchImport: batchLocale.enUS.batchImport,
        productAnother: batchLocale.enUS.productAnother,
        productBatchAnother: batchLocale.enUS.productBatchAnother,
        // 流向商业--公共
        distributor: distributor.enUS.distriButorSystem,
        // 流向商业---全部
        distributorAll: distributor.enUS.distributorAll,
        // 地理信息
        locationConfirm: locationLocal.enUS.locationConfirm,
        // 地理信息---城市
        city: locationLocal.enUS.city,
        // 地理信息---区县
        county: locationLocal.enUS.county,
        // 地理信息---大区
        area: locationLocal.enUS.area,
        // 军区管理-
        military: locationLocal.enUS.military,
        // 系统管理--收货方类型
        receiverType: receiverLocal.enUS.receiverType,
        // 收货方主数据
        receiver: receiverLocal.enUS.receiver,
        // 院边店
        drugstoreNearHospital: receiverLocal.enUS.drugstoreNearHospital,

        receiverAliasChangeRequestForm: receiverLocal.enUS.receiverAliasChangeRequestForm,
        targetReceiver: receiverLocal.enUS.targetReceiver,
        // 院外药店
        drugstoreOfHospital: receiverLocal.enUS.drugstoreOfHospital,
        // 流向管理-数据导入
        importData: salseFlowLocale.enUS.importData,
        // 流向管理-数据删除
        deleteData: salseFlowLocale.enUS.deleteData,
        // 流向管理-流向核查
        deficiencyInspect: salseFlowLocale.enUS.deficiencyInspect,
        // 流向管理-数据查询
        queryData: salseFlowLocale.enUS.queryData,
        // 进销存管理导航
        inventoryMain: inventory.enUS.inventoryMain,
        // 进销存管理-流向商业库存天数
        distributorInventoryDay: inventory.enUS.distributorInventoryDay,
        // 进销存管理
        inventoryList: inventory.enUS.inventoryList,
        // 库存差异核查
        inventoryInspect: inventory.enUS.inventoryInspect,
        // 进销存报表
        inventoryReport: inventory.enUS.inventoryReport,
        // 安全库存范围设置
        safetyStockRange: inventory.enUS.safetyStockRange,

        // 日库存差异率设置
        discrepancyRateSetting: inventory.enUS.discrepancyRateSetting,
        // 经销商库存预估设置
        inventoryEstimateSetting: inventory.enUS.inventoryEstimateSetting,
        // 自动服务运行状态查询
        taskStatus: system.enUS.taskStatus,
        productGroup: productGroup.enUS.productGroup,
        inventoryBatchNumber: inventory.enUS.inventoryBatchNumber,
        expiringQualification: system.enUS.expiringQualification,
        sfelog: system.enUS.syncSFELog,
        crmData: receiverLocal.enUS.crmData,
        cPASaleData: marketData.enUS.cPASaleData,
        quota: sales.enUS.quota,
        procurement: receiverLocal.zhCN.procurement
    }
}
