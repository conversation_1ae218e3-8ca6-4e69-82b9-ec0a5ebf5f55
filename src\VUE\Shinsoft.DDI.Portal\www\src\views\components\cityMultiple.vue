<!--多选城市  作者：程瑞杰 -->
<template>
  <div class="checkboxDomeComponents">
    <Poptip placement="bottom-start" @on-popper-hide="onPopperHide" trigger="hover">
      <Input
        v-model="cityNameValues"
        readonly
        @on-focus="onInputFocus"
        :placeholder="senderCityName"
        :disabled = "cityDisabled"
      />
      <div slot="content" class="CheckboxGroupTemplate">
        <div class="CheckboxGroupHide" style="white-space: pre-wrap;">
          <CheckboxGroup v-model="cityNames">
            <Checkbox :label="item.label" v-for="item in cityList" :key="item.value"></Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </Poptip>
  </div>
</template>
<script>
    export default {
        name: 'cityMultiple',
        props: ['cityList', 'senderCityName', 'cityDisabled'],
        data () {
            return {
                cityIDs: [],
                cityNames: []
            };
        },
        computed: {
            cityNameValues: function () {
                return this.cityNames.join(',');
            }
        },
        methods: {
            onPopperHide () {
                this.$emit('input', this.cityIDs); // 返回所有选中id
            },
            onInputFocus () {
                this.cityNames = [];
            }
        },
        watch: {
            // 监听数组变化，取值
            cityNames: {
                handler: function (newValue) {
                    this.cityIDs = [];
                    if (newValue.length > 0) {
                        newValue.forEach(item => {
                            this.cityList.forEach(itemOne => {
                                if (item === itemOne.label) {
                                    this.cityIDs.push(itemOne.value);
                                }
                            });
                        });
                    }
                },
                deep: true
            }
        }
    };
</script>
<style>
.ivu-poptip-rel {
  width: 100% !important;
}
</style>

<style scoped>
.CheckboxGroupTemplate {
  max-height: 200px;
  overflow: auto;
}
.CheckboxGroupTemplate::-webkit-scrollbar {
  width: 0 !important;
}

.CheckboxGroupHide {
  width: 400px;
  background: #fff;
}
.CheckboxGroupHide ul {
  margin-top: 5px;
  margin-bottom: 5px;
  clear: both;
}
.CheckboxGroupHide ul li {
  clear: both;
  list-style-type: none;
  border-bottom: 1px solid #dddee1;
  padding-bottom: 5px;
}
.CheckboxGroupHide ul li .left {
  width: 80px;
  display: inline-block;
  vertical-align: top;
}
.CheckboxGroupHide ul li .right {
  display: inline-block;
  width: 315px;
  word-wrap: break-word;
}
.CheckboxGroupHide ul:last-child li {
  border-bottom: none;
}
.addressStyle {
  float: right;
  margin-top: 5px;
  position: absolute;
  right: 20px;
  top: 3px;
  color: #666;
  cursor: pointer;
}
.leftAndRightOne {
  border-bottom: 1px solid #dddee1;
}
.leftAndRightOne:last-child {
  border-bottom: none;
}
.leftOne {
  display: inline-block;
  width: 55px;
  word-wrap: break-word;
  vertical-align: top;
}
.rightOne {
  display: inline-block;
  width: 255px;
  word-wrap: break-word;
}
.ivu-poptip-body {
  padding-right: 0 !important;
}
div.ivu-poptip {
  width: 100% !important;
}
</style>
