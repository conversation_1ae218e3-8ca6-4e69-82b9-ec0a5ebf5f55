html,
body {
  /* min-height: 550px; */
  min-height: 100vh;
  height: auto;
  padding: 0 !important;
  margin: 0 !important;
}

/* 强制解决双滚动条问题 */
.ivu-layout-content {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

.ivu-layout-content > div {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}


.ivu-layout {
  background: #ffffff !important;
}

.el-table .ascending .sort-caret.ascending {
  border-bottom-color: #fd9e00 !important;
}

.el-table .descending .sort-caret.descending {
  border-top-color: #fd9e00 !important;
}

.paddingTable td.el-table__expanded-cell {
  padding: 0;
  color: #000;
}
.el-table thead {
    color: #495060 !important;
}
.el-table .success-row {
  background: #f0f9eb;
}

.ivu-row-Top {
  padding-top: 15px;
}

.ivu-row-Bottom {
  padding-bottom: 15px;
}

.ivu-row-Bottom-30 {
  padding-bottom: 30px;
}

.ivu-row-Padding {
  padding-left: 18px;
  padding-right: 18px;
}

.ivu-row-Padding-5 {
  padding-left: 5px;
  padding-right: 5px;
}

.ivu-row-Padding-55 {
  padding-left: 23px;
}

.pageStyle {
  padding-right: 10px;
}

.ivu-row-BG {
  width: 100%;
  background: #f2f4f5;
}

.ivu-btn-primary,
.ivu-page-item-active {
  background: #fd9e00;
}

.primary_R {
  float: right;
}

.ivu-input,
.ivu-select {
  width: 98% !important;
}

.ivuInput .ivu-input {
  width: 99% !important;
}
.ivuInput .ivu-poptip,.ivu-poptip-rel{
  width: 100% !important;
}
textarea.ivu-input {
  width: 100% !important;
}

.OneHundredPercent {
  width: 260px !important;
}

.ivu-cascader-arrow {
  right: 20px !important;
}

.ivu-input-icon {
  right: 2% !important;
}

.OneHundredPercent .ivu-input,
.OneHundredPercent .ivu-select {
  width: 100% !important;
}
textarea.ivu-input {
  font-size: 12px !important;
}

.uploadListA {
  height: 32px;
  line-height: 32px;
  font-size: 16px;
  margin-left: 10px;
}

.primary_L {
  float: left;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1.2s linear infinite;
}

.primary_LEFT {
  margin-left: 10px;
}

.btn-custom-cancel {
  float: right;
  margin-left: 10px;
}

.helpCircledBut {
  float: left !important;
}

.el-tabs__item:hover {
  color: #fd9e00 !important;
  cursor: pointer;
}

.el-tabs__active-bar
{
  position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    background-color: #fd9e00 !important;
    z-index: 1;
    -webkit-transition: -webkit-transform .3s cubic-bezier(.645,.045,.355,1);
    transition: -webkit-transform .3s cubic-bezier(.645,.045,.355,1);
    transition: transform .3s cubic-bezier(.645,.045,.355,1);
    transition: transform .3s cubic-bezier(.645,.045,.355,1), -webkit-transform .3s cubic-bezier(.645,.045,.355,1);
    transition: transform .3s cubic-bezier(.645,.045,.355,1),-webkit-transform .3s cubic-bezier(.645,.045,.355,1);
    list-style: none;
}

.el-tabs__item.is-active {
    color: #fd9e00  !important;
}

.ivu-select-multiple .ivu-select-item-selected {
  color: rgba(253,158,0,.9) !important;
  background: #fff;
}

.ivu-select-visible .ivu-select-selection {
  border-color: #fd9e00 !important;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(255,243,228,.2);
}

.ivu-dropdown-item a {
  color: #fd9e00 !important;
  background: 0 0;
  text-decoration: none;
  outline: 0;
  cursor: pointer;
  transition: color .2s ease;
}

.ivu-form-item-content a {
  color: #fd9e00 !important;
  background: 0 0;
  text-decoration: none;
  outline: 0;
  cursor: pointer;
  transition: color .2s ease;
}

.ivu-select-multiple .ivu-select-item-selected:after {
  color: rgba(253,158,0,.9) !important;
}

.ivu-spin {
  color: #fd9e00 !important;
  vertical-align: middle;
  text-align: center;
}

/* 表格鼠标滑过 */
tr.ivu-table-row-hover td {
  background-color: #fdd8ad !important;
}

.ivu-date-picker-cells-focused em {
  box-shadow: 0 0 0 1px #fdd8ad inset !important;
}

.ivu-date-picker-cells-cell-range:before {
  content: '';
  display: block;
  background: #fdd8ad !important;
  border-radius: 0;
  border: 0;
  position: absolute;
  top: 2px;
  bottom: 2px;
  left: 0;
  right: 0;
}

.ivu-date-picker-cells-cell-today em:after {
  content: '';
  display: block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #fdd8ad !important;
  position: absolute;
  top: 1px;
  right: 1px;
}

.ivu-date-picker-header-label:hover {
  color: #fdd8ad !important;
}

.ivu-steps-item.ivu-steps-status-process .ivu-steps-head-inner {
  border-color: #fd9e00 !important;
  background-color: #fd9e00 !important;
}

.ivu-date-picker-cells-cell-selected em, .ivu-date-picker-cells-cell-selected:hover em {
  background: #fd9e00 !important;
  color: #fff;
}

.ivu-cascader-menu .ivu-cascader-menu-item-active {
  background-color: #f3f3f3;
  color: #fd9e00 !important;
}

/* 表格选中行 */

.stopReceiverList tr.ivu-table-row-highlight td {
  color: #fff !important;
  background-color: #fdd8ad !important;
}

.spanStyle0 {
  width: 240px !important;
}

.spanStyle1 {
  width: 220px !important;
}

.spanStyle2 {
  width: 200px !important;
}

.spanStyle3 {
  width: 180px !important;
}

.spanStyle4 {
  width: 160px !important;
}

.spanStyle5 {
  width: 140px !important;
}

.spanStyle6 {
  width: 120px !important;
}

.ivu-picker-panel-body {
  background: #fff !important;
}

.verify-bar-area .verify-move-block:hover {
  background-color: #fd8900 !important;
  color: #FFFFFF;
}

.verify-bar-area .verify-move-block {
  background-color: #fd8900 !important;
  color: #FFFFFF;
}

.verify-bar-area .verify-left-bar {
  position: absolute;
  top: -1px;
  left: -1px;
  background: #fff3e4 !important;
  cursor: pointer;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  border: 1px solid #fd8900  !important;
}

.ivu-input:hover {
  border-color: #fd8900 !important;
}
.ivu-input:focus {
  border-color: #fd8900 !important;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(253,216,173,.2) !important;
}

.ivu-select-selection-focused, .ivu-select-selection:hover {
  border-color: #fd8900 !important;
}

.ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #fd8900 !important;
  background-color: #fd8900 !important;
}

.ivu-input-number:hover{border-color:#fd8900 !important;}
.ivu-input-number:focus{border-color:#fd8900 !important;outline:0;-webkit-box-shadow:0 0 0 2px rgba(253,137,0,.2)!important;box-shadow:0 0 0 2px rgba(253,137,0,.2) !important;}

element.style {
  margin: 0px 8px;
}
* {
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}
.el-tabs__item :hover {
  color: #fd8900 !important;
}

.el-button--text {
  color: #fd8900 !important;
  background: 0 0;
  padding-left: 0;
  padding-right: 0;
}

.ivu-btn:hover {
  color: #fd8900 !important;
  background-color: #fff;
  border-color: #fd8900 !important;
}

.ivu-alert-info {
  border: 1px solid #fd8900 !important;
  background-color: #fff3e4 !important;
}

.ivu-alert-info .ivu-alert-icon {
  color: #fd8900 !important;
}

.ivu-date-picker-cells-cell:hover em {
  background: #fff3e4 !important;
}

.ivu-date-picker-cells-month .ivu-date-picker-cells-cell-focused, .ivu-date-picker-cells-year .ivu-date-picker-cells-cell-focused {
  background-color: #fff3e4 !important;
}

.ivu-switch-checked {
  border-color: #fd8900 !important;
  background-color: #ffaa47 !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #fd8900 !important;
  border-color: #fd8900 !important;
}

.ivu-tag-blue, .ivu-tag-blue.ivu-tag-dot .ivu-tag-dot-inner {
  background: #fd8900 !important;
}

/* 上传附件遮罩滚动条样式  start*/
.progressBarBox {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}
.progressBarCon {
  padding: 10px 30px;
  position: absolute;
  top: 35%;
  left: 50%;
  z-index: 99999;
  width: 500px;
  margin-left: -250px;
  background: #fff !important;
  border-radius: 5px;
}
.progressBarCon #progressCss {
  width: 100%;
  height: 20px;
  border: 1px solid #ccc;
  border-radius: 15px;
  overflow: hidden; /*注意这里*/
  box-shadow: 0 0 5px 0px #ddd inset;
}
.progressBarCon #progressCss span {
  display: inline-block;
  width: 95%;
  height: 100%;
  background: linear-gradient(
    45deg,
    #2989d8 30%,
    #7db9e8 31%,
    #7db9e8 58%,
    #2989d8 59%
  );
  background-size: 60px 30px;
  text-align: center;
  color: #fff;
  animation: load 3s infinite;
}
@keyframes load {
  0% {
    width: 0%;
  }
  100% {
    width: 95%;
  }
}
.progressBarCon p {
  height: 30px;
  line-height: 30px;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-top: 10px;
  margin-bottom: 10px;
  white-space: nowrap;
}
/* 上传附件遮罩滚动条样式  End*/
h4 {
  font-size: 14px;
}
.CardCss .ivu-card-head {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
.CardCss .ivu-card-body {
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
/*.ivu-table-cell{
	  padding-left: 4px!important;
	  padding-right: 0!important;
  } */
  .el-tree-node__content {
  line-height: 26px;
  overflow: hidden;
}
.spanBox {
  line-height: 26px !important;
  height: 26px;
  display: inline-block;
  width: 100%;
}

.ivu-table-cell {
  padding-left: 6px;
  padding-right: 6px;
}
.ivu-form .ivu-form-item-label {
  /* word-break: break-all; */
  word-wrap: break-word;
  white-space: pre-wrap;
}
.msgBox {
  width: 80%;
  max-height: 90vh;
  overflow: auto;
}
.splitMethod .ivu-input,
.splitMethod .ivu-select {
  width: 100% !important;
}
.el-table.paddingTable th{
  background: #f8f8f9;
}

.buWidth .ivuInput .ivu-input {
  width: 98% !important;
}
.el-cascader-menu__item {
  font-size: 12px !important;
}
.el-cascader-menu__item.is-active {
  color: #fd8900 !important;

}
.el-select-dropdown__item {
  font-size: 12px !important;
}
.el-select-dropdown__item.selected {
  color: #fd8900 !important;
  font-weight: 400 !important;
}

.el-select .el-input.is-focus .el-input__inner {
  border-color:  #fd8900 !important;

}

.el-input__inner{
  font-size: 12px;
}

.el-input.is-active .el-input__inner, .el-input__inner:focus {
  border-color: #fd8900 !important;
  outline: 0 !important;
}
.el-cascader__label {
  font-size:12px !important;
}

.el-input__icon {
  line-height: 0px !important;
}
.el-checkbox__input.is-checked+.el-checkbox__label {
  color: #606266 !important;
}
/* 调整下拉框文字大小，使其与文本框保持一致 */
.custom-select :deep(.el-input__wrapper) {
  font-size: 12px !important;
}

.custom-select :deep(.el-input__inner) {
  font-size: 12px !important;
}

.custom-select :deep(.el-select__placeholder) {
  font-size: 12px !important;
}

.custom-select :deep(.el-select__selected-item) {
  font-size: 12px !important;
}

/* 调整下拉选项的文字大小 */
:deep(.el-select-dropdown .el-select-dropdown__item) {
  font-size: 12px !important;
}

/* 确保下拉框输入框的文字大小 */
.custom-select :deep(.el-select .el-input .el-input__wrapper .el-input__inner) {
  font-size: 12px !important;
}