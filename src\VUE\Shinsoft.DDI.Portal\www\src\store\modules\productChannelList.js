const productChannelList = {
    state: {
        // 所有字段
        ReceiverProductChannelList: {},
        dataChildChangeProductChannel: []
    },
    mutations: {
        dataReceiverProductChannel: (state, payload) => {
            state.ReceiverProductChannelList = payload
        },
        // 变更产品渠道--保存数据管理
        saveChildChangeProductChannel: (state, payload) => {
            state.dataChildChangeProductChannel.push(payload)
        }
    }
}
export default productChannelList
