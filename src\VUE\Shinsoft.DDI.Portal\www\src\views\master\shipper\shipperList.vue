<!--货主管理页面 - 基于Element Plus组件-->
<template>
  <div>
    <!-- 面包屑导航 -->
    <div class="page-header management-style">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>主数据管理</el-breadcrumb-item>
        <el-breadcrumb-item>货主管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-container">
      <el-row :gutter="16" type="flex">
        <el-col :span="6">
          <el-input v-model="filter.name" placeholder="货主名称" clearable />
        </el-col>
        <el-col :span="6">
          <el-input v-model="filter.code" placeholder="货主编码" clearable />
        </el-col>
        <el-col :span="6">
          <el-button icon="Search" @click="search" :loading="loading">查询</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-container">
      <div class="action-buttons">
        <el-button icon="CirclePlus" @click="addShipper">新增货主</el-button>
        <el-button icon="Download" @click="exportShipperList">导出</el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="shipperList" stripe size="small" v-loading="loading">
        <el-table-column label="序号" width="60">
          <template #default="{ $index }">
            {{ (filter.pageIndex - 1) * filter.pageSize + $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="code" label="货主编码" width="120" />
        <el-table-column prop="name" label="货主名称" min-width="200" />
        <el-table-column prop="shortName" label="简称" width="100" />
        <el-table-column prop="address" label="地址" min-width="200" />
        <el-table-column prop="contact" label="联系人" width="100" />
        <el-table-column prop="phone" label="电话" width="130" />
        <el-table-column prop="status" label="状态" width="80" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="编辑" placement="top">
              <el-button icon="Edit" circle size="small" @click="editShipper(row)" />
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button icon="Delete" circle size="small" @click="removeShipper(row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-container">
      <el-pagination v-model:current-page="filter.pageIndex" v-model:page-size="filter.pageIndexSize"
        :page-sizes="pageSizeOpts" :total="totalCount" layout="total, sizes, prev, pager, next, jumper"
        @size-change="changePageSize" @current-change="changePage" />
    </div>

    <!-- 货主对话框组件 -->
    <ShipperDialog v-model:visible="showAddDialog" :record-id="currentEditId" @success="handleDialogSuccess" />

  </div>
</template>

<script>
import ShipperDialog from './components/shipperDialog.vue'
import fileDownload from 'js-file-download'
import { shipperApi } from '@/api/shipperApi'

export default {
  name: 'ShipperList',
  components: {
    ShipperDialog 
  },
  data() {
    return {
      loading: false,
      pageSizeOpts: [10, 20, 50, 100],
      // 对话框相关
      showAddDialog: false,
      currentEditId: null,
      filter: {
        pageIndex: 1,
        pageSize: 10,
        shipperName: '',
        shipperCode: '',
        order: "LastEditTime desc",
      },
      totalCount: 0,
      shipperList: []
    };
  },
  mounted() {
    this.loadShipperList();
  },
  methods: {
    /**
     * 加载货主列表数据
     */
    loadShipperList() {
      this.loading = true;
      shipperApi.queryShipper(this.filter)
        .then((response) => {
          if (response.data && response.data.success) {
            // axios拦截器已经处理了嵌套的data结构，直接访问datas即可
            this.shipperList = response.data.datas || [];
            this.totalCount = response.data.total || 0;

          } else {
            this.shipperList = [];
            this.totalCount = 0;
          }
        })
        .catch((error) => {
          this.shipperList = [];
          this.totalCount = 0;
          this.$message.error("查询货主失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * 查询按钮点击事件
     */
    search() {
      this.filter.pageIndex = 1;
      this.loadShipperList();
    },

    /**
     * 分页大小改变事件
     * @param {number} size - 新的分页大小
     */
    changePageSize(size) {
      this.filter.pageIndexSize = size;
      this.filter.pageIndex = 1;
      this.loadShipperList();
    },

    /**
     * 页码改变事件
     * @param {number} page - 新的页码
     */
    changePage(page) {
      this.filter.pageIndex = page;
      this.loadShipperList();
    },

    /**
     * 新增货主
     */
    addShipper() {
      this.currentEditId = null
      this.showAddDialog = true
    },

    /**
     * 编辑货主
     * @param {Object} row - 货主数据行
     */
    editShipper(row) {
      this.currentEditId = row.id
      this.showAddDialog = true
    },

    /**
     * 对话框成功事件处理
     */
    handleDialogSuccess() {
      // 刷新列表数据
      this.loadShipperList()
      console.log('货主保存成功，刷新列表')
    },

    /**
     * 删除货主
     * @param {Object} row - 货主数据行
     */
    removeShipper(row) {
      this.$confirm('确定删除该货主吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        shipperApi.deleteShipper({ id: row.id }).then((response) => {
          if (response.data.success) {
            this.$message.success('删除成功');
            this.loadShipperList();
          } else {
            this.$message.error('删除失败');
          }
        });
      }).catch(() => {
        // 取消删除操作
      });
    },

    /**
     * 导出货主列表
     */
    exportShipperList() {
      shipperApi.exportShipper(this.filter).then((result) => {
        var filename = '货主信息.xlsx'
        fileDownload(result.data, filename)
      })
    }
  }
};
</script>
