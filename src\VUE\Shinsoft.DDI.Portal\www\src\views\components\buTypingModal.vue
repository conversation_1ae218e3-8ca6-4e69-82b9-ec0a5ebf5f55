<!--BU 分型弹框  作者：程瑞杰 -->
<template>
  <div style="width:100%;" class="ivuInput">
    <Poptip placement="bottom-start" @on-popper-hide="allMateriallBlur" trigger="hover">
      <Input v-model="addressIndex" :placeholder="placeholderText" readonly
             @on-focus="allMateriallFocus" />
      <div slot="content" class="CheckboxGroupTemplate">
        <div class="CheckboxGroupHide" style="white-space: pre-wrap;">
          <ul v-for="(todo,index) in buTypingAttr">
            <li>
              <div class="left">
                <Checkbox :indeterminate="todo.indeterminate" :value="todo.selectedAll" @click.prevent.native="handleCheckAll(index,buTypingAttr)"><b>{{todo.label}}</b></Checkbox>
              </div>
              <div class="right">
                <CheckboxGroup v-model="todo.selectedChildren" @on-change="checkAllGroupChange(index,todo.selectedChildren,buTypingAttr)">
                  <Checkbox v-for="itemTwo in todo.children" :key="itemTwo.label" :label="itemTwo.label">{{itemTwo.label}}</Checkbox>
                </CheckboxGroup>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </Poptip>
  </div>
</template>
<script>
    export default {
        props: ['buTypingAttr', 'placeholderText'],
        data () {
            return {
                addressAttrHide: false,
                addressAttr: []
            };
        },
        methods: {
            // 区域--全选
            handleCheckAll (index, dataAll) {
                let childrenAll = [];
                dataAll[index].children.forEach(item => {
                    childrenAll.push(item.label);
                });
                if (dataAll[index].indeterminate) {
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].selectedAll = !dataAll[index].selectedAll;
                }
                dataAll[index].indeterminate = false;
                if (dataAll[index].selectedAll) {
                    dataAll[index].selectedChildren = childrenAll;
                    childrenAll.forEach(val => {
                        this.addressAttr.push(val)
                    })
                } else {
                    dataAll[index].selectedChildren = [];
                    let c = new Set(childrenAll);
                    let d = new Set(this.addressAttr);
                    this.addressAttr = [...new Set([...d].filter(x => !c.has(x)))];
                }
                this.allMateriallBlur();
            },
            // 区域--change
            checkAllGroupChange (index, data, dataAll) {
                let selectedChildrenData = [];
                dataAll.forEach(val => {
                    if (val.selectedChildren.length > 0) {
                        val.selectedChildren.forEach(value => {
                            selectedChildrenData.push(value);
                        })
                    }
                })
                this.addressAttr = selectedChildrenData;
                let childrenAll = [];
                dataAll[index].children.forEach(item => {
                    childrenAll.push(item.label);
                });
                if (data.length == childrenAll.length) {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = true;
                } else if (data.length > 0) {
                    dataAll[index].indeterminate = true;
                    dataAll[index].selectedAll = false;
                } else {
                    dataAll[index].indeterminate = false;
                    dataAll[index].selectedAll = false;
                }
                this.allMateriallBlur();
            },
            // 得到焦点
            allMateriallFocus () {
                // this.$emit("input", null);
                this.addressAttr = [];
                this.buTypingAttr.forEach(element => {
                    element.selectedAll = false;
                    element.indeterminate = false;
                    element.selectedChildren = [];
                })
                this.addressAttrHide = true;
            },
            // 清值
            onInputFocus () {
                this.addressAttr = [];
                this.buTypingAttr.forEach(element => {
                    element.selectedAll = false;
                    element.indeterminate = false;
                    element.selectedChildren = [];
                })
            },
            allMateriallBlur () {
                let provinceIndex = [];
                let buTypingBox = [];
                var aa = this.addressIndex.replace(/(.)(?=[^$])/g, '$1').split(',');
                for (
                    let index = 0;
                    index < this.buTypingAttr.length;
                    index++
                ) {
                    for (
                        let num = 0;
                        num < this.buTypingAttr[index].children.length;
                        num++
                    ) {
                        for (let y = 0; y < aa.length; y++) {
                            if (
                                this.buTypingAttr[index].children[num].label == aa[y]
                            ) {
                                provinceIndex.push(this.buTypingAttr[index].children[num].value);
                                buTypingBox.push(this.buTypingAttr[index].children[num].packing);
                            }
                        }
                    }
                }
                let buTypingVal = '';
                if (buTypingBox.length === 1) {
                    buTypingVal = buTypingBox[0];
                }
                this.$emit('buTypingVal', buTypingVal);
                this.$emit('input', provinceIndex);
            }
        },
        computed: {
            addressIndex: function () {
                return this.addressAttr.join(',');
            }
        }
    };
</script>
<style>
.ivu-poptip-rel{
  width: 100%!important;
}
</style>

<style scoped>
.CheckboxGroupTemplate {
  max-height: 250px;
  overflow: auto;
}
.CheckboxGroupTemplate::-webkit-scrollbar { width: 0 !important }

.CheckboxGroupHide {
  width: 400px;
  background: #fff;
}
.CheckboxGroupHide ul{
  margin-top: 5px;
  margin-bottom: 5px;
  clear: both;
}
.CheckboxGroupHide ul li{
  clear: both;
  list-style-type: none;
  border-bottom: 1px solid #dddee1;
  padding-bottom: 5px;
}
.CheckboxGroupHide ul li .left{
  width: 80px;
  display: inline-block;
  vertical-align:top;
}
.CheckboxGroupHide ul li .right{
  display: inline-block;
  width: 315px;
  word-wrap:break-word;
}
.CheckboxGroupHide ul:last-child li{
  border-bottom:none;
}
.addressStyle {
  float: right;
  margin-top: 5px;
  position: absolute;
  right: 20px;
  top: 3px;
  color: #666;
  cursor: pointer;
}
.leftAndRightOne{
  border-bottom: 1px solid #dddee1;
}
.leftAndRightOne:last-child{
  border-bottom: none;
}
.leftOne{
  display: inline-block;
  width: 55px;
  word-wrap:break-word;
  vertical-align:top;
}
.rightOne{
  display: inline-block;
  width: 255px;
  word-wrap:break-word;
}
.ivu-poptip-body {
    padding-right: 0!important;
}
div.ivu-poptip{
  width: 100%!important;
}
</style>
