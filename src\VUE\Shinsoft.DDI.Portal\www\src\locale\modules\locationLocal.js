// 导航-->中英文
export const location = {
    'zhCN': {
        // confirm提示
        locationConfirm: {
            whetherDeleteCounty: '是否删除该区县?',
            whetherDeleteCity: '是否删除该城市?',
            whetherDeleteArea: '是否删除该大区?',
            whetherDeleteMilitary: '是否删除该军区?'
        },
        // 城市管理
        city: {
            province: '省份名称',
            nameCn: '城市名称',
            nameEn: '英文名称',
            shortName: '简称',
            creator: '创建人',
            createTime: '创建时间',
            finalEditor: '修改人',
            finalEditTime: '修改时间',
            provinceBeEmpty: '省市名称不能为空',
            provinceLength: '大区描述过长',
            shortNameLength: '简称过长',
            nameEnLength: '英文名称过长',
            nameCnLength: '中文名称过长',
            nameCnBeEmpty: '城市名称不能为空',
            provinceIDBeEmpty: '省份不能为空'
        },
        // 区县管理
        county: {
            city: '城市',
            nameCn: '区县名称',
            nameEn: '英文名称',
            shortName: '简称',
            creator: '创建人',
            createTime: '创建时间',
            finalEditor: '修改人',
            finalEditTime: '修改时间',
            countyNameBeEmpty: '区县名称不能为空',
            countyNameLength: '区县名称过长',
            provinceIDsBeEmpty: '省市名称不能为空',
            countyNameEnLength: '区县英文名称过长',
            countyShortName: '区县简称过长',
            delCountyName: '删除区县成功'
        },
        area: {
            name: '大区',
            description: '大区描述',
            creator: '创建人',
            createTime: '创建时间',
            finalEditor: '修改人',
            finalEditTime: '修改时间',
            areaNameBeEmpty: '大区名称不能为空',
            areaNameLength: '大区名称过长',
            provinceIDsBeEmpty: '省市名称不能为空',
            descriptionLength: '大区描述过长',
            delAreaName: '删除大区成功'
        },
        // 军区管理
        military: {
            militaryName: '军区名称',
            nameEn: '英文名称',
            abbreviation: '简称',
            militaryNameBeEmpty: '军区名称不能为空',
            militaryNameLength: '军区名称过长',
            abbreviationBeEmpty: '军区简称不能为空',
            abbreviationLength: '军区简称过长',
            creator: '创建人',
            createTime: '创建时间',
            finalEditor: '修改人',
            finalEditTime: '修改时间',
            delMilitaryName: '删除军区成功'
        }

    },
    'enUS': {
        // confirm提示
        locationConfirm: {
            whetherDeleteCounty: 'Are you sure you want to delete it?',
            whetherDeleteCity: 'Are you sure you want to delete it?',
            whetherDeleteArea: 'Are you sure you want to delete it?',
            whetherDeleteMilitary: 'Are you sure you want to delete it?'
        },
        // 城市管理
        city: {
            province: 'Province',
            nameCn: 'Name',
            nameEn: 'English Name',
            shortName: 'Short Name',
            creator: 'Creator',
            createTime: 'CreateTime',
            finalEditor: 'FinalEditor',
            finalEditTime: 'FinalEditTime',
            provinceBeEmpty: 'Required',
            provinceLength: 'Description is too long.',
            shortNameLength: 'City short name is too long.',
            nameEnLength: 'City name is too long.',
            nameCnLength: 'City english name is too long.',
            nameCnBeEmpty: 'Required',
            provinceIDBeEmpty: 'Required'
        },
        // 区县管理
        county: {
            city: 'City',
            nameCn: 'Name',
            nameEn: 'English Name',
            shortName: 'Short Name',
            creator: 'Creator',
            createTime: 'CreateTime',
            finalEditor: 'FinalEditor',
            finalEditTime: 'FinalEditTime',
            countyNameBeEmpty: 'Required',
            countyNameLength: 'County name is too long.',
            provinceIDsBeEmpty: 'Required',
            countyNameEnLength: 'City short name is too long.',
            countyShortName: 'County short name is too long.',
            delCountyName: 'Success'
        },
        area: {
            name: 'Name',
            description: 'Description',
            creator: 'Creator',
            createTime: 'CreateTime',
            finalEditor: 'FinalEditor',
            finalEditTime: 'FinalEditTime',
            areaNameBeEmpty: 'Required',
            areaNameLength: 'Area name is too long.',
            provinceIDsBeEmpty: 'Required',
            descriptionLength: 'Description is too long.',
            delAreaName: 'Success'
        },
        // 军区管理
        military: {
            militaryName: 'Military Name',
            nameEn: 'English Name',
            abbreviation: 'Abbreviation',
            militaryNameBeEmpty: 'Required',
            militaryNameLength: 'Military name is too long',
            abbreviationBeEmpty: 'Required',
            abbreviationLength: ' abbreviation is too long',
            creator: 'Creator',
            createTime: 'CreateTime',
            finalEditor: 'FinalEditor',
            finalEditTime: 'FinalEditTime',
            delMilitaryName: 'Success'
        }
    }
}
export default location
