import Main from "@/views/Main.vue";
export const loginRouter = {
  path: "/login",
  name: "login",
  meta: {
    title: "Login - 登录",
    requireAuth: true,
  },
  component: () => import("@/views/login.vue"),
  alias: "/",
};
export const loginJump = {
  path: "/loginJump",
  meta: {
    title: "登录跳转",
  },
  name: "loginJump",
  component: () => import("@/views/errorpage/loginJump.vue"),
};
export const page403 = {
  path: "/403",
  meta: {
    title: "403-权限不足",
    requireAuth: true,
  },
  name: "error-403",
  component: () => import("@/views/errorpage/403.vue"),
};

export const page404 = {
  path: "/404",
  meta: {
    title: "404-页面不存在或已被删除",
  },
  name: "error-404",
  component: () => import("@/views/errorpage/404.vue"),
};
export const page500 = {
  path: "/500",
  meta: {
    title: "500-服务端错误",
  },
  name: "error-500",
  component: () => import("@/views/errorpage/500.vue"),
};
// 作为Main组件的子页面展示并且在菜单显示的路由写在appRouter里
export const appRouter = [
  {
    path: "/home",
    name: "home",
    title: "首页",
    component: Main,
    children: [
      {
        path: "index",
        title: "首页",
        name: "index",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () => import("@/views/home/<USER>"),
      },
    ],
  },
  {
    path: "/master",
    name: "master",
    title: "主数据管理",
    component: Main,
    children: [
      {
        path: "shipper",
        title: "货主管理",
        name: "shipperList",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/master/shipper/shipperList.vue"),
      },
      {
        path: "distributor",
        title: "收货方管理",
        name: "distributorList",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/master/distributor/distributorList.vue"),
      },
      // 产品管理
      {
        path: "product",
        title: "产品管理",
        name: "productList",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/master/product/productList.vue"),
      },
    ],
  },
  {
    path: "/ddi-config",
    name: "ddi-config",
    title: "DDI配置监控",
    component: Main,
    children: [
      // DDI配置 - 父级路由
      {
        path: "config",
        title: "DDI配置",
        name: "ddiConfig",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
      },
      // 经销商配置
      {
        path: "config/distributor",
        title: "经销商配置",
        name: "distributorConfig",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/ddi-config/config/distributorConfig.vue"),
      },
      // 产品配置
      {
        path: "config/product",
        title: "产品配置",
        name: "productConfig",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/ddi-config/config/productConfig.vue"),
      },
      // 客户端配置
      {
        path: "config/client",
        title: "客户端配置",
        name: "clientConfig",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/ddi-config/config/clientConfig.vue"),
      },
      // DDI监控
      {
        path: "monitor",
        title: "DDI监控",
        name: "ddiMonitor",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/ddi-config/monitor/ddiMonitor.vue"),
      },
    ],
  },
  {
    path: "/salesflow-cleaning",
    name: "salesflow-cleaning",
    title: "数据清洗",
    component: Main,
    children: [
      // 产品别名
      {
        path: "alias/product",
        title: "产品别名",
        name: "productAlias",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow-cleaning/alias/productAlias.vue"),
      },
      // 收货方别名
      {
        path: "alias/distributor",
        title: "收货方别名",
        name: "distributorAlias",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow-cleaning/alias/distributorAlias.vue"),
      },
      // 流向导入日志
      {
        path: "import",
        title: "流向导入日志",
        name: "importSalesflow",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow-cleaning/import/importSalesflow.vue"),
      },
    ],
  },
  {
    path: "/salesflow",
    name: "salesflow",
    title: "数据查询",
    component: Main,
    children: [
      // 日数据查询 - 父级路由
      {
        path: "daily",
        title: "日数据查询",
        name: "dailyQuery",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
      },
      // 日销售流向查询
      {
        path: "daily/salesflow",
        title: "日销售流向查询",
        name: "dailySalesFlowQuery",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow/daily/dailySalesFlowQuery.vue"),
      },
      // 日采购查询
      {
        path: "daily/purchase",
        title: "日采购查询",
        name: "dailyPurchaseQuery",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow/daily/dailyPurchaseQuery.vue"),
      },
      // 日库存查询
      {
        path: "daily/inventory",
        title: "日库存查询",
        name: "dailyInventoryQuery",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow/daily/dailyInventoryQuery.vue"),
      },
      // 月数据查询 - 父级路由
      {
        path: "monthly",
        title: "月数据查询",
        name: "monthlyQuery",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
      },
      // 月销售流向查询
      {
        path: "monthly/salesflow",
        title: "月销售流向",
        name: "monthlySalesFlowQuery",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow/monthly/monthlySalesFlowQuery.vue"),
      },
      // 月采购查询
      {
        path: "monthly/purchase",
        title: "月采购查询",
        name: "monthlyPurchaseQuery",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/salesflow/monthly/monthlyPurchaseQuery.vue"),
      },
    ],
  },
  {
    path: "/system",
    name: "system",
    title: "系统管理",
    component: Main,
    children: [
      // 用户管理
      {
        path: "employee/userlist",
        title: "用户管理",
        name: "userlist",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () => import("@/views/system/employee/userlist.vue"),
      },
      // 字典管理
      {
        path: "dictionary/dictionarylist",
        title: "字典管理",
        name: "dictionarylist",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () => import("@/views/system/dictionaries/dictionary.vue"),
      },
      // 日志管理 - 父级路由
      {
        path: "log",
        title: "日志管理",
        name: "logManagement",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
      },
      // 操作日志查询
      {
        path: "log/operationlog",
        title: "操作日志查询",
        name: "operationlog",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/system/logManagement/eventLogQuery.vue"),
      },
      // 异常日志查询
      {
        path: "log/exceptionlog",
        title: "异常日志查询",
        name: "exceptionlog",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/system/logManagement/exceptionLogQuery.vue"),
      },
      // 客户端日志查询
      {
        path: "log/clientlog",
        title: "客户端日志查询",
        name: "clientlog",
        meta: {
          requireAuth: true,
        },
        icon: "compose",
        component: () =>
          import("@/views/system/logManagement/clientLogQuery.vue"),
      },
    ],
  },
];
export const routers = [
  loginRouter,
  ...appRouter,
  page403,
  page404,
  page500,
  loginJump,
];
