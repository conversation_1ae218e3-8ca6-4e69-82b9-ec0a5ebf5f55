<!--配置角色 作者：盛波-->
<template>
  <div>
    <Row class="ivu-row-Bottom">
      <Col span="4">
        <!-- 姓名-->
        <p class="UploadStyle">{{ $t("userlist.searchname") }}:{{employee.NameCn}}</p>
      </Col>
      <Col span="4">
        <!-- 部门 -->
        <p class="UploadStyle">{{ $t("userlist.department") }}：{{employee.Department}}</p>
      </Col>
      <Col span="4">
        <p class="UploadStyle">{{ $t('userlist.loginName') }}：{{employee.LoginName}}</p>
      </Col>
      <Col span="12">
        <!-- 角色 -->
        <p class="UploadStyle">
          {{ $t("productGroup.role") }}：
          <Tag v-for="item in employee.Roles" :key="item.ID" :name="item.Name">{{ item.Name }}</Tag>
        </p>
      </Col>
    </Row>
    <Row class="ivu-row-Bottom">
      <Col span="4">
        <!-- 角色名称-->
        <Input v-model="filter.Name" :placeholder="$t('productGroup.roleListName')" />
      </Col>
      <Col span="4">
        <Select v-model="filter.IsChecked" clearable :placeholder="$t('productGroup.assignedRole')">
          <Option v-for="item in hasRole" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </Col>
      <Col span="6">
        <!-- 查询 -->
        <Button
          @click="search"
          icon="ios-search-strong"
          class="helpCircledBut"
        >{{ $t('system.search') }}</Button>
        <tips></tips>
      </Col>
    </Row>
    <Row>
      <Col span="24">
        <!-- stripe属性  隔行换色 -->
        <Table stripe size="small" :columns="columns" :data="roleData"></Table>
      </Col>
    </Row>
    <Row class="ivu-row-Top">
      <Col span="24">
        <Page
          :total="totalCount"
          :page-size-opts="pageSizeOpts"
          :current="filter.Page"
          :page-size="filter.Per"
          size="small"
          show-total
          show-elevator
          show-sizer
          @on-change="changePage"
          @on-page-size-change="changePageSize"
          class="primary_R"
        ></Page>
      </Col>
    </Row>
    <div slot="footer">
      <Row class="ivu-row-Padding-5 ivu-row-Top">
        <Col span="24">
          <Row type="flex" justify="end" :gutter="10">
            <Col span="2.5">
              <Button
                @click="handlecancel()"
                icon="ios-close-outline"
                style="margin-left: 8px"
              >{{$t('system.close')}}</Button>
            </Col>
          </Row>
        </Col>
      </Row>
    </div>
  </div>
</template>
<script>
    export default {
        // 组件
        components: { },
        props: ['employeeID', 'isShow'],
        data () {
            return {
                isLockQuery: false,
                employee: {},
                dataAuthorityEmployeeID: 0,
                totalCount: 0,
                pageSizeOpts: this.$constDefinition.pageSizeOpts,
                role: {},
                filter: {
                    Page: 1,
                    Per: 10,
                    EnumRoleType: 1
                },
                hasRole: [
                    {
                        value: this.$constDefinition.yesOrDenyValue.yes, // "true"
                        label: this.$constDefinition.yesOrDeny.yes // 是
                    },
                    {
                        value: this.$constDefinition.yesOrDenyValue.deny, // "false"
                        label: this.$constDefinition.yesOrDeny.deny // 否
                    }
                ],
                columns: [
                    {
                        title: this.$t('system.no'), // 编号
                        width: 70,
                        align: 'center',
                        type: 'index'
                    },
                    {
                        title: this.$t('productGroup.roleListName'),
                        key: 'Name'
                    },
                    {
                        title: this.$t('productGroup.roleDescription'),
                        key: 'Description'
                    },
                    {
                        title: this.$t('system.action'), // 操作
                        key: '',
                        width: 120,
                        render: (h, params) => {
                            if (
                                this.employee.Roles.findIndex(item => item.ID === params.row.ID) >
                                -1
                            ) {
                                return h('div', [
                                    h(
                                        'Tooltip',
                                        {
                                            props: {
                                                placement: 'top',
                                                content: this.$t('productGroup.deleteRole')
                                            }
                                        },
                                        [
                                            h('Icon', {
                                                props: {
                                                    size: '20',
                                                    type: 'android-remove-circle'
                                                },
                                                style: {
                                                    marginRight: '5px',
                                                    cursor: 'pointer'
                                                },
                                                on: {
                                                    click: () => {
                                                        this.handleDeleteEmployeeRoleAndDataAuthority(
                                                            params.row
                                                        );
                                                    }
                                                }
                                            })
                                        ]
                                    )
                                ]);
                            } else {
                                return h('div', [
                                    h(
                                        'Tooltip',
                                        {
                                            props: {
                                                placement: 'top',
                                                content: this.$t('productGroup.addRole')
                                            }
                                        },
                                        [
                                            h('Icon', {
                                                props: {
                                                    size: '20',
                                                    type: 'android-add-circle'
                                                },
                                                style: {
                                                    marginRight: '5px',
                                                    cursor: 'pointer'
                                                },
                                                on: {
                                                    click: () => {
                                                        this.handleAddEmployeeRole(params.row);
                                                    }
                                                }
                                            })
                                        ]
                                    )
                                ]);
                            }
                        }
                    }
                ],
                roleData: []
            };
        },
        methods: {
            search () {
                this.filter.Page = 1;
                this.queryRole();
            },
            queryRole () {
                if (this.isLockQuery) {
                    return
                }
                this.isLockQuery = true
                this.filter.EmployeeID = this.employeeID;
                this.$http
                    .get('/Role/QueryRole', {
                        params: this.filter
                    })
                    .then(response => {
                        this.roleData = response.data.Models;
                        this.totalCount = response.data.TotalCount;
                    }).finally(() => {
                        this.isLockQuery = false
                });
            },
            handlecancel (name) {
                this.filter.Per = 10;
                this.filter.Page = 1;
                this.$emit('configurationRoleClose', false);
            },
            handleAddEmployeeRole (row) {
                this.employee.RoleID = row.ID;
                this.$http
                    .post('/Employee/AddEmployeeRole', this.employee)
                    .then(response => {
                        this.$http
                            .get('/Employee/GetEmployeeWithRoles', {
                                params: { employeeId: this.employeeID }
                            })
                            .then(response => {
                                this.employee = response.data;
                                this.queryRole();
                        });
                });
            },
            handleDeleteEmployeeRoleAndDataAuthority (row) {
                this.$confirm(
                    this.$t('productGroup.deselected'),
                    this.$t('productGroup.removeRoles'),
                    {
                        cancelButtonClass: 'btn-custom-cancel',
                        confirmButtonText: this.$t('system.confirm'),
                        cancelButtonText: this.$t('system.cancel'),
                        type: 'warning'
                    }
                )
                    .then(() => {
                        this.employee.RoleID = row.ID;
                        this.$http
                            .post('/Employee/DeleteEmployeeRole', this.employee)
                            .then(response => {
                                this.$http
                                    .get('/Employee/GetEmployeeWithRoles', {
                                        params: { employeeId: this.employeeID }
                                    })
                                    .then(response => {
                                        this.employee = response.data;
                                        this.queryRole();
                                });
                        });
                    })
                    .catch(() => {});
            },
            // 分页
            changePage (value) {
                this.filter.Page = value;
                this.queryRole();
            },
            // 分页
            changePageSize (value) {
                this.filter.Per = value;
                this.filter.Page = 1;
                this.queryRole();
            },
            handleSubmitCollapse () {
                this.$refs.userDataAuthoritySetting.handleSubmit();
            },
            handlecancelCollapse () {
                this.$refs.userDataAuthoritySetting.handlecancel();
            }
        },
        watch: {
            employeeID (val) {
                this.dataAuthorityEmployeeID = val;
                this.$http
                    .get('/Employee/GetEmployeeWithRoles', {
                        params: { employeeId: val }
                    })
                    .then(response => {
                        this.employee = response.data;
                        this.queryRole();
                });
            },
            isShow (val) {
                if (val === true) {
                    this.queryRole();
                }
            }
        }
    };
</script>
