import http from '../../utils/axios'
const user = {
    state: {
        userlist: [],
        totalcount: 1
    },
    mutations: {
        QueryUsers (state, data) {
            state.userlist = data.Models
            state.totalcount = data.TotalCount
        }
    },
    actions: {
        Login ({ commit }, params) {
            http.post('/User/Login', params).then(response => {
                localStorage.token = response.data.Token
            })
        },
        queryUsers ({ commit }, params) {
            http.get('/Employee/QueryEmployee', { params: params }).then(function (response) {
                commit('QueryUsers', response.data)
            })
        }
    }
}

export default user
