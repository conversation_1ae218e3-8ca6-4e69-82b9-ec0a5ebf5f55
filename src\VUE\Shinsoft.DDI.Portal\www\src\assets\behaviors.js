const serverSrc = {
    // 产品基本信息1
    product: {
        product_AllMaterial: false, // 全部物料
        product_AddProduct: false, // 新增产品
        product_Export: false, // 导出
        product_Edit: false, // 编辑
        product_AddMaterialGroup: false, // 新增分型
        product_MaterialManagerment: false, // 物料管理
        product_ProductAnother: false, // 产品别名
        product_Listing: false, // 上市
        product_Delisting: false, // 退市
        product_Search: false // 查询
    },
    // 产品组管理2
    productlevel: {
        productlevel_BUManagement: false, // BU维护
        productlevel_ProductLineManagement: false, // 产品线维护
        productlevel_AddBrand: false, // 新增品牌
        productlevel_Export: false, // 导出
        productlevel_Search: false, // 查询
        productlevel_Edit: false, // 编辑
        productlevel_Delete: false // 删除
    },
    // 批号导入4
    batchimport: {
        batchimport_Search: false, // 查询
        batchimport_MaterialLog: false, // 物料忽略记录
        batchimport_BatchLog: false, // 批号忽略记录
        batchimport_Import: false, // 导入
        batchimport_Export: false // 导出
    },
    // 批号管理5
    batchlist: {
        batchlist_Search: false, // 查询
        batchlist_Add: false, // 新增
        batchlist_Export: false, // 导出
        batchlist_Edit: false, // 编辑
        batchlist_Delete: false, // 删除
        batchlist_AddBatchAlias: false // 新增批号别名
    },
    // 批号别名6
    batchanother: {
        batchanother_Search: false, // 查询
        batchanother_Add: false, // 新增
        batchanother_Export: false, // 导出
        batchanother_Edit: false, // 编辑
        batchanother_Delete: false // 删除
    },
    // 历史单据查询7
    historyBillQuery: {
        historyBillQuery_Search: false, // 查询
        historyBillQuery_Detail: false, // 查看
        historyBillQuery_ChannelChange_Export: false
    },
    // 流向商业管理8
    distributorList: {
        distributorList_Search: false, // 查询
        distributorList_Upgrade: false, // 升级为流向商业
        distributorList_UpgradeTraceability: false, // 升级为可追溯商业
        distributorList_DowngradeTraceability: false, // 降级为可追溯商业
        distributorList_Downgrade: false, // 取消
        distributorList_Edit: false, // 编辑
        distributorList_ContractManager: false, // 联系人管理
        distributorList_Detail: false, // 查看
        distributorList_Export: false, // 导出
        distributorList_ExportHistoryFlow: false, // 导出历史流向商业名单
        distributorList_Addgrade: false, // 新增流向商业
        distributorList_AddgradeTraceability: false, // 新增可追溯商业
        distributorList_SetBidding: false // 设置投标主体
    },
    // 变更管理(我的申请)9
    applyList: {
        applyList_Search: false, // 查询
        applyList_Edit: false, // 编辑
        applyList_Delete: false, // 删除
        applyList_Approval: false, // 审批
        applyList_Detail: false
    },
    // 变更管理(我的待办)10
    confirmList: {
        confirmList_Search: false, // 查询
        confirmList_Detail: false, // 确认
        confirmList_Revoke: false, // 撤销
        confirmList_Detail_ConfirmAndEdit: false
    },
    // 联系人管理11
    contractorList: {
        contractorList_Search: false, // 查询
        contractorList_Add: false, // 新增
        contractorList_Export: false, // 导出
        contractorList_Edit: false, // 编辑
        contractorList_Delete: false // 删除
    },
    // 渠道管理12
    productChannelList: {
        productChannelList_Search: false, // 查询
        productChannelList_Add: false, // 增加产品渠道
        productChannelList_Edit: false, // 变更产品渠道
        productChannelList_Disabled: false, // 停用产品渠道
        productChannelList_Export: false // 导出全部
    },
    // 收货方管理14
    receiverList: {
        receiverList_Search: false, // 查询
        receiverList_Add: false, // 新增
        receiverList_BatchImport: false, // 批量导出
        receiverList_BatchChange: false, // 批量变更
        receiverList_BatchChangeByBusinessLicenseNo: false, // 批量变更By社会统一信用代码
        receiverList_Edit: false, // 编辑
        receiverList_Stop: false, // 停用启用
        receiverList_Export: false, // 导出
        receiverList_GBCODE: false, // GBCODE
        receiverList_ExportTree: false
    },
    // 收货方别名管理15
    receiverAliasList: {
        receiverAliasList_Search: false, // 查询
        receiverAliasList_Add: false, // 新增
        receiverAliasList_Export: false, // 导出
        receiverAliasList_Edit: false, // 编辑
        receiverAliasList_Request: false // 别名申请
    },

    // 收货方日志16
    receiverLog: {
        receiverLog_Export: false, // 导出
        receiverLog_Search: false // 查询
    },
    // 大区管理17
    areaList: {
        areaList_Search: false, // 查询
        areaList_Add: false, // 新增
        areaList_Export: false, // 导出
        areaList_Edit: false, // 编辑
        areaList_Delete: false // 删除
    },
    // 城市管理18
    cityList: {
        cityList_Search: false, // 查询
        cityList_Add: false, // 新增
        cityList_Export: false, // 导出
        cityList_Edit: false, // 编辑
        cityList_Delete: false // 删除
    },
    // 区县管理19
    countyList: {
        countyList_Search: false, // 查询
        countyList_Add: false, // 新增
        countyList_Export: false, // 导出
        countyList_Edit: false, // 编辑
        countyList_Delete: false // 删除
    },
    // DDI数据导入20
    ddiImport: {
        ddiImport_Upload: false
    },
    // SAP月数据生成22
    monthlySAPData: {
        monthlySAPData_Search: false,
        monthlySAPData_MonthlyConversion: false
    },
    // 上传日志列表23
    manualUploadLog: {
        manualUploadLog_Search: false,
        manualUploadLog_Export: false
    },
    // 自动日志列表24
    autoUploadLog: {
        autoUploadLog_Searc: false,
        autoUploadLog_Export: false
    },
    // 流向商业流向确认25
    distributorSalesFlowConfirm: {
        distributorSalesFlowConfirm_Search: false,
        distributorSalesFlowConfirm_Add: false,
        distributorSalesFlowConfirm_Delete: false,
        distributorSalesFlowConfirm_Confirm: false,
        distributorSalesFlowConfirm_AllConfirm: false,
        distributorSalesFlowConfirm_Release: false,
        distributorSalesFlowConfirm_Frozen: false,
        distributorSalesFlowConfirm_Export: false,
        distributorSalesFlowConfirm_Edit: false
    },
    // 可追溯商业流向确认26
    preparatorySalesowConfirm: {
        preparatorySalesowConfirm_Search: false,
        preparatorySalesowConfirm_Add: false,
        preparatorySalesowConfirm_Delete: false,
        preparatorySalesowConfirm_Confirm: false,
        preparatorySalesowConfirm_AllConfirm: false,
        preparatorySalesowConfirm_Release: false,
        preparatorySalesowConfirm_Export: false,
        preparatorySalesowConfirm_Edit: false
    },
    // 月采购数据27
    monthlyPurchaseQuery: {
        monthlyPurchaseQuery_Search: false,
        monthlyPurchaseQuery_Export: false
    },
    // 异常终端报表
    abnormalTerminalReport: {
        abnormalTerminalReport_Search: false,
        abnormalTerminalReport_Export: false
    },
    // 日采购数据28
    dailyPurchaseQuery: {
        dailyPurchaseQuery_Search: false,
        dailyPurchaseQuery_Export: false
    },
    // 流向商业明细流向查询29
    salesFlowDetailQuery: {
        salesFlowDetailQuery_Search: false,
        salesFlowDetailQuery_Export: false,
        salesFlowDetailQuery_WriteOff: false,
        salesFlowDetailQuery_Edit: false,
        salesFlowDetailQuery_BatchUpdate: false
    },
    distributorSalesFlowSimpleQuery: {
        distributorSalesFlowSimpleQuery_Search: false,
        distributorSalesFlowSimpleQuery_Export: false
    },
    // 可追溯商业流向查询30
    preparatorySalesFlowQuery: {
        preparatorySalesFlowQuery_Search: false,
        preparatorySalesFlowQuery_Export: false
    },
    // 流向商业月提供情况31
    distributorMonthStatusQuery: {
        distributorMonthStatusQuery_Search: false,
        distributorMonthStatusQuery_Export: false
    },
    // 流向商业日提供情况32
    distributorDailyStatusQuery: {
        distributorDailyStatusQuery_Search: false,
        distributorDailyStatusQuery_Export: false
    },
    // 兴奋剂查询34
    analepticQuery: {
        analepticQuery_Search: false,
        analepticQuery_Export: false,
        analepticQuery_Feedback: false,
        analepticQuery_Detail: false
    },
    // 流向差异核查35
    salesflowDeficiencyInspect: {
        salesflowDeficiencyInspect_Search: false,
        salesflowDeficiencyInspect_Export: false
    },
    // 渠道差异核查36
    channelAbnormalInspect: {
        channelAbnormalInspect_Search: false,
        channelAbnormalInspect_Export: false,
        channelAbnormalInspect_FieldHandling: false,
        channelAbnormalInspect_See: false
    },
    // 删除数据提供情况37
    deleteDailyStatus: {
        deleteDailyStatus_Search: false,
        deleteDailyStatus_Export: false,
        deleteDailyStatus_AllDelete: false
    },
    // 删除月采购数据38
    deleteMonthlyPurchase: {
        deleteMonthlyPurchase_Search: false,
        deleteMonthlyPurchase_Export: false,
        deleteMonthlyPurchase_AllDelete: false
    },
    // 删除日采购数据39
    deleteDailyPurchase: {
        deleteDailyPurchase_Search: false,
        deleteDailyPurchase_Export: false,
        deleteDailyPurchase_AllDelete: false
    },
    // 删除日库存40
    deleteDailyStock: {
        deleteDailyStock_Search: false,
        deleteDailyStock_Export: false,
        deleteDailyStock_AllDelete: false
    },
    // 删除月库存
    deleteMonthlyStock: {
        deleteMonthlyStock_Search: false,
        deleteMonthlyStock_Export: false,
        deleteMonthlyStock_AllDelete: false
    },
    // 删除日流向41
    deleteDailySalesFlow: {
        deleteDailySalesFlow_Search: false,
        deleteDailySalesFlow_Export: false,
        deleteDailySalesFlow_AllDelete: false
    },
    // 人员管理42
    userlist: {
        userlist_Search: false, // 查询
        userlist_Add: false, // 新增
        userlist_Edit: false, // 修改
        userlist_ResetPassword: false, // 重置密码
        userlist_EntryDepart: false, // 入离职
        userlist_ToConfigure: false, // 配置角色
        userlist_Detail: false // 查看
    },
    employeeStationHistory: {
        employeeStationHistory_Search: false, // 查询
        employeeStationHistory_Export: true // 导出
    },
    // 默认批号修改43
    batchModification: {
        batchModification_Search: false, // 查询
        batchModification_Edit: false // 修改
    },
    // 产品权限组管理44
    productGroupList: {
        productGroupList_Search: false, // 查询
        productGroupList_Add: false, // 新增
        productGroupList_Edit: false, // 编辑
        productGroupList_Delete: false // 删除
    },
    // 收货方类型45
    receiverTypeList: {
        receiverTypeList_Add: false, // 新增
        receiverTypeList_Export: false, // 导出
        receiverTypeList_Edit: false // 编辑
    },
    // 字典管理46
    dictionaryAdministration: {
        dictionaryAdministration_Search: false,
        dictionaryAdministration_Add: false,
        dictionaryAdministration_Edit: false,
        dictionaryAdministration_Delete: false
    },
    // 货主管理47
    shipperList: {
        shipper_Add: false,
        shipper_Search: false,
        shipper_Stop: false,
        shipper_Edit: false,
        shipper_UploadSeal: false,
        shipper_Authentication: false
    },
    // 进销存管理48
    inventoryList: {
        inventoryList_Search: false,
        inventoryList_Export: false,
        inventoryList_Edit: false
    },
    // 流向商业库存天数49
    distributorInventoryDay: {
        distributorInventoryDay_Search: false,
        distributorInventoryDay_Export: false,
        distributorInventoryDay_Recalculate: false // 重新计算
    },
    // 产品库存天数50
    productInventoryDay: {
        productInventoryDay_Search: false,
        productInventoryDay_Export: false,
        productInventoryDay_Recalculate: false // 重新计算
    },
    // 当月库存预估51
    inventoryEstimateMonth: {
        inventoryEstimateMonth_Search: false,
        inventoryEstimateMonth_Export: false,
        inventoryEstimateMonth_Recalculate: false // 重新计算
    },
    // 月进销货差异核查52
    monthlySalesFlowInspect: {
        monthlySalesFlowInspect_Search: false,
        monthlySalesFlowInspect_Export: false,
        monthlySalesFlowInspect_Recalculate: false // 重新计算
    },
    // 日库存差异核查53
    dailyInventoryInspect: {
        dailyInventoryInspect_Search: false,
        dailyInventoryInspect_Export: false,
        dailyInventoryInspect_FeedBack: false // 反馈
    },
    // 进销存其它项汇总54
    otherItemReport: {
        otherItemReport_Search: false,
        otherItemReport_Export: false
    },
    // 按产品‌经销商统计55
    distributorProductReport: {
        distributorProductReport_Search: false,
        distributorProductReport_Export: false,
        distributorProductReport_Recalculate: false // 重新计算
    },
    // 流向商业库存金额56
    distributorAmountReport: {
        distributorAmountReport_Search: false,
        distributorAmountReport_Export: false,
        distributorAmountReport_Recalculate: false // 重新计算
    },
    // 流向商业负库存57
    flowNegativeStock: {
        flowNegativeStock_Search: false,
        flowNegativeStock_Export: false,
        flowNegativeStock_FeedBack: false
    },
    // 日库存查询58
    dailyInventoryList: {
        dailyInventoryList_Search: false,
        dailyInventoryList_Export: false
    },
    // 安全库存范围设置59
    safetyStockRange: {
        safetyStockRange_Search: false,
        safetyStockRange_Add: false,
        safetyStockRange_BatchImport: false,
        safetyStockRange_BatchDelete: false,
        safetyStockRange_Edit: false,
        safetyStockRange_Delete: false
    },
    // 库存报表设置60
    inventoryReportSetup: {
        inventoryReportSetup_SaveInventoryYearMonth: false,
        inventoryReportSetup_SaveInventoryMonthXSetUp: false
    },
    // 日销售流向查询61
    dailySalesFlowQuery: {
        dailySalesFlowQuery_Search: false,
        dailySalesFlowQuery_Export: false
    },
    // 销售日流向查询
    salesDailySalesFlowQuery: {
        salesDailySalesFlowQuery_Search: false,
        salesDailySalesFlowQuery_Export: false
    },
    // 月销售流向查询
    monthlySalesFlowQuery: {
        monthlySalesFlowQuery_Search: false,
        monthlySalesFlowQuery_Export: false
    },
    // 日流向 By Terminal 62
    diurnalFlowByTerminal: {
        diurnalFlowByTerminal_Search: false,
        diurnalFlowByTerminal_Export: false
    },
    // 月流向 By Terminal 63
    monthlyFlowByTerminal: {
        monthlyFlowByTerminal_Search: false,
        monthlyFlowByTerminal_Export: false
    },
    // 周期管理 64
    cycleManagement: {
        cycleManagement_Add: false,
        cycleManagement_Search: false,
        cycleManagement_Edit: false
    },
    // 异常日志管理 65
    exceptionLogQuery: {
        exceptionLogQuery_Search: false,
        exceptionLogQuery_Export: false
    },
    // 事件日志管理 66
    eventLogQuery: {
        eventLogQuery_Search: false,
        eventLogQuery_Export: false
    },
    //   经销商产品信息 67
    distributorProductQuery: {
        distributorProductQuery_Search: false,
        distributorProductQuery_Export: false
    },
    // 打单商业查询
    distributorProductForBO: {
        distributorProductForBO_Search: false,
        distributorProductForBO_Export: false
    },
    // 计算报告 68
    inventoryGenerate: {
        inventoryGenerate_Calculate: false
    },
    // 按收货方汇总查询69
    analysisByReceiver: {
        analysisByReceiver_Search: false,
        analysisByReceiver_Export: false
    },
    // 按年度分型汇总70
    analysisInMarket: {
        analysisInMarket_Search: false,
        analysisInMarket_Export: false
    },
    salesReturnOwner: {
        salesReturnOwner_Search: false,
        salesReturnOwner_Add: false,
        salesReturnOwner_Edit: false,
        salesReturnOwner_Delete: false
    },
    salesReturnReason: {
        salesReturnReason_Search: false,
        salesReturnReason_Add: false,
        salesReturnReason_Edit: false,
        salesReturnReason_Delete: false
    },
    salesReturnReply: {
        salesReturnReply_Search: false,
        salesReturnReply_Export: false,
        salesReturnReply_Save: false
    },
    salesReturnReport: {
        salesReturnReport_Search: false
    },
    // 按收货方省份汇总
    analysisByReceiverProvince: {
        analysisByReceiverProvince_Search: false,
        analysisByReceiverProvince_Export: false
    },
    // 按发货方汇总
    analysisInByDistributor: {
        analysisInByDistributor_Search: false,
        analysisInByDistributor_Export: false
    },
    replyRateLineSetting: {
        replyRateLineSetting_Save: false
    },
    replyRadixSetting: {
        replyRadixSetting_Search: false,
        replyRadixSetting_Export: false,
        replyRadixSetting_Edit: false,
        replyRadixSetting_BatchEdit: false
    },
    // GXP信息导入
    gXPBusinessInformationImport: {
        gXPBusinessInformationImport_Upload: false,
        gXPBusinessInformationImport_Search: false
    },
    warehouse: {
        warehouse_Search: false,
        warehouse_Add: false,
        warehouse_Edit: false,
        warehouse_EnableDisable: false
    },
    shipperWarehouseProductRelation: {
        shipperWarehouseProductRelation_Search: false,
        shipperWarehouseProductRelation_Add: false,
        shipperWarehouseProductRelation_Edit: false,
        shipperWarehouseProductRelation_EnableDisable: false,
        shipperWarehouseProductRelation_Transfer: false,
        shipperWarehouseProductRelation_BatchUpload: false,
        shipperWarehouseProductRelation_Export: false
    },
    customerAgreement: {
        customerAgreement_Search: false,
        customerAgreement_Add: false,
        customerAgreement_BatchUpload: false,
        customerAgreement_Export: false,
        customerAgreement_Edit: false,
        customerAgreement_Delete: false
    },
    customerCredit: {
        customerCredit_Search: false,
        customerCredit_Export: false
    },
    orderProcessing: {
        orderProcessing_Search: false,
        orderProcessing_Processing: false,
        orderProcessing_Cancel: false,
        orderProcessing_NewOrder: false
    },
    orderProcess: {
        orderProcess_Search: false,
        orderProcess_BatchContractStamped: false,
        orderProcess_BatchContractReturn: false,
        orderProcess_Export: false,
        orderProcess_ContractStamped: false,
        orderProcess_EditSO: false,
        orderProcess_EditDN: false,
        orderProcess_ContractReturn: false,
        orderProcess_ContractDetail: false,
        orderProcess_InvoiceDetail: false,
        orderProcess_EditOrder: false,
        orderProcess_SendMailAgain: false,
        orderProcess_Remark: false,
        orderProcess_Cancel: false,
        orderProcess_ProcessingLog: false,
        orderProcess_OrderDetail: false,
        orderProcess_ChangContractHasBeenSent: false,
        orderProcess_UploadContractAndSign: false,
        orderProcess_ApprovalContract: false
    },
    contractScanConfirmation: {
        contractScanConfirmation_Confirm: false
    },
    orderDetailEnquiry: {
        orderDetailEnquiry_Search: false,
        orderDetailEnquiry_Export: false
    },
    informationMaintenance: {
        informationMaintenance_Search: false,
        informationMaintenance_BatchUpload: false,
        informationMaintenance_ExpressBatchUpload: false,
        informationMaintenance_Export: false,
        informationMaintenance_Delete: false
    },
    importSalesPlan: {
        importSalesPlan_Search: false,
        importSalesPlan_BatchDelete: false,
        importSalesPlan_BatchConfirm: false,
        importSalesPlan_BatchRelease: false,
        importSalesPlan_AllNoticeConfirm: false,
        importSalesPlan_BatchCompulsoryConfirm: false,
        importSalesPlan_BatchUpload: false,
        importSalesPlan_Export: false,
        importSalesPlan_Edit: false,
        importSalesPlan_Delete: false,
        importSalesPlan_Confirm: false,
        importSalesPlan_CompulsoryConfirm: false,
        importSalesPlan_Adjustment: false,
        importSalesPlan_Add: false
    },
    salesPlanKPI: {
        salesPlanKPI_Setting: false,
        salesPlanKPI_Search: false,
        salesPlanKPI_Export: false,
        salesPlanKPI_Update: false
    },
    achievingRateReport: {
        achievingRateReport_Search: false,
        achievingRateReport_Export: false
    },
    contractSetting: {
        contractSetting_Save: false
    },
    tenderPriceQuery: {
        tenderPriceQuery_Search: false,
        tenderPriceQuery_Export: false
    },
    biddingDetailQuery: {
        biddingDetailQuery_Search: false,
        biddingDetailQuery_Export: false
    },
    biddingProject: {
        biddingProject_Search: false,
        biddingProject_Add: false,
        biddingProject_Export: false,
        biddingProject_Edit: false,
        biddingProject_Delete: false
    },
    suspectAllocation: {
        suspectAllocation_Search: false,
        suspectAllocation_Export: false,
        suspectAllocation_FeedBack: false
    },
    baselineOfInventoryCheckDate: {
        baselineOfInventoryCheckDate_Search: false,
        baselineOfInventoryCheckDate_Edit: false,
        baselineOfInventoryCheckDate_Export: false,
        baselineOfInventoryCheckDate_BatchEdit: false
    },
    baselineSettingofSalesQuantity: {
        baselineSettingofSalesQuantity_Search: false,
        baselineSettingofSalesQuantity_Edit: false,
        baselineSettingofSalesQuantity_Export: false,
        baselineSettingofSalesQuantity_BatchEdit: false
    },
    baselineSettingOfTargetHospital: {
        baselineSettingOfTargetHospital_Search: false,
        baselineSettingOfTargetHospital_Edit: false,
        baselineSettingOfTargetHospital_Export: false,
        baselineSettingOfTargetHospital_BatchEdit: false
    },
    discrepancyRateOfInventoryDaily: {
        discrepancyRateOfInventoryDaily_Save: false
    },
    targetHospitalSales: {
        targetHospitalSales_Search: false,
        targetHospitalSales_Export: false
    },
    noticeDefaultNumberSetting: {
        noticeDefaultNumberSetting_Save: false
    },
    ddiContributionRateSetting: {
        ddiContributionRateSetting_Save: false
    },
    otherShipperSettings: {
        otherShipperSettings_Search: false,
        otherShipperSettings_Add: false,
        otherShipperSettings_Delete: false
    },
    queryVolumeOfTargetHospital: {
        queryVolumeOfTargetHospital_Search: false,
        queryVolumeOfTargetHospital_Export: false,
        queryVolumeOfTargetHospital_Upload: false,
        queryVolumeOfTargetHospital_UploadLog: false
    },
    batchMonthlyInventory: {
        batchMonthlyInventory_Search: false,
        batchMonthlyInventory_Export: false
    },
    notificationSetting: {
        notificationSetting_Save: false
    },
    inventoryEstimateSetting: {
        inventoryEstimateSetting_Search: false,
        inventoryEstimateSetting_Add: false,
        inventoryEstimateSetting_Delete: false
    },
    documentList: {
        documentList_Search: false,
        documentList_Add: false,
        documentList_Delete: false
    },
    biddingPriceConflict: {
        biddingPriceConflict_Search: false,
        biddingPriceConflict_Export: false,
        biddingPriceConflict_GenerateReport: false,
        biddingPriceConflict_Edit: false
    },
    theLowestBidPrice: {
        theLowestBidPrice_Search: false,
        theLowestBidPrice_Export: false
    },
    biddingOfDelistingProducts: {
        biddingOfDelistingProducts_Search: false,
        biddingOfDelistingProducts_Export: false
    },
    compareByDistributor: {
        compareByDistributor_Search: false,
        compareByDistributor_Export: false
    },
    compareByReceiver: {
        compareByReceiver_Search: false,
        compareByReceiver_Export: false
    },
    splitDetailQuery: {
        splitDetailQuery_Search: false,
        splitDetailQuery_Export: false
    },
    analysisByDistributorAndReceiver: {
        analysisByDistributorAndReceiver_Search: false,
        analysisByDistributorAndReceiver_Export: false
    },
    defaultBidder: {
        defaultBidder_Search: false,
        defaultBidder_Add: false,
        defaultBidder_Edit: false,
        defaultBidder_EnableDisable: false,
        defaultBidder_Delete: false,
        defaultBidder_Export: false
    },
    biddingType: {
        biddingType_Search: false,
        biddingType_Add: false,
        biddingType_Edit: false,
        biddingType_EnableDisable: false,
        biddingType_Delete: false,
        biddingType_Export: false
    },
    biddingStep: {
        biddingStep_Search: false,
        biddingStep_Add: false,
        biddingStep_Edit: false,
        biddingStep_EnableDisable: false,
        biddingStep_Delete: false,
        biddingStep_Export: false
    },
    myApproval: {
        myApproval_Search: false,
        myApproval_Export: false
    },
    drugStoreCompensationFile: {
        drugStoreCompensationFile_Search: false,
        drugStoreCompensationFile_Upload: false,
        drugStoreCompensationFile_Delete: false
    },
    compensationSummaryList: {
        compensationSummaryList_Recalculate: false,
        compensationSummaryList_Detail: false,
        compensationSummaryList_End: false,
        compensationSummaryList_Edit: false,
        compensationSummaryList_NewUnitPrice: false,
        compensationSummaryList_Search: false,
        compensationSummaryList_DifferentReport: false,
        compensationSummaryList_ExportAll: false
    },
    drugStoreSetting: {
        drugStoreSetting_Search: false,
        drugStoreSetting_Add: false,
        drugStoreSetting_Edit: false,
        drugStoreSetting_Delete: false,
        drugStoreSetting_Export: false
    },
    confirmUnitPriceDetail: {
        confirmUnitPriceDetail_Export: false,
        confirmUnitPriceDetail_ConfirmAll: false,
        confirmUnitPriceDetail_Confirm: false,
        confirmUnitPriceDetail_Search: false
    },
    militaryRegionManagement: {
        militaryRegionManagement_Search: false,
        militaryRegionManagement_Add: false,
        militaryRegionManagement_Export: false,
        militaryRegionManagement_Edit: false,
        militaryRegionManagement_Delete: false
    },
    returnCertificateRequirement: {
        returnCertificateRequirement_Add: false,
        returnCertificateRequirement_Export: false,
        returnCertificateRequirement_Edit: false,
        returnCertificateRequirement_Delete: false
    },
    certificateManagement: {
        certificateManagement_Search: false,
        certificateManagement_Add: false,
        certificateManagement_Edit: false,
        certificateManagement_Delete: false,
        certificateManagement_Upload: false,
        certificateManagement_CreatePaymentPlan: false,
        certificateManagement_PaymentPlanDetail: false,
        certificateManagement_CreateResult: false,
        certificateManagement_Return: false,
        certificateManagement_CollectPaymentCertificate: false,
        certificateManagement_CollectPaymentDetail: false,
        certificateManagement_PDFZipDownload: false,

        returnCertificateRequirement_Search: false,
        certificateManagement_Correct: false,
        certificateManagement_Stop: false,

        certificateManagement_SealResult: false,
        certificateManagement_BatchSeal: false,
        certificateManagement_BatchRelease: false,
        certificateManagement_Release: false,
        certificateManagement_ReSign: false,
        certificateManagement_Cancel: false,
        certificateManagement_ReplaceNewFile: false,
        certificateManagement_ApprovelNotice: false,
        certificateManagement_DownloadNotice: false,
        certificateManagement_EditNotice: false,
        certificateManagement_ResendEmail: false,
        certificateManagement_DownloadFile: false,
        certificateManagement_GenerationFile: false,
        certificateManagement_Export: false
    },
    certificateQuery: {
        certificateQuery_Search: false,
        certificateQuery_Export: false,
        certificateQuery_ReturnCertificateDownLoad: false,
        certificateQuery_CollectPaymentCertificateDownLoad: false,
        certificateQuery_CollectPaymentCertificateDetailDownLoad: false
    },
    kpiSummary: {
        kpiSummary_Search: false,
        kpiSummary_Add_Switch: false
    },
    dailySummaryReport: {
        dailySummaryReport_Search: false,
        dailySummaryReport_Export: false
    },
    receiverAliasChangeRequest: {
        receiverAliasChangeRequest_Search: false,
        receiverAliasChangeRequest_Export: false,
        receiverAliasChangeRequest_Confirm: false,
        receiverAliasChangeRequest_Reject: false
    },
    biddingQueryForRepresentative: {
        biddingQueryForRepresentative_Search: false,
        biddingQueryForRepresentative_AddSecondBargaining: false,
        biddingQueryForRepresentative_Edit: false,
        biddingQueryForRepresentative_Export: false,
        biddingQueryForRepresentative_Detail: false
    },
    customerManagement: {
        customerManagement_Export: false,
        customerManagement_ApprovalSeal: false,
        customerManagement_UploadSeal: false,
        customerManagement_UnbindWeChat: false,
        customerManagement_CancelRegister: false,
        customerManagement_CreateInvitationCode: false,
        customerManagement_Detail: false,
        customerManagement_Search: false
    },
    customerNoticeManagement: {
        customerNoticeManagement_Edit: false,
        customerNoticeManagement_Cancel: false,
        customerNoticeManagement_Release: false,
        customerNoticeManagement_Delete: false,
        customerNoticeManagement_Search: false,
        customerNoticeManagement_Add: false
    },
    materialGroup: {
        materialGroup_Search: false,
        materialGroup_Export: false,
        materialGroup_IsCompensation: false
    },
    certificateDetailRepresentative: {
        certificateDetailRepresentative_Search: false,
        certificateDetailRepresentative_Export: false
    },
    quarterCompensation: {
        quarterCompensation_Search: false,
        quarterCompensation_Add: false,
        quarterCompensation_Edit: false,
        quarterCompensation_ReCalculate: false,
        quarterCompensation_Result: false,
        quarterCompensation_ResultExport: false,
        quarterCompensation_Delete: false,
        quarterCompensation_Error: false
    },
    accruedExpense: {
        accruedExpense_Search: false,
        accruedExpense_Add: false,
        accruedExpense_Edit: false,
        accruedExpense_PriceDetail: false,
        accruedExpense_ReCalculateAll: false,
        accruedExpense_ExportAll: false,
        accruedExpense_ReCalculateMany: false,
        accruedExpense_EditPrice: false,
        accruedExpense_ReCalculateOne: false,
        accruedExpense_ShowPrice: false,
        accruedExpense_DeletePrice: false,
        accruedExpense_Result: false,
        accruedExpense_ReCalculate: false,
        accruedExpense_Export: false,
        accruedExpense_Report: false,
        accruedExpense_Delete: false,
        accruedExpense_BatchDeletePrice: false,
        accruedExpense_Error: false,
        accruedExpense_CalculatePrice: false,
        accruedExpense_Calculate: false,
        accruedExpense_ExportReport: false
    },
    inventoryModifyReport: {
        inventoryModifyReport_Search: false,
        inventoryModifyReport_Export: false
    },
    visitPlan: {
        visitPlan_Search: false,
        visitPlan_Edit: false,
        visitPlan_EnableDisable: false,
        visitPlan_GeneratePlan: false,
        visitPlan_PublishPlan: false,
        visitPlan_BatchPublish: false
    },
    drugstoreManagement: {
        drugstoreManagement_Search: false,
        drugstoreManagement_Add: false,
        drugstoreManagement_Import: false,
        drugstoreManagement_Export: false,
        drugstoreManagement_Delete: false,
        drugstoreManagement_BatchDelete: false,
        drugstoreBUManagement_BatchDelete: false
    },
    drugstoreBUManagement: {
        drugstoreBUManagement_Search: false,
        drugstoreBUManagement_Add: false,
        drugstoreBUManagement_Edit: false,
        drugstoreBUManagement_Delete: false,
        drugstoreBUManagement_Detail: false,
        drugstoreBUManagement_Import: false,
        drugstoreBUManagement_Export: false
    },
    visitResult: {
        visitResult_Search: false,
        visitResult_Export: false,
        visitResult_Detail: false
    },
    // 批号进销存报告
    inventoryByBatchNumberReport: {
        inventoryByBatchNumberList_Search: false,
        inventoryByBatchNumberList_Export: false
    },
    // SFE日志管理
    sfeLogManagement: {
        SFELogQuery_Search: false,
        SFELogQuery_Export: false,
        SFELogQuery_ReExecute: false
    },
    // 目标客户
    targetReceiver: {
        targetReceiver_Search: false,
        targetReceiver_Add: false,
        targetReceiver_Edit: false,
        targetReceiver_Delete: false,
        targetReceiver_BatchImport: false,
        targetReceiver_BatchDisable: false,
        targetReceiver_Export: false
    },
    // 考核价
    accountingPrice: {
        accountingPrice_Search: false,
        accountingPrice_Add: false,
        accountingPrice_Edit: false,
        accountingPrice_Delete: false,
        accountingPrice_Import: false,
        accountingPrice_Export: false
    },
    // 医生
    doctor: {
        doctor_Search: false,
        doctor_Export: false,
        doctor_AddSpeaker: false,
        doctor_StopDoctor: false,
        doctor_UpgradeSpeaker: false,
        doctor_AddSpeakerOfDataManager: false,
        doctor_AddDoctorOfDataManager: false,
        doctor_EditDoctorAndSpeaker: false,
        doctor_Import: false,
        doctor_ChangeImport: false,
        doctor_UpgradeDoctorOfDataManager: false,
        doctor_ChangeDoctorAndSpeaker: false
    },
    // CRM数据审核
    crmDataApproval: {
        crmDataApproval_Search: false,
        crmDataApproval_Export: false,
        crmDataApproval_Approval: false
    },
    // 客户项目管理
    departmentProject: {
        departmentProject_Search: false,
        departmentProject_Edit: false
    },
    // 客户分级
    receiverLevel: {
        receiverLevel_Search: false,
        receiverLevel_Add: false,
        receiverLevel_Edit: false,
        receiverLevel_Delete: false,
        receiverLevel_Import: false,
        receiverLevel_Export: false
    },
    quota: {
        quota_Search: false,
        quota_Add: false,
        quota_Edit: false,
        quota_Delete: false,
        quota_Export: false,
        quota_Split: false,
        quota_RegionQuota: false,
        quota_RegionQuotaExport: false
    },
    salesFlowComplaints: {
        salesFlowComplaints_Search: false,
        salesFlowComplaints_Complete: false,
        salesFlowComplaints_DownloadZip: false,
        salesFlowComplaints_Approval: false
    },
    receiverInStationHistory: {
        receiverInStationHistory_Search: false,
        receiverInStationHistory_Export: false
    },
    provinceInStationHistory: {
        provinceInStationHistory_Search: false,
        provinceInStationHistory_Export: false,
        provinceInStationHistory_Edit: false
    },
    receiverQuota: {
        receiverQuota_Search: false,
        receiverQuota_Add: false,
        receiverQuota_Edit: false,
        receiverQuota_Delete: false,
        receiverQuota_Export: false,
        receiverQuota_Import: false
    },
    hospitalProcurement: {
        hospitalProcurement_Search: false,
        hospitalProcurement_Add: false,
        hospitalProcurement_Edit: false,
        hospitalProcurement_Delete: false,
        hospitalProcurement_Export: false,
        hospitalProcurement_Import: false
    },
    areaQuota: {
        areaQuota_Search: false,
        areaQuota_Export: false
    },
    // 院外药店
    drugstoreOfHospital: {
        drugstoreOfHospital_Search: false,
        drugstoreOfHospital_Add: false,
        drugstoreOfHospital_Edit: false,
        drugstoreOfHospital_Delete: false,
        drugstoreOfHospital_BatchImport: false,
        drugstoreOfHospital_Export: false
    },
    salesAchievementReport: {
        salesAchievementReport_Search: false,
        salesAchievementReport_Export: false
    },
    targetHospitalGrowthRateReport: {
        targetHospitalGrowthRateReport_Search: false,
        targetHospitalGrowthRateReport_Export: false
    },
    receiverSalesWarning: {
        receiverSalesWarning_Search: false,
        receiverSalesWarning_Export: false
    },
    employeeQuota: {
        employeeQuota_Search: false,
        employeeQuota_Add: false,
        employeeQuota_Edit: false,
        employeeQuota_Delete: false,
        employeeQuota_Export: false,
        employeeQuota_Import: false
    },
    cPASaleData: {
        cPASaleData_Search: false,
        cPASaleData_Edit: false,
        cPASaleData_Delete: false,
        cPASaleData_BatchImport: false,
        cPASaleData_Export: false
    },
    receiverSalesAchievementReport: {
        receiverSalesAchievementReport_Search: false,
        receiverSalesAchievementReport_Export: false
    },
    hospitalDevelopStatistics: {
        hospitalDevelopStatistics_Search: false,
        hospitalDevelopStatistics_Export: false
    },
    salesAmountRankReport: {
        salesAmountRank_Search: false,
        salesAmountRank_Export: false
    },
    salesAmountABSRankReport: {
        salesAmountABSRank_Search: false,
        salesAmountABSRank_Export: false
    },
    hospitalProcurementWarning: {
        hospitalProcurementWarning_Search: false,
        hospitalProcurementWarning_Export: false
    },
    department: {
        department_Search: false,
        department_Add: false,
        department_Edit: false,
        department_Delete: false
    }
}

function hasPermission (allBehaviors, permissionCode) {
    let behaviors = allBehaviors.split(',')
    return behaviors.findIndex(fitem => fitem === permissionCode) !== -1
}

export function behaviorsSession (allBehaviors, permissionCode) {
    switch (permissionCode) {
    // 产品基本信息1
    case 'product':
        serverSrc.product.product_AllMaterial = hasPermission(allBehaviors, 'product_AllMaterial'); // 全部物料
        serverSrc.product.product_AddProduct = hasPermission(allBehaviors, 'product_AddProduct'); // 新增产品
        serverSrc.product.product_Export = hasPermission(allBehaviors, 'product_Export'); // 导出
        serverSrc.product.product_Edit = hasPermission(allBehaviors, 'product_Edit'); // 编辑
        serverSrc.product.product_AddMaterialGroup = hasPermission(allBehaviors, 'product_AddMaterialGroup'); // 新增分型
        serverSrc.product.product_MaterialManagerment = hasPermission(allBehaviors, 'product_MaterialManagerment'); // 物料管理
        serverSrc.product.product_ProductAnother = hasPermission(allBehaviors, 'product_ProductAnother'); // 产品别名
        serverSrc.product.product_Listing = hasPermission(allBehaviors, 'product_Listing'); // 上市
        serverSrc.product.product_Delisting = hasPermission(allBehaviors, 'product_Delisting'); // 退市
        serverSrc.product.product_Search = hasPermission(allBehaviors, 'product_Search'); // 查询
        break;
    // 产品组管理2
    case 'productlevel':
        serverSrc.productlevel.productlevel_BUManagement = hasPermission(allBehaviors, 'productlevel_BUManagement'); // BU维护
        serverSrc.productlevel.productlevel_ProductLineManagement = hasPermission(allBehaviors, 'productlevel_ProductLineManagement'); // 产品线维护
        serverSrc.productlevel.productlevel_AddBrand = hasPermission(allBehaviors, 'productlevel_AddBrand'); // 新增品牌
        serverSrc.productlevel.productlevel_Export = hasPermission(allBehaviors, 'productlevel_Export'); // 导出
        serverSrc.productlevel.productlevel_Search = hasPermission(allBehaviors, 'productlevel_Search'); // 查询
        serverSrc.productlevel.productlevel_Edit = hasPermission(allBehaviors, 'productlevel_Edit'); // 编辑
        serverSrc.productlevel.productlevel_Delete = hasPermission(allBehaviors, 'productlevel_Delete'); // 删除
        break;
    // 产品别名3
    case 'productanother':
        serverSrc.productAnother.productanother_Search = hasPermission(allBehaviors, 'productanother_Search'); // 查询
        serverSrc.productAnother.productanother_Add = hasPermission(allBehaviors, 'productanother_Add'); // 新增
        serverSrc.productAnother.productanother_Export = hasPermission(allBehaviors, 'productanother_Export'); // 导出
        serverSrc.productAnother.productanother_Edit = hasPermission(allBehaviors, 'productanother_Edit'); // 编辑
        serverSrc.productAnother.productanother_Delete = hasPermission(allBehaviors, 'productanother_Delete'); // 删除
        break;
    // 批号导入4
    case 'batchimport':
        serverSrc.batchimport.batchimport_Search = hasPermission(allBehaviors, 'batchimport_Search'); // 查询
        serverSrc.batchimport.batchimport_MaterialLog = hasPermission(allBehaviors, 'batchimport_MaterialLog'); // 物料忽略记录
        serverSrc.batchimport.batchimport_BatchLog = hasPermission(allBehaviors, 'batchimport_BatchLog'); // 批号忽略记录
        serverSrc.batchimport.batchimport_Import = hasPermission(allBehaviors, 'batchimport_Import'); // 导入
        serverSrc.batchimport.batchimport_Export = hasPermission(allBehaviors, 'batchimport_Export'); // 导出
        break;
    // 批号管理5
    case 'batchlist':
        serverSrc.batchlist.batchlist_Search = hasPermission(allBehaviors, 'batchlist_Search'); // 查询
        serverSrc.batchlist.batchlist_Add = hasPermission(allBehaviors, 'batchlist_Add'); // 新增
        serverSrc.batchlist.batchlist_Export = hasPermission(allBehaviors, 'batchlist_Export'); // 导出
        serverSrc.batchlist.batchlist_Edit = hasPermission(allBehaviors, 'batchlist_Edit'); // 编辑
        serverSrc.batchlist.batchlist_Delete = hasPermission(allBehaviors, 'batchlist_Delete'); // 删除
        serverSrc.batchlist.batchlist_AddBatchAlias = hasPermission(allBehaviors, 'batchlist_AddBatchAlias'); // 新增批号别名
        break;
    // 批号别名6
    case 'batchanother':
        serverSrc.batchanother.batchanother_Search = hasPermission(allBehaviors, 'batchanother_Search'); // 查询
        serverSrc.batchanother.batchanother_Add = hasPermission(allBehaviors, 'batchanother_Add'); // 新增
        serverSrc.batchanother.batchanother_Export = hasPermission(allBehaviors, 'batchanother_Export'); // 导出
        serverSrc.batchanother.batchanother_Edit = hasPermission(allBehaviors, 'batchanother_Edit'); // 编辑
        serverSrc.batchanother.batchanother_Delete = hasPermission(allBehaviors, 'batchanother_Delete'); // 删除
        break;
    // 审批历史7
    case 'historyBillQuery':
        serverSrc.historyBillQuery.historyBillQuery_Search = hasPermission(allBehaviors, 'historyBillQuery_Search'); // 查询
        serverSrc.historyBillQuery.historyBillQuery_Detail = hasPermission(allBehaviors, 'historyBillQuery_Detail'); // 查看
        serverSrc.historyBillQuery.historyBillQuery_ChannelChange_Export = hasPermission(allBehaviors, 'historyBillQuery_ChannelChange_Export'); // 导出渠道变更记录
        break;
    // 流向商业管理8
    case 'distributorList':
        serverSrc.distributorList.distributorList_Search = hasPermission(allBehaviors, 'distributorList_Search'); // 查询
        serverSrc.distributorList.distributorList_Upgrade = hasPermission(allBehaviors, 'distributorList_Upgrade'); // 升级为流向商业
        serverSrc.distributorList.distributorList_UpgradeTraceability = hasPermission(allBehaviors, 'distributorList_UpgradeTraceability'); // 升级为可追溯商业
        serverSrc.distributorList.distributorList_DowngradeTraceability = hasPermission(allBehaviors, 'distributorList_DowngradeTraceability'); // 降级为可追溯商业
        serverSrc.distributorList.distributorList_Downgrade = hasPermission(allBehaviors, 'distributorList_Downgrade'); // 取消
        serverSrc.distributorList.distributorList_Edit = hasPermission(allBehaviors, 'distributorList_Edit'); // 编辑
        serverSrc.distributorList.distributorList_ContractManager = hasPermission(allBehaviors, 'distributorList_ContractManager'); // 联系人管理
        serverSrc.distributorList.distributorList_Detail = hasPermission(allBehaviors, 'distributorList_Detail'); // 查看
        serverSrc.distributorList.distributorList_Export = hasPermission(allBehaviors, 'distributorList_Export'); // 导出
        serverSrc.distributorList.distributorList_ExportHistoryFlow = hasPermission(allBehaviors, 'distributorList_ExportHistoryFlow'); // 导出历史流向商业名单
        serverSrc.distributorList.distributorList_Addgrade = hasPermission(allBehaviors, 'distributorList_Addgrade'); // 新增流向商业
        serverSrc.distributorList.distributorList_AddgradeTraceability = hasPermission(allBehaviors, 'distributorList_AddgradeTraceability'); // 新增可追溯商业
        serverSrc.distributorList.distributorList_SetBidding = hasPermission(allBehaviors, 'distributorList_SetBidding'); // 设置投标主体
        break;
    // 变更管理(我的申请)9
    case 'applyList':
        serverSrc.applyList.applyList_Search = hasPermission(allBehaviors, 'applyList_Search'); // 查询
        serverSrc.applyList.applyList_Edit = hasPermission(allBehaviors, 'applyList_Edit'); // 编辑
        serverSrc.applyList.applyList_Delete = hasPermission(allBehaviors, 'applyList_Delete'); // 删除
        serverSrc.applyList.applyList_Approval = hasPermission(allBehaviors, 'applyList_Approval'); // 审批
        serverSrc.applyList.applyList_Detail = hasPermission(allBehaviors, 'applyList_Detail'); // 查看
        serverSrc.applyList.applyList_Withdraw = hasPermission(allBehaviors, 'applyList_Withdraw'); // 撤回
        break;
    // 变更管理(我的待办)10
    case 'confirmList':
        serverSrc.confirmList.confirmList_Search = hasPermission(allBehaviors, 'confirmList_Search'); // 查询
        serverSrc.confirmList.confirmList_Detail = hasPermission(allBehaviors, 'confirmList_Detail'); // 确认
        serverSrc.confirmList.confirmList_Revoke = hasPermission(allBehaviors, 'confirmList_Revoke'); // 确认
        serverSrc.confirmList.confirmList_Detail_ConfirmAndEdit = hasPermission(allBehaviors, 'confirmList_Detail_ConfirmAndEdit'); // 确认并编辑
        break;
    // 联系人管理11
    case 'contractorList':
        serverSrc.contractorList.contractorList_Search = hasPermission(allBehaviors, 'contractorList_Search'); // 查询
        serverSrc.contractorList.contractorList_Add = hasPermission(allBehaviors, 'contractorList_Add'); // 新增
        serverSrc.contractorList.contractorList_Export = hasPermission(allBehaviors, 'contractorList_Export'); // 导出
        serverSrc.contractorList.contractorList_Edit = hasPermission(allBehaviors, 'contractorList_Edit'); // 编辑
        serverSrc.contractorList.contractorList_Delete = hasPermission(allBehaviors, 'contractorList_Delete'); // 删除
        break;
    // 渠道管理12
    case 'productChannelList':
        serverSrc.productChannelList.productChannelList_Search = hasPermission(allBehaviors, 'productChannelList_Search'); // 查询
        serverSrc.productChannelList.productChannelList_Add = hasPermission(allBehaviors, 'productChannelList_Add'); // 增加产品渠道
        serverSrc.productChannelList.productChannelList_Edit = hasPermission(allBehaviors, 'productChannelList_Edit'); // 变更产品渠道
        serverSrc.productChannelList.productChannelList_Disabled = hasPermission(allBehaviors, 'productChannelList_Disabled'); // 停用产品渠道
        serverSrc.productChannelList.productChannelList_Export = hasPermission(allBehaviors, 'productChannelList_Export'); // 导出全部
        break;
    // 收货方管理14
    case 'receiverList':
        serverSrc.receiverList.receiverList_Search = hasPermission(allBehaviors, 'receiverList_Search'); // 查询
        serverSrc.receiverList.receiverList_Add = hasPermission(allBehaviors, 'receiverList_Add'); // 新增
        serverSrc.receiverList.receiverList_BatchImport = hasPermission(allBehaviors, 'receiverList_BatchImport'); // 批量导出
        serverSrc.receiverList.receiverList_BatchChange = hasPermission(allBehaviors, 'receiverList_BatchChange'); // 批量变更
        serverSrc.receiverList.receiverList_BatchChangeByBusinessLicenseNo = hasPermission(allBehaviors, 'receiverList_BatchChangeByBusinessLicenseNo'); // 批量变更By社会统一信用代码
        serverSrc.receiverList.receiverList_Edit = hasPermission(allBehaviors, 'receiverList_Edit'); // 编辑
        serverSrc.receiverList.receiverList_Stop = hasPermission(allBehaviors, 'receiverList_Stop'); // 停用启用
        serverSrc.receiverList.receiverList_Export = hasPermission(allBehaviors, 'receiverList_Export'); // 导出
        serverSrc.receiverList.receiverList_GBCODE = hasPermission(allBehaviors, 'receiverList_GBCODE'); // GBCODE
        serverSrc.receiverList.receiverList_ExportTree = hasPermission(allBehaviors, 'receiverList_ExportTree');
        break;
    // 收货方别名管理15
    case 'receiverAliasList':
        serverSrc.receiverAliasList.receiverAliasList_Search = hasPermission(allBehaviors, 'receiverAliasList_Search'); // 查询
        serverSrc.receiverAliasList.receiverAliasList_Add = hasPermission(allBehaviors, 'receiverAliasList_Add'); // 新增
        serverSrc.receiverAliasList.receiverAliasList_Export = hasPermission(allBehaviors, 'receiverAliasList_Export'); // 导出
        serverSrc.receiverAliasList.receiverAliasList_Edit = hasPermission(allBehaviors, 'receiverAliasList_Edit'); // 编辑
        serverSrc.receiverAliasList.receiverAliasList_Request = hasPermission(allBehaviors, 'receiverAliasList_Request'); // 别名申请
        break;
    // 院边店管理
    case 'drugstoreNearHospitalManagement':
        serverSrc.drugstoreNearHospitalManagement.drugstoreNearHospitalManagement_Search = hasPermission(allBehaviors, 'drugstoreNearHospitalManagement_Search'); // 查询
        serverSrc.drugstoreNearHospitalManagement.drugstoreNearHospitalManagement_Add = hasPermission(allBehaviors, 'drugstoreNearHospitalManagement_Add'); // 新增
        serverSrc.drugstoreNearHospitalManagement.drugstoreNearHospitalManagement_BatchDelete = hasPermission(allBehaviors, 'drugstoreNearHospitalManagement_BatchDelete'); // 批量删除
        serverSrc.drugstoreNearHospitalManagement.drugstoreNearHospitalManagement_Delete = hasPermission(allBehaviors, 'drugstoreNearHospitalManagement_Delete'); // 删除
        serverSrc.drugstoreNearHospitalManagement.drugstoreNearHospitalManagement_Import = hasPermission(allBehaviors, 'drugstoreNearHospitalManagement_Import'); // 导入
        serverSrc.drugstoreNearHospitalManagement.drugstoreNearHospitalManagement_Export = hasPermission(allBehaviors, 'drugstoreNearHospitalManagement_Export'); // 导出
        break;
    // 收货方日志16
    case 'receiverLog':
        serverSrc.receiverLog.receiverLog_Export = hasPermission(allBehaviors, 'receiverLog_Export'); // 导出
        serverSrc.receiverLog.receiverLog_Search = hasPermission(allBehaviors, 'receiverLog_Search'); // 查询
        break;
    // 大区管理17
    case 'areaList':
        serverSrc.areaList.areaList_Search = hasPermission(allBehaviors, 'areaList_Search'); // 查询
        serverSrc.areaList.areaList_Add = hasPermission(allBehaviors, 'areaList_Add'); // 新增
        serverSrc.areaList.areaList_Export = hasPermission(allBehaviors, 'areaList_Export'); // 导出
        serverSrc.areaList.areaList_Edit = hasPermission(allBehaviors, 'areaList_Edit'); // 编辑
        serverSrc.areaList.areaList_Delete = hasPermission(allBehaviors, 'areaList_Delete'); // 删除
        break;
    // 城市管理18
    case 'cityList':
        serverSrc.cityList.cityList_Search = hasPermission(allBehaviors, 'cityList_Search'); // 查询
        serverSrc.cityList.cityList_Add = hasPermission(allBehaviors, 'cityList_Add'); // 新增
        serverSrc.cityList.cityList_Export = hasPermission(allBehaviors, 'cityList_Export'); // 导出
        serverSrc.cityList.cityList_Edit = hasPermission(allBehaviors, 'cityList_Edit'); // 编辑
        serverSrc.cityList.cityList_Delete = hasPermission(allBehaviors, 'cityList_Delete'); // 删除
        break;
    // 区县管理19
    case 'countyList':
        serverSrc.countyList.countyList_Search = hasPermission(allBehaviors, 'countyList_Search'); // 查询
        serverSrc.countyList.countyList_Add = hasPermission(allBehaviors, 'countyList_Add'); // 新增
        serverSrc.countyList.countyList_Export = hasPermission(allBehaviors, 'countyList_Export'); // 导出
        serverSrc.countyList.countyList_Edit = hasPermission(allBehaviors, 'countyList_Edit'); // 编辑
        serverSrc.countyList.countyList_Delete = hasPermission(allBehaviors, 'countyList_Delete'); // 删除
        break;
    // DDI数据导入20
    case 'ddiImport':
        serverSrc.ddiImport.ddiImport_Upload = hasPermission(allBehaviors, 'ddiImport_Upload');
        break;
    // SAP月数据生成22
    case 'monthlySAPData':
        serverSrc.monthlySAPData.monthlySAPData_Search = hasPermission(allBehaviors, 'monthlySAPData_Search');
        serverSrc.monthlySAPData.monthlySAPData_MonthlyConversion = hasPermission(allBehaviors, 'monthlySAPData_MonthlyConversion');
        break;
    // 上传日志列表23
    case 'manualUploadLog':
        serverSrc.manualUploadLog.manualUploadLog_Search = hasPermission(allBehaviors, 'manualUploadLog_Search');
        serverSrc.manualUploadLog.manualUploadLog_Export = hasPermission(allBehaviors, 'manualUploadLog_Export');
        break;
    // 自动日志列表24
    case 'autoUploadLog':
        serverSrc.autoUploadLog.autoUploadLog_Search = hasPermission(allBehaviors, 'autoUploadLog_Search');
        serverSrc.autoUploadLog.autoUploadLog_Export = hasPermission(allBehaviors, 'autoUploadLog_Export');
        break;
    // 流向商业流向确认25
    case 'distributorSalesFlowConfirm':
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Search = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Search');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Add = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Add');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Delete = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Delete');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Confirm = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Confirm');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_AllConfirm = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_AllConfirm');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Release = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Release');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Frozen = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Frozen');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Export = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Export');
        serverSrc.distributorSalesFlowConfirm.distributorSalesFlowConfirm_Edit = hasPermission(allBehaviors, 'distributorSalesFlowConfirm_Edit');
        break;
    // 可追溯商业流向确认26
    case 'preparatorySalesowConfirm':
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_Search = hasPermission(allBehaviors, 'preparatorySalesowConfirm_Search');
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_Add = hasPermission(allBehaviors, 'preparatorySalesowConfirm_Add');
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_Delete = hasPermission(allBehaviors, 'preparatorySalesowConfirm_Delete');
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_Confirm = hasPermission(allBehaviors, 'preparatorySalesowConfirm_Confirm');
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_AllConfirm = hasPermission(allBehaviors, 'preparatorySalesowConfirm_AllConfirm');
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_Release = hasPermission(allBehaviors, 'preparatorySalesowConfirm_Release');
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_Export = hasPermission(allBehaviors, 'preparatorySalesowConfirm_Export');
        serverSrc.preparatorySalesowConfirm.preparatorySalesowConfirm_Edit = hasPermission(allBehaviors, 'preparatorySalesowConfirm_Edit');
        break;
    // 月采购数据27
    case 'monthlyPurchaseQuery':
        serverSrc.monthlyPurchaseQuery.monthlyPurchaseQuery_Search = hasPermission(allBehaviors, 'monthlyPurchaseQuery_Search');
        serverSrc.monthlyPurchaseQuery.monthlyPurchaseQuery_Export = hasPermission(allBehaviors, 'monthlyPurchaseQuery_Export');
        break;
    // 异常终端报表
    case 'abnormalTerminalReport':
        serverSrc.abnormalTerminalReport.abnormalTerminalReport_Search = hasPermission(allBehaviors, 'abnormalTerminalReport_Search');
        serverSrc.abnormalTerminalReport.abnormalTerminalReport_Export = hasPermission(allBehaviors, 'abnormalTerminalReport_Export');
        break;
    // 日采购数据28
    case 'dailyPurchaseQuery':
        serverSrc.dailyPurchaseQuery.dailyPurchaseQuery_Search = hasPermission(allBehaviors, 'dailyPurchaseQuery_Search');
        serverSrc.dailyPurchaseQuery.dailyPurchaseQuery_Export = hasPermission(allBehaviors, 'dailyPurchaseQuery_Export');
        break;
    // 流向商业明细流向查询29
    case 'salesFlowDetailQuery':
        serverSrc.salesFlowDetailQuery.salesFlowDetailQuery_Search = hasPermission(allBehaviors, 'salesFlowDetailQuery_Search');
        serverSrc.salesFlowDetailQuery.salesFlowDetailQuery_Export = hasPermission(allBehaviors, 'salesFlowDetailQuery_Export');
        serverSrc.salesFlowDetailQuery.salesFlowDetailQuery_WriteOff = hasPermission(allBehaviors, 'salesFlowDetailQuery_WriteOff');
        serverSrc.salesFlowDetailQuery.salesFlowDetailQuery_Edit = hasPermission(allBehaviors, 'salesFlowDetailQuery_Edit');
        serverSrc.salesFlowDetailQuery.salesFlowDetailQuery_BatchUpdate = hasPermission(allBehaviors, 'salesFlowDetailQuery_BatchUpdate');
        break;
    // 可追溯商业流向查询30
    case 'preparatorySalesFlowQuery':
        serverSrc.preparatorySalesFlowQuery.preparatorySalesFlowQuery_Search = hasPermission(allBehaviors, 'preparatorySalesFlowQuery_Search');
        serverSrc.preparatorySalesFlowQuery.preparatorySalesFlowQuery_Export = hasPermission(allBehaviors, 'preparatorySalesFlowQuery_Export');
        break;
    // 流向商业月提供情况31
    case 'distributorMonthStatusQuery':
        serverSrc.distributorMonthStatusQuery.distributorMonthStatusQuery_Search = hasPermission(allBehaviors, 'distributorMonthStatusQuery_Search');
        serverSrc.distributorMonthStatusQuery.distributorMonthStatusQuery_Export = hasPermission(allBehaviors, 'distributorMonthStatusQuery_Export');
        break;
    // 流向商业日提供情况32
    case 'distributorDailyStatusQuery':
        serverSrc.distributorDailyStatusQuery.distributorDailyStatusQuery_Search = hasPermission(allBehaviors, 'distributorDailyStatusQuery_Search');
        serverSrc.distributorDailyStatusQuery.distributorDailyStatusQuery_Export = hasPermission(allBehaviors, 'distributorDailyStatusQuery_Export');
        break;
    // 兴奋剂查询34
    case 'analepticQuery':
        serverSrc.analepticQuery.analepticQuery_Search = hasPermission(allBehaviors, 'analepticQuery_Search');
        serverSrc.analepticQuery.analepticQuery_Export = hasPermission(allBehaviors, 'analepticQuery_Export');
        serverSrc.analepticQuery.analepticQuery_Feedback = hasPermission(allBehaviors, 'analepticQuery_Feedback');
        serverSrc.analepticQuery.analepticQuery_Detail = hasPermission(allBehaviors, 'analepticQuery_Detail');
        break;
    // 流向差异核查35
    case 'salesflowDeficiencyInspect':
        serverSrc.salesflowDeficiencyInspect.salesflowDeficiencyInspect_Search = hasPermission(allBehaviors, 'salesflowDeficiencyInspect_Search');
        serverSrc.salesflowDeficiencyInspect.salesflowDeficiencyInspect_Export = hasPermission(allBehaviors, 'salesflowDeficiencyInspect_Export');
        break;
    // 渠道差异核查36
    case 'channelAbnormalInspect':
        serverSrc.channelAbnormalInspect.channelAbnormalInspect_Search = hasPermission(allBehaviors, 'channelAbnormalInspect_Search');
        serverSrc.channelAbnormalInspect.channelAbnormalInspect_Export = hasPermission(allBehaviors, 'channelAbnormalInspect_Export');
        serverSrc.channelAbnormalInspect.channelAbnormalInspect_FieldHandling = hasPermission(allBehaviors, 'channelAbnormalInspect_FieldHandling');
        serverSrc.channelAbnormalInspect.channelAbnormalInspect_See = hasPermission(allBehaviors, 'channelAbnormalInspect_See');
        break;
    // 删除数据提供情况37
    case 'deleteDailyStatus':
        serverSrc.deleteDailyStatus.deleteDailyStatus_Search = hasPermission(allBehaviors, 'deleteDailyStatus_Search');
        serverSrc.deleteDailyStatus.deleteDailyStatus_Export = hasPermission(allBehaviors, 'deleteDailyStatus_Export');
        serverSrc.deleteDailyStatus.deleteDailyStatus_AllDelete = hasPermission(allBehaviors, 'deleteDailyStatus_AllDelete');
        break;
    // 删除月采购数据38
    case 'deleteMonthlyPurchase':
        serverSrc.deleteMonthlyPurchase.deleteMonthlyPurchase_Search = hasPermission(allBehaviors, 'deleteMonthlyPurchase_Search');
        serverSrc.deleteMonthlyPurchase.deleteMonthlyPurchase_Export = hasPermission(allBehaviors, 'deleteMonthlyPurchase_Export');
        serverSrc.deleteMonthlyPurchase.deleteMonthlyPurchase_AllDelete = hasPermission(allBehaviors, 'deleteMonthlyPurchase_AllDelete');
        break;
    // 删除日采购数据39
    case 'deleteDailyPurchase':
        serverSrc.deleteDailyPurchase.deleteDailyPurchase_Search = hasPermission(allBehaviors, 'deleteDailyPurchase_Search');
        serverSrc.deleteDailyPurchase.deleteDailyPurchase_Export = hasPermission(allBehaviors, 'deleteDailyPurchase_Export');
        serverSrc.deleteDailyPurchase.deleteDailyPurchase_AllDelete = hasPermission(allBehaviors, 'deleteDailyPurchase_AllDelete');
        break;
    // 删除日库存40
    case 'deleteDailyStock':
        serverSrc.deleteDailyStock.deleteDailyStock_Search = hasPermission(allBehaviors, 'deleteDailyStock_Search');
        serverSrc.deleteDailyStock.deleteDailyStock_Export = hasPermission(allBehaviors, 'deleteDailyStock_Export');
        serverSrc.deleteDailyStock.deleteDailyStock_AllDelete = hasPermission(allBehaviors, 'deleteDailyStock_AllDelete');
        break;
    // 删除月库存
    case 'deleteMonthlyStock':
        serverSrc.deleteMonthlyStock.deleteMonthlyStock_Search = hasPermission(allBehaviors, 'deleteMonthlyStock_Search');
        serverSrc.deleteMonthlyStock.deleteMonthlyStock_Export = hasPermission(allBehaviors, 'deleteMonthlyStock_Export');
        serverSrc.deleteMonthlyStock.deleteMonthlyStock_AllDelete = hasPermission(allBehaviors, 'deleteMonthlyStock_AllDelete');
        break;
    // 删除日流向41
    case 'deleteDailySalesFlow':
        serverSrc.deleteDailySalesFlow.deleteDailySalesFlow_Search = hasPermission(allBehaviors, 'deleteDailySalesFlow_Search');
        serverSrc.deleteDailySalesFlow.deleteDailySalesFlow_Export = hasPermission(allBehaviors, 'deleteDailySalesFlow_Export');
        serverSrc.deleteDailySalesFlow.deleteDailySalesFlow_AllDelete = hasPermission(allBehaviors, 'deleteDailySalesFlow_AllDelete');
        break;
    // 人员管理42
    case 'userlist':
        serverSrc.userlist.userlist_Search = hasPermission(allBehaviors, 'userlist_Search'); // 查询
        serverSrc.userlist.userlist_Add = hasPermission(allBehaviors, 'userlist_Add'); // 新增
        serverSrc.userlist.userlist_Edit = hasPermission(allBehaviors, 'userlist_Edit'); // 编辑
        serverSrc.userlist.userlist_ResetPassword = hasPermission(allBehaviors, 'userlist_ResetPassword'); // 重置密码
        serverSrc.userlist.userlist_EntryDepart = hasPermission(allBehaviors, 'userlist_EntryDepart'); // 入离职
        serverSrc.userlist.userlist_ToConfigure = hasPermission(allBehaviors, 'userlist_ToConfigure'); // 配置角色
        serverSrc.userlist.userlist_Detail = hasPermission(allBehaviors, 'userlist_Detail'); // 查看
        break;
    // 默认批号修改43
    case 'batchModification':
        serverSrc.batchModification.batchModification_Search = hasPermission(allBehaviors, 'batchModification_Search'); // 查询
        serverSrc.batchModification.batchModification_Edit = hasPermission(allBehaviors, 'batchModification_Edit'); // 修改
        break;
    // 产品权限组管理44
    case 'productGroupList':
        serverSrc.productGroupList.productGroupList_Search = hasPermission(allBehaviors, 'productGroupList_Search'); // 查询
        serverSrc.productGroupList.productGroupList_Add = hasPermission(allBehaviors, 'productGroupList_Add'); // 新增
        serverSrc.productGroupList.productGroupList_Edit = hasPermission(allBehaviors, 'productGroupList_Edit'); // 编辑
        serverSrc.productGroupList.productGroupList_Delete = hasPermission(allBehaviors, 'productGroupList_Delete'); // 删除
        break;
    // 收货方类型45
    case 'receiverTypeList':
        serverSrc.receiverTypeList.receiverTypeList_Add = hasPermission(allBehaviors, 'receiverTypeList_Add'); // 新增
        serverSrc.receiverTypeList.receiverTypeList_Export = hasPermission(allBehaviors, 'receiverTypeList_Export'); // 导出
        serverSrc.receiverTypeList.receiverTypeList_Edit = hasPermission(allBehaviors, 'receiverTypeList_Edit'); // 编辑
        break;
    // 字典管理46
    case 'dictionaryAdministration':
        serverSrc.dictionaryAdministration.dictionaryAdministration_Search = hasPermission(allBehaviors, 'dictionaryAdministration_Search');
        serverSrc.dictionaryAdministration.dictionaryAdministration_Add = hasPermission(allBehaviors, 'dictionaryAdministration_Add');
        serverSrc.dictionaryAdministration.dictionaryAdministration_Edit = hasPermission(allBehaviors, 'dictionaryAdministration_Edit');
        serverSrc.dictionaryAdministration.dictionaryAdministration_Delete = hasPermission(allBehaviors, 'dictionaryAdministration_Delete');
        break;
    // 货主管理47
    case 'shipperList':
        serverSrc.shipperList.shipper_Add = hasPermission(allBehaviors, 'shipper_Add');
        serverSrc.shipperList.shipper_Search = hasPermission(allBehaviors, 'shipper_Search');
        serverSrc.shipperList.shipper_Stop = hasPermission(allBehaviors, 'shipper_Stop');
        serverSrc.shipperList.shipper_Edit = hasPermission(allBehaviors, 'shipper_Edit');
        serverSrc.shipperList.shipper_UploadSeal = hasPermission(allBehaviors, 'shipper_UploadSeal');
        serverSrc.shipperList.shipper_Authentication = hasPermission(allBehaviors, 'shipper_Authentication');
        break;
    // 进销存管理48
    case 'inventoryList':
        serverSrc.inventoryList.inventoryList_Search = hasPermission(allBehaviors, 'inventoryList_Search');
        serverSrc.inventoryList.inventoryList_Export = hasPermission(allBehaviors, 'inventoryList_Export');
        serverSrc.inventoryList.inventoryList_Edit = hasPermission(allBehaviors, 'inventoryList_Edit');
        break;
    // 流向商业库存天数49
    case 'distributorInventoryDay':
        serverSrc.distributorInventoryDay.distributorInventoryDay_Search = hasPermission(allBehaviors, 'distributorInventoryDay_Search');
        serverSrc.distributorInventoryDay.distributorInventoryDay_Export = hasPermission(allBehaviors, 'distributorInventoryDay_Export');
        serverSrc.distributorInventoryDay.distributorInventoryDay_Recalculate = hasPermission(allBehaviors, 'distributorInventoryDay_Recalculate');
        break;
    // 流向商业库存天数50
    case 'productInventoryDay':
        serverSrc.productInventoryDay.productInventoryDay_Search = hasPermission(allBehaviors, 'productInventoryDay_Search');
        serverSrc.productInventoryDay.productInventoryDay_Export = hasPermission(allBehaviors, 'productInventoryDay_Export');
        serverSrc.productInventoryDay.productInventoryDay_Recalculate = hasPermission(allBehaviors, 'productInventoryDay_Recalculate');
        break;
    // 当月库存预估51
    case 'inventoryEstimateMonth':
        serverSrc.inventoryEstimateMonth.inventoryEstimateMonth_Search = hasPermission(allBehaviors, 'inventoryEstimateMonth_Search');
        serverSrc.inventoryEstimateMonth.inventoryEstimateMonth_Export = hasPermission(allBehaviors, 'inventoryEstimateMonth_Export');
        serverSrc.inventoryEstimateMonth.inventoryEstimateMonth_Recalculate = hasPermission(allBehaviors, 'inventoryEstimateMonth_Recalculate');
        break;
    // 月进销货差异核查52
    case 'monthlySalesFlowInspect':
        serverSrc.monthlySalesFlowInspect.monthlySalesFlowInspect_Search = hasPermission(allBehaviors, 'monthlySalesFlowInspect_Search');
        serverSrc.monthlySalesFlowInspect.monthlySalesFlowInspect_Export = hasPermission(allBehaviors, 'monthlySalesFlowInspect_Export');
        serverSrc.monthlySalesFlowInspect.monthlySalesFlowInspect_Recalculate = hasPermission(allBehaviors, 'monthlySalesFlowInspect_Recalculate');
        break;
    // 日库存差异核查53
    case 'dailyInventoryInspect':
        serverSrc.dailyInventoryInspect.dailyInventoryInspect_Search = hasPermission(allBehaviors, 'dailyInventoryInspect_Search');
        serverSrc.dailyInventoryInspect.dailyInventoryInspect_Export = hasPermission(allBehaviors, 'dailyInventoryInspect_Export');
        serverSrc.dailyInventoryInspect.dailyInventoryInspect_FeedBack = hasPermission(allBehaviors, 'dailyInventoryInspect_FeedBack');
        break;
    // 进销存其它项汇总54
    case 'otherItemReport':
        serverSrc.otherItemReport.otherItemReport_Search = hasPermission(allBehaviors, 'otherItemReport_Search');
        serverSrc.otherItemReport.otherItemReport_Export = hasPermission(allBehaviors, 'otherItemReport_Export');
        break;
    // 按产品‌经销商统计 55
    case 'distributorProductReport':
        serverSrc.distributorProductReport.distributorProductReport_Search = hasPermission(allBehaviors, 'distributorProductReport_Search');
        serverSrc.distributorProductReport.distributorProductReport_Export = hasPermission(allBehaviors, 'distributorProductReport_Export');
        serverSrc.distributorProductReport.distributorProductReport_Recalculate = hasPermission(allBehaviors, 'distributorProductReport_Recalculate');
        break;
    // 流向商业库存金额56
    case 'distributorAmountReport':
        serverSrc.distributorAmountReport.distributorAmountReport_Search = hasPermission(allBehaviors, 'distributorAmountReport_Search');
        serverSrc.distributorAmountReport.distributorAmountReport_Export = hasPermission(allBehaviors, 'distributorAmountReport_Export');
        serverSrc.distributorAmountReport.distributorAmountReport_Recalculate = hasPermission(allBehaviors, 'distributorAmountReport_Recalculate');
        break;
    // 流向商业负库存57
    case 'flowNegativeStock':
        serverSrc.flowNegativeStock.flowNegativeStock_Search = hasPermission(allBehaviors, 'flowNegativeStock_Search');
        serverSrc.flowNegativeStock.flowNegativeStock_Export = hasPermission(allBehaviors, 'flowNegativeStock_Export');
        serverSrc.flowNegativeStock.flowNegativeStock_FeedBack = hasPermission(allBehaviors, 'flowNegativeStock_FeedBack');
        break;
    // 日库存查询58
    case 'dailyInventoryList':
        serverSrc.dailyInventoryList.dailyInventoryList_Search = hasPermission(allBehaviors, 'dailyInventoryList_Search');
        serverSrc.dailyInventoryList.dailyInventoryList_Export = hasPermission(allBehaviors, 'dailyInventoryList_Export');
        break;
    // 安全库存范围设置59
    case 'safetyStockRange':
        serverSrc.safetyStockRange.safetyStockRange_Search = hasPermission(allBehaviors, 'safetyStockRange_Search');
        serverSrc.safetyStockRange.safetyStockRange_Add = hasPermission(allBehaviors, 'safetyStockRange_Add');
        serverSrc.safetyStockRange.safetyStockRange_BatchImport = hasPermission(allBehaviors, 'safetyStockRange_BatchImport');
        serverSrc.safetyStockRange.safetyStockRange_BatchDelete = hasPermission(allBehaviors, 'safetyStockRange_BatchDelete');
        serverSrc.safetyStockRange.safetyStockRange_Edit = hasPermission(allBehaviors, 'safetyStockRange_Edit');
        serverSrc.safetyStockRange.safetyStockRange_Delete = hasPermission(allBehaviors, 'safetyStockRange_Delete');
        break;
    // 库存报表设置60
    case 'inventoryReportSetup':
        serverSrc.inventoryReportSetup.inventoryReportSetup_SaveInventoryYearMonth = hasPermission(allBehaviors, 'inventoryReportSetup_SaveInventoryYearMonth');
        serverSrc.inventoryReportSetup.inventoryReportSetup_SaveInventoryMonthXSetUp = hasPermission(allBehaviors, 'inventoryReportSetup_SaveInventoryMonthXSetUp');
        break;
    // 日销售流向查询61
    case 'dailySalesFlowQuery':
        serverSrc.dailySalesFlowQuery.dailySalesFlowQuery_Search = hasPermission(allBehaviors, 'dailySalesFlowQuery_Search');
        serverSrc.dailySalesFlowQuery.dailySalesFlowQuery_Export = hasPermission(allBehaviors, 'dailySalesFlowQuery_Export');
        break;
    // 日流向 By Terminal 62
    case 'diurnalFlowByTerminal':
        serverSrc.diurnalFlowByTerminal.diurnalFlowByTerminal_Search = hasPermission(allBehaviors, 'diurnalFlowByTerminal_Search');
        serverSrc.diurnalFlowByTerminal.diurnalFlowByTerminal_Export = hasPermission(allBehaviors, 'diurnalFlowByTerminal_Export');
        break;
    // 月流向 By Terminal 63
    case 'monthlyFlowByTerminal':
        serverSrc.monthlyFlowByTerminal.monthlyFlowByTerminal_Search = hasPermission(allBehaviors, 'monthlyFlowByTerminal_Search');
        serverSrc.monthlyFlowByTerminal.monthlyFlowByTerminal_Export = hasPermission(allBehaviors, 'monthlyFlowByTerminal_Export');
        break;
    // 销售日流向
    case 'salesDailySalesFlowQuery':
        serverSrc.salesDailySalesFlowQuery.salesDailySalesFlowQuery_Search = hasPermission(allBehaviors, 'salesDailySalesFlowQuery_Search');
        serverSrc.salesDailySalesFlowQuery.salesDailySalesFlowQuery_Export = hasPermission(allBehaviors, 'salesDailySalesFlowQuery_Export');
        break;
    // 月销售流向查询
    case 'monthlySalesFlowQuery':
        serverSrc.monthlySalesFlowQuery.monthlySalesFlowQuery_Search = hasPermission(allBehaviors, 'monthlySalesFlowQuery_Search');
        serverSrc.monthlySalesFlowQuery.monthlySalesFlowQuery_Export = hasPermission(allBehaviors, 'monthlySalesFlowQuery_Export');
        break;
    // 周期管理 64
    case 'cycleManagement':
        serverSrc.cycleManagement.cycleManagement_Add = hasPermission(allBehaviors, 'cycleManagement_Add');
        serverSrc.cycleManagement.cycleManagement_Search = hasPermission(allBehaviors, 'cycleManagement_Search');
        serverSrc.cycleManagement.cycleManagement_Edit = hasPermission(allBehaviors, 'cycleManagement_Edit');
        break;
    // 异常日志管理 65
    case 'exceptionLogQuery':
        serverSrc.exceptionLogQuery.exceptionLogQuery_Search = hasPermission(allBehaviors, 'exceptionLogQuery_Search');
        serverSrc.exceptionLogQuery.exceptionLogQuery_Export = hasPermission(allBehaviors, 'exceptionLogQuery_Export');
        break;
    // 事件日志管理 66
    case 'eventLogQuery':
        serverSrc.eventLogQuery.eventLogQuery_Search = hasPermission(allBehaviors, 'eventLogQuery_Search');
        serverSrc.eventLogQuery.eventLogQuery_Export = hasPermission(allBehaviors, 'eventLogQuery_Export');
        break;
    // 经销商产品信息 67
    case 'distributorProductQuery':
        serverSrc.distributorProductQuery.distributorProductQuery_Search = hasPermission(allBehaviors, 'distributorProductQuery_Search');
        serverSrc.distributorProductQuery.distributorProductQuery_Export = hasPermission(allBehaviors, 'distributorProductQuery_Export');
        break;
    // 打单商业查询
    case 'distributorProductForBO':
        serverSrc.distributorProductForBO.distributorProductForBO_Search = hasPermission(allBehaviors, 'distributorProductForBO_Search');
        serverSrc.distributorProductForBO.distributorProductForBO_Export = hasPermission(allBehaviors, 'distributorProductForBO_Export');
        break;
    // 计算报告 68
    case 'inventoryGenerate':
        serverSrc.inventoryGenerate.inventoryGenerate_Calculate = hasPermission(allBehaviors, 'inventoryGenerate_Calculate');
        break;
    // 按收货方汇总 69
    case 'analysisByReceiver':
        serverSrc.analysisByReceiver.analysisByReceiver_Search = hasPermission(allBehaviors, 'analysisByReceiverQuery_Search');
        serverSrc.analysisByReceiver.analysisByReceiver_Export = hasPermission(allBehaviors, 'analysisByReceiverQuery_Export');
        break;
    // 按年度分型汇总 70
    case 'analysisInMarket':
        serverSrc.analysisInMarket.analysisInMarket_Search = hasPermission(allBehaviors, 'analysisInMarket_Search');
        serverSrc.analysisInMarket.analysisInMarket_Export = hasPermission(allBehaviors, 'analysisInMarket_Export');
        break;
    // 销退Owner 71
    case 'salesReturnOwner':
        serverSrc.salesReturnOwner.salesReturnOwner_Search = hasPermission(allBehaviors, 'salesReturnOwner_Search');
        serverSrc.salesReturnOwner.salesReturnOwner_Add = hasPermission(allBehaviors, 'salesReturnOwner_Add');
        serverSrc.salesReturnOwner.salesReturnOwner_Edit = hasPermission(allBehaviors, 'salesReturnOwner_Edit');
        serverSrc.salesReturnOwner.salesReturnOwner_Delete = hasPermission(allBehaviors, 'salesReturnOwner_Delete');
        break;
    // 销退原因 73
    case 'salesReturnReason':
        serverSrc.salesReturnReason.salesReturnReason_Search = hasPermission(allBehaviors, 'salesReturnReason_Search');
        serverSrc.salesReturnReason.salesReturnReason_Add = hasPermission(allBehaviors, 'salesReturnReason_Add');
        serverSrc.salesReturnReason.salesReturnReason_Edit = hasPermission(allBehaviors, 'salesReturnReason_Edit');
        serverSrc.salesReturnReason.salesReturnReason_Delete = hasPermission(allBehaviors, 'salesReturnReason_Delete');
        break;
    case 'salesReturnReply':
        serverSrc.salesReturnReply.salesReturnReply_Search = hasPermission(allBehaviors, 'salesReturnReply_Search');
        serverSrc.salesReturnReply.salesReturnReply_Export = hasPermission(allBehaviors, 'salesReturnReply_Export');
        serverSrc.salesReturnReply.salesReturnReply_Save = hasPermission(allBehaviors, 'salesReturnReply_Save');
        break;
    case 'salesReturnReport':
        serverSrc.salesReturnReport.salesReturnReport_Search = hasPermission(allBehaviors, 'salesReturnReport_Search');
        break;
    case 'analysisByReceiverProvince':
        serverSrc.analysisByReceiverProvince.analysisByReceiverProvince_Search = hasPermission(allBehaviors, 'analysisByReceiverProvince_Search');
        serverSrc.analysisByReceiverProvince.analysisByReceiverProvince_Export = hasPermission(allBehaviors, 'analysisByReceiverProvince_Export');
        break;
    // 按发货方汇总
    case 'analysisInByDistributor':
        serverSrc.analysisInByDistributor.analysisInByDistributor_Search = hasPermission(allBehaviors, 'analysisInByDistributor_Search');
        serverSrc.analysisInByDistributor.analysisInByDistributor_Export = hasPermission(allBehaviors, 'analysisInByDistributor_Export');
        break;
    case 'replyRateLineSetting':
        serverSrc.replyRateLineSetting.replyRateLineSetting_Save = hasPermission(allBehaviors, 'replyRateLineSetting_Save');
        break;
    case 'replyRadixSetting':
        serverSrc.replyRadixSetting.replyRadixSetting_Search = hasPermission(allBehaviors, 'replyRadixSetting_Search');
        serverSrc.replyRadixSetting.replyRadixSetting_Export = hasPermission(allBehaviors, 'replyRadixSetting_Export');
        serverSrc.replyRadixSetting.replyRadixSetting_Edit = hasPermission(allBehaviors, 'replyRadixSetting_Edit');
        serverSrc.replyRadixSetting.replyRadixSetting_BatchEdit = hasPermission(allBehaviors, 'replyRadixSetting_BatchEdit');
        break;
    case 'gXPBusinessInformationImport':
        serverSrc.gXPBusinessInformationImport.gXPBusinessInformationImport_Upload = hasPermission(allBehaviors, 'gXPBusinessInformationImport_Upload');
        serverSrc.gXPBusinessInformationImport.gXPBusinessInformationImport_Search = hasPermission(allBehaviors, 'gXPBusinessInformationImport_Search');
        break;
    case 'warehouse':
        serverSrc.warehouse.warehouse_Search = hasPermission(allBehaviors, 'warehouse_Search');
        serverSrc.warehouse.warehouse_Add = hasPermission(allBehaviors, 'warehouse_Add');
        serverSrc.warehouse.warehouse_Edit = hasPermission(allBehaviors, 'warehouse_Edit');
        serverSrc.warehouse.warehouse_EnableDisable = hasPermission(allBehaviors, 'warehouse_EnableDisable');
        break;
    case 'shipperWarehouseProductRelation':
        serverSrc.shipperWarehouseProductRelation.shipperWarehouseProductRelation_Search = hasPermission(allBehaviors, 'shipperWarehouseProductRelation_Search');
        serverSrc.shipperWarehouseProductRelation.shipperWarehouseProductRelation_Add = hasPermission(allBehaviors, 'shipperWarehouseProductRelation_Add');
        serverSrc.shipperWarehouseProductRelation.shipperWarehouseProductRelation_Edit = hasPermission(allBehaviors, 'shipperWarehouseProductRelation_Edit');
        serverSrc.shipperWarehouseProductRelation.shipperWarehouseProductRelation_EnableDisable = hasPermission(allBehaviors, 'shipperWarehouseProductRelation_EnableDisable');
        serverSrc.shipperWarehouseProductRelation.shipperWarehouseProductRelation_Transfer = hasPermission(allBehaviors, 'shipperWarehouseProductRelation_Transfer');
        serverSrc.shipperWarehouseProductRelation.shipperWarehouseProductRelation_BatchUpload = hasPermission(allBehaviors, 'shipperWarehouseProductRelation_BatchUpload');
        serverSrc.shipperWarehouseProductRelation.shipperWarehouseProductRelation_Export = hasPermission(allBehaviors, 'shipperWarehouseProductRelation_Export');
        break;
    case 'customerCredit':
        serverSrc.customerCredit.customerCredit_Search = hasPermission(allBehaviors, 'customerCredit_Search');
        serverSrc.customerCredit.customerCredit_Export = hasPermission(allBehaviors, 'customerCredit_Export');
        break;
    case 'orderProcessing':
        serverSrc.orderProcessing.orderProcessing_Search = hasPermission(allBehaviors, 'orderProcessing_Search');
        serverSrc.orderProcessing.orderProcessing_Processing = hasPermission(allBehaviors, 'orderProcessing_Processing');
        serverSrc.orderProcessing.orderProcessing_Cancel = hasPermission(allBehaviors, 'orderProcessing_Cancel');
        serverSrc.orderProcessing.orderProcessing_NewOrder = hasPermission(allBehaviors, 'orderProcessing_NewOrder');
        break;
    case 'orderProcess':
        serverSrc.orderProcess.orderProcess_Search = hasPermission(allBehaviors, 'orderProcess_Search');
        serverSrc.orderProcess.orderProcess_BatchContractStamped = hasPermission(allBehaviors, 'orderProcess_BatchContractStamped');
        serverSrc.orderProcess.orderProcess_BatchContractReturn = hasPermission(allBehaviors, 'orderProcess_BatchContractReturn');
        serverSrc.orderProcess.orderProcess_Export = hasPermission(allBehaviors, 'orderProcess_Export');
        serverSrc.orderProcess.orderProcess_ContractStamped = hasPermission(allBehaviors, 'orderProcess_ContractStamped');
        serverSrc.orderProcess.orderProcess_EditSO = hasPermission(allBehaviors, 'orderProcess_EditSO');
        serverSrc.orderProcess.orderProcess_EditDN = hasPermission(allBehaviors, 'orderProcess_EditDN');
        serverSrc.orderProcess.orderProcess_ContractReturn = hasPermission(allBehaviors, 'orderProcess_ContractReturn');
        serverSrc.orderProcess.orderProcess_ContractDetail = hasPermission(allBehaviors, 'orderProcess_ContractDetail');
        serverSrc.orderProcess.orderProcess_InvoiceDetail = hasPermission(allBehaviors, 'orderProcess_InvoiceDetail');
        serverSrc.orderProcess.orderProcess_EditOrder = hasPermission(allBehaviors, 'orderProcess_EditOrder');
        serverSrc.orderProcess.orderProcess_SendMailAgain = hasPermission(allBehaviors, 'orderProcess_SendMailAgain');
        serverSrc.orderProcess.orderProcess_Remark = hasPermission(allBehaviors, 'orderProcess_Remark');
        serverSrc.orderProcess.orderProcess_Cancel = hasPermission(allBehaviors, 'orderProcess_Cancel');
        serverSrc.orderProcess.orderProcess_ProcessingLog = hasPermission(allBehaviors, 'orderProcess_ProcessingLog');
        serverSrc.orderProcess.orderProcess_OrderDetail = hasPermission(allBehaviors, 'orderProcess_OrderDetail');
        serverSrc.orderProcess.orderProcess_ChangContractHasBeenSent = hasPermission(allBehaviors, 'orderProcess_ChangContractHasBeenSent');
        serverSrc.orderProcess.orderProcess_UploadContractAndSign = hasPermission(allBehaviors, 'orderProcess_UploadContractAndSign');
        serverSrc.orderProcess.orderProcess_ApprovalContract = hasPermission(allBehaviors, 'orderProcess_ApprovalContract');
        break;
    case 'contractScanConfirmation':
        serverSrc.contractScanConfirmation.contractScanConfirmation_Confirm = hasPermission(allBehaviors, 'contractScanConfirmation_Confirm');
        break;
    case 'orderDetailEnquiry':
        serverSrc.orderDetailEnquiry.orderDetailEnquiry_Search = hasPermission(allBehaviors, 'orderDetailEnquiry_Search');
        serverSrc.orderDetailEnquiry.orderDetailEnquiry_Export = hasPermission(allBehaviors, 'orderDetailEnquiry_Export');
        break;
    case 'informationMaintenance':
        serverSrc.informationMaintenance.informationMaintenance_Search = hasPermission(allBehaviors, 'informationMaintenance_Search');
        serverSrc.informationMaintenance.informationMaintenance_BatchUpload = hasPermission(allBehaviors, 'informationMaintenance_BatchUpload');
        serverSrc.informationMaintenance.informationMaintenance_Export = hasPermission(allBehaviors, 'informationMaintenance_Export');
        serverSrc.informationMaintenance.informationMaintenance_Delete = hasPermission(allBehaviors, 'informationMaintenance_Delete');
        serverSrc.informationMaintenance.informationMaintenance_ExpressBatchUpload = hasPermission(allBehaviors, 'informationMaintenance_ExpressBatchUpload');
        break;
    case 'importSalesPlan':
        serverSrc.importSalesPlan.importSalesPlan_Search = hasPermission(allBehaviors, 'orderProcessing_Search');
        serverSrc.importSalesPlan.importSalesPlan_BatchDelete = hasPermission(allBehaviors, 'importSalesPlan_BatchDelete');
        serverSrc.importSalesPlan.importSalesPlan_BatchConfirm = hasPermission(allBehaviors, 'importSalesPlan_BatchConfirm');
        serverSrc.importSalesPlan.importSalesPlan_BatchRelease = hasPermission(allBehaviors, 'importSalesPlan_BatchRelease');
        serverSrc.importSalesPlan.importSalesPlan_AllNoticeConfirm = hasPermission(allBehaviors, 'importSalesPlan_AllNoticeConfirm');
        serverSrc.importSalesPlan.importSalesPlan_BatchCompulsoryConfirm = hasPermission(allBehaviors, 'importSalesPlan_BatchCompulsoryConfirm');
        serverSrc.importSalesPlan.importSalesPlan_BatchUpload = hasPermission(allBehaviors, 'importSalesPlan_BatchUpload');
        serverSrc.importSalesPlan.importSalesPlan_Export = hasPermission(allBehaviors, 'importSalesPlan_Export');
        serverSrc.importSalesPlan.importSalesPlan_Edit = hasPermission(allBehaviors, 'importSalesPlan_Edit');
        serverSrc.importSalesPlan.importSalesPlan_Delete = hasPermission(allBehaviors, 'importSalesPlan_Delete');
        serverSrc.importSalesPlan.importSalesPlan_Confirm = hasPermission(allBehaviors, 'importSalesPlan_Confirm');
        serverSrc.importSalesPlan.importSalesPlan_CompulsoryConfirm = hasPermission(allBehaviors, 'importSalesPlan_CompulsoryConfirm');
        serverSrc.importSalesPlan.importSalesPlan_Adjustment = hasPermission(allBehaviors, 'importSalesPlan_Adjustment');
        serverSrc.importSalesPlan.importSalesPlan_Add = hasPermission(allBehaviors, 'importSalesPlan_Add');
        break;
    case 'salesPlanKPI':
        serverSrc.salesPlanKPI.salesPlanKPI_Setting = hasPermission(allBehaviors, 'salesPlanKPI_Setting');
        serverSrc.salesPlanKPI.salesPlanKPI_Search = hasPermission(allBehaviors, 'salesPlanKPI_Search');
        serverSrc.salesPlanKPI.salesPlanKPI_Export = hasPermission(allBehaviors, 'salesPlanKPI_Export');
        serverSrc.salesPlanKPI.salesPlanKPI_Update = hasPermission(allBehaviors, 'salesPlanKPI_Update');
        break;
    case 'achievingRateReport':
        serverSrc.achievingRateReport.achievingRateReport_Search = hasPermission(allBehaviors, 'achievingRateReport_Search');
        serverSrc.achievingRateReport.achievingRateReport_Export = hasPermission(allBehaviors, 'achievingRateReport_Export');
        break;
    case 'contractSetting':
        serverSrc.contractSetting.contractSetting_Save = hasPermission(allBehaviors, 'contractSetting_Save');
        break;
    case 'tenderPriceQuery':
        serverSrc.tenderPriceQuery.tenderPriceQuery_Search = hasPermission(allBehaviors, 'tenderPriceQuery_Search');
        serverSrc.tenderPriceQuery.tenderPriceQuery_Export = hasPermission(allBehaviors, 'tenderPriceQuery_Export');
        break;
    case 'biddingDetailQuery':
        serverSrc.biddingDetailQuery.biddingDetailQuery_Search = hasPermission(allBehaviors, 'biddingDetailQuery_Search');
        serverSrc.biddingDetailQuery.biddingDetailQuery_Export = hasPermission(allBehaviors, 'biddingDetailQuery_Export');
        break;
    case 'biddingProject':
        serverSrc.biddingProject.biddingProject_Search = hasPermission(allBehaviors, 'biddingProject_Search');
        serverSrc.biddingProject.biddingProject_Add = hasPermission(allBehaviors, 'biddingProject_Add');
        serverSrc.biddingProject.biddingProject_Export = hasPermission(allBehaviors, 'biddingProject_Export');
        serverSrc.biddingProject.biddingProject_Edit = hasPermission(allBehaviors, 'biddingProject_Edit');
        serverSrc.biddingProject.biddingProject_Delete = hasPermission(allBehaviors, 'biddingProject_Delete');
        break;
    case 'suspectAllocation':
        serverSrc.suspectAllocation.suspectAllocation_Search = hasPermission(allBehaviors, 'suspectAllocation_Search');
        serverSrc.suspectAllocation.suspectAllocation_Export = hasPermission(allBehaviors, 'suspectAllocation_Export');
        serverSrc.suspectAllocation.suspectAllocation_FeedBack = hasPermission(allBehaviors, 'suspectAllocation_FeedBack');
        break;
    case 'baselineOfInventoryCheckDate':
        serverSrc.baselineOfInventoryCheckDate.baselineOfInventoryCheckDate_Search = hasPermission(allBehaviors, 'baselineOfInventoryCheckDate_Search');
        serverSrc.baselineOfInventoryCheckDate.baselineOfInventoryCheckDate_Edit = hasPermission(allBehaviors, 'baselineOfInventoryCheckDate_Edit');
        serverSrc.baselineOfInventoryCheckDate.baselineOfInventoryCheckDate_Export = hasPermission(allBehaviors, 'baselineOfInventoryCheckDate_Export');
        serverSrc.baselineOfInventoryCheckDate.baselineOfInventoryCheckDate_BatchEdit = hasPermission(allBehaviors, 'baselineOfInventoryCheckDate_BatchEdit');
        break;
    case 'baselineSettingofSalesQuantity':
        serverSrc.baselineSettingofSalesQuantity.baselineSettingofSalesQuantity_Search = hasPermission(allBehaviors, 'baselineSettingofSalesQuantity_Search');
        serverSrc.baselineSettingofSalesQuantity.baselineSettingofSalesQuantity_Edit = hasPermission(allBehaviors, 'baselineSettingofSalesQuantity_Edit');
        serverSrc.baselineSettingofSalesQuantity.baselineSettingofSalesQuantity_Export = hasPermission(allBehaviors, 'baselineSettingofSalesQuantity_Export');
        serverSrc.baselineSettingofSalesQuantity.baselineSettingofSalesQuantity_BatchEdit = hasPermission(allBehaviors, 'baselineSettingofSalesQuantity_BatchEdit');
        break;
    case 'baselineSettingOfTargetHospital':
        serverSrc.baselineSettingOfTargetHospital.baselineSettingOfTargetHospital_Search = hasPermission(allBehaviors, 'baselineSettingOfTargetHospital_Search');
        serverSrc.baselineSettingOfTargetHospital.baselineSettingOfTargetHospital_Edit = hasPermission(allBehaviors, 'baselineSettingOfTargetHospital_Edit');
        serverSrc.baselineSettingOfTargetHospital.baselineSettingOfTargetHospital_Export = hasPermission(allBehaviors, 'baselineSettingOfTargetHospital_Export');
        serverSrc.baselineSettingOfTargetHospital.baselineSettingOfTargetHospital_BatchEdit = hasPermission(allBehaviors, 'baselineSettingOfTargetHospital_BatchEdit');
        break;
    case 'discrepancyRateOfInventoryDaily':
        serverSrc.discrepancyRateOfInventoryDaily.discrepancyRateOfInventoryDaily_Save = hasPermission(allBehaviors, 'discrepancyRateOfInventoryDaily_Save');
        break;
    case 'targetHospitalSales':
        serverSrc.targetHospitalSales.targetHospitalSales_Search = hasPermission(allBehaviors, 'targetHospitalSales_Search');
        serverSrc.targetHospitalSales.targetHospitalSales_Export = hasPermission(allBehaviors, 'targetHospitalSales_Export');
        break;
    case 'noticeDefaultNumberSetting':
        serverSrc.noticeDefaultNumberSetting.noticeDefaultNumberSetting_Save = hasPermission(allBehaviors, 'noticeDefaultNumberSetting_Save');
        break;
    case 'ddiContributionRateSetting':
        serverSrc.ddiContributionRateSetting.ddiContributionRateSetting_Save = hasPermission(allBehaviors, 'ddiContributionRateSetting_Save');
        break;
    case 'otherShipperSettings':
        serverSrc.otherShipperSettings.otherShipperSettings_Search = hasPermission(allBehaviors, 'otherShipperSettings_Search');
        serverSrc.otherShipperSettings.otherShipperSettings_Add = hasPermission(allBehaviors, 'otherShipperSettings_Add');
        serverSrc.otherShipperSettings.otherShipperSettings_Delete = hasPermission(allBehaviors, 'otherShipperSettings_Delete');
        break;
    case 'queryVolumeOfTargetHospital':
        serverSrc.queryVolumeOfTargetHospital.queryVolumeOfTargetHospital_Search = hasPermission(allBehaviors, 'queryVolumeOfTargetHospital_Search');
        serverSrc.queryVolumeOfTargetHospital.queryVolumeOfTargetHospital_Export = hasPermission(allBehaviors, 'queryVolumeOfTargetHospital_Export');
        serverSrc.queryVolumeOfTargetHospital.queryVolumeOfTargetHospital_Upload = hasPermission(allBehaviors, 'queryVolumeOfTargetHospital_Upload');
        serverSrc.queryVolumeOfTargetHospital.queryVolumeOfTargetHospital_UploadLog = hasPermission(allBehaviors, 'queryVolumeOfTargetHospital_UploadLog');
        break;
    case 'batchMonthlyInventory':
        serverSrc.batchMonthlyInventory.batchMonthlyInventory_Search = hasPermission(allBehaviors, 'batchMonthlyInventory_Search');
        serverSrc.batchMonthlyInventory.batchMonthlyInventory_Export = hasPermission(allBehaviors, 'batchMonthlyInventory_Export');
        break;
    case 'notificationSetting':
        serverSrc.notificationSetting.notificationSetting_Save = hasPermission(allBehaviors, 'notificationSetting_Save');
        break;
    case 'inventoryEstimateSetting':
        serverSrc.inventoryEstimateSetting.inventoryEstimateSetting_Search = hasPermission(allBehaviors, 'inventoryEstimateSetting_Search');
        serverSrc.inventoryEstimateSetting.inventoryEstimateSetting_Add = hasPermission(allBehaviors, 'inventoryEstimateSetting_Add');
        serverSrc.inventoryEstimateSetting.inventoryEstimateSetting_Delete = hasPermission(allBehaviors, 'inventoryEstimateSetting_Delete');
        break;
    case 'documentList':
        serverSrc.documentList.documentList_Search = hasPermission(allBehaviors, 'documentList_Search');
        serverSrc.documentList.documentList_Add = hasPermission(allBehaviors, 'documentList_Add');
        serverSrc.documentList.documentList_Delete = hasPermission(allBehaviors, 'documentList_Delete');
        break;
    case 'distributorSalesFlowSimpleQuery':
        serverSrc.distributorSalesFlowSimpleQuery.distributorSalesFlowSimpleQuery_Search = hasPermission(allBehaviors, 'distributorSalesFlowSimpleQuery_Search');
        serverSrc.distributorSalesFlowSimpleQuery.distributorSalesFlowSimpleQuery_Export = hasPermission(allBehaviors, 'distributorSalesFlowSimpleQuery_Export');
        break;
    case 'biddingPriceConflict':
        serverSrc.biddingPriceConflict.biddingPriceConflict_Search = hasPermission(allBehaviors, 'biddingPriceConflict_Search');
        serverSrc.biddingPriceConflict.biddingPriceConflict_Export = hasPermission(allBehaviors, 'biddingPriceConflict_Export');
        serverSrc.biddingPriceConflict.biddingPriceConflict_GenerateReport = hasPermission(allBehaviors, 'biddingPriceConflict_GenerateReport');
        serverSrc.biddingPriceConflict.biddingPriceConflict_Edit = hasPermission(allBehaviors, 'biddingPriceConflict_Edit');
        break;
    case 'theLowestBidPrice':
        serverSrc.theLowestBidPrice.theLowestBidPrice_Search = hasPermission(allBehaviors, 'theLowestBidPrice_Search');
        serverSrc.theLowestBidPrice.theLowestBidPrice_Export = hasPermission(allBehaviors, 'theLowestBidPrice_Export');
        break;
    case 'biddingOfDelistingProducts':
        serverSrc.biddingOfDelistingProducts.biddingOfDelistingProducts_Search = hasPermission(allBehaviors, 'biddingOfDelistingProducts_Search');
        serverSrc.biddingOfDelistingProducts.biddingOfDelistingProducts_Export = hasPermission(allBehaviors, 'biddingOfDelistingProducts_Export');
        break;
    case 'compareByDistributor':
        serverSrc.compareByDistributor.compareByDistributor_Search = hasPermission(allBehaviors, 'compareByDistributor_Search');
        serverSrc.compareByDistributor.compareByDistributor_Export = hasPermission(allBehaviors, 'compareByDistributor_Export');
        break;
    case 'compareByReceiver':
        serverSrc.compareByReceiver.compareByReceiver_Search = hasPermission(allBehaviors, 'compareByReceiver_Search');
        serverSrc.compareByReceiver.compareByReceiver_Export = hasPermission(allBehaviors, 'compareByReceiver_Export');
        break;
    case 'analysisByDistributorAndReceiver':
        serverSrc.analysisByDistributorAndReceiver.analysisByDistributorAndReceiver_Search = hasPermission(allBehaviors, 'analysisByDistributorAndReceiver_Search');
        serverSrc.analysisByDistributorAndReceiver.analysisByDistributorAndReceiver_Export = hasPermission(allBehaviors, 'analysisByDistributorAndReceiver_Export');
        break;
    case 'defaultBidder':
        serverSrc.defaultBidder.defaultBidder_Search = hasPermission(allBehaviors, 'defaultBidder_Search');
        serverSrc.defaultBidder.defaultBidder_Add = hasPermission(allBehaviors, 'defaultBidder_Add');
        serverSrc.defaultBidder.defaultBidder_Edit = hasPermission(allBehaviors, 'defaultBidder_Edit');
        serverSrc.defaultBidder.defaultBidder_EnableDisable = hasPermission(allBehaviors, 'defaultBidder_EnableDisable');
        serverSrc.defaultBidder.defaultBidder_Delete = hasPermission(allBehaviors, 'defaultBidder_Delete');
        serverSrc.defaultBidder.defaultBidder_Export = hasPermission(allBehaviors, 'defaultBidder_Export');
        break;
    case 'biddingType':
        serverSrc.biddingType.biddingType_Search = hasPermission(allBehaviors, 'biddingType_Search');
        serverSrc.biddingType.biddingType_Add = hasPermission(allBehaviors, 'biddingType_Add');
        serverSrc.biddingType.biddingType_Edit = hasPermission(allBehaviors, 'biddingType_Edit');
        serverSrc.biddingType.biddingType_EnableDisable = hasPermission(allBehaviors, 'biddingType_EnableDisable');
        serverSrc.biddingType.biddingType_Delete = hasPermission(allBehaviors, 'biddingType_Delete');
        serverSrc.biddingType.biddingType_Export = hasPermission(allBehaviors, 'biddingType_Export');
        break;
    case 'biddingStep':
        serverSrc.biddingStep.biddingStep_Search = hasPermission(allBehaviors, 'biddingStep_Search');
        serverSrc.biddingStep.biddingStep_Add = hasPermission(allBehaviors, 'biddingStep_Add');
        serverSrc.biddingStep.biddingStep_Edit = hasPermission(allBehaviors, 'biddingStep_Edit');
        serverSrc.biddingStep.biddingStep_EnableDisable = hasPermission(allBehaviors, 'biddingStep_EnableDisable');
        serverSrc.biddingStep.biddingStep_Delete = hasPermission(allBehaviors, 'biddingStep_Delete');
        serverSrc.biddingStep.biddingStep_Export = hasPermission(allBehaviors, 'biddingStep_Export');
        break;
    case 'myApproval':
        serverSrc.myApproval.myApproval_Search = hasPermission(allBehaviors, 'myApproval_Search');
        serverSrc.myApproval.myApproval_Export = hasPermission(allBehaviors, 'myApproval_Export');
        serverSrc.myApproval.myApproval_Detail = hasPermission(allBehaviors, 'myApproval_Detail');
        break;
    case 'drugStoreCompensationFile':
        serverSrc.drugStoreCompensationFile.drugStoreCompensationFile_Search = hasPermission(allBehaviors, 'drugStoreCompensationFile_Search');
        serverSrc.drugStoreCompensationFile.drugStoreCompensationFile_Upload = hasPermission(allBehaviors, 'drugStoreCompensationFile_Upload');
        serverSrc.drugStoreCompensationFile.drugStoreCompensationFile_Delete = hasPermission(allBehaviors, 'drugStoreCompensationFile_Delete');
        break;
    case 'compensationSummaryList':
        serverSrc.compensationSummaryList.compensationSummaryList_Recalculate = hasPermission(allBehaviors, 'compensationSummaryList_Recalculate');
        serverSrc.compensationSummaryList.compensationSummaryList_Detail = hasPermission(allBehaviors, 'compensationSummaryList_Detail');
        serverSrc.compensationSummaryList.compensationSummaryList_End = hasPermission(allBehaviors, 'compensationSummaryList_End');
        serverSrc.compensationSummaryList.compensationSummaryList_Edit = hasPermission(allBehaviors, 'compensationSummaryList_Edit');
        serverSrc.compensationSummaryList.compensationSummaryList_NewUnitPrice = hasPermission(allBehaviors, 'compensationSummaryList_NewUnitPrice');
        serverSrc.compensationSummaryList.compensationSummaryList_Search = hasPermission(allBehaviors, 'compensationSummaryList_Search');
        serverSrc.compensationSummaryList.compensationSummaryList_DifferentReport = hasPermission(allBehaviors, 'compensationSummaryList_DifferentReport');
        serverSrc.compensationSummaryList.compensationSummaryList_Delete = hasPermission(allBehaviors, 'compensationSummaryList_Delete');
        serverSrc.compensationSummaryList.compensationSummaryList_ExportAll = hasPermission(allBehaviors, 'compensationSummaryList_ExportAll');
        break;
    case 'drugStoreSetting':
        serverSrc.drugStoreSetting.drugStoreSetting_Search = hasPermission(allBehaviors, 'drugStoreSetting_Search');
        serverSrc.drugStoreSetting.drugStoreSetting_Add = hasPermission(allBehaviors, 'drugStoreSetting_Add');
        serverSrc.drugStoreSetting.drugStoreSetting_Edit = hasPermission(allBehaviors, 'drugStoreSetting_Edit');
        serverSrc.drugStoreSetting.drugStoreSetting_Delete = hasPermission(allBehaviors, 'drugStoreSetting_Delete');
        serverSrc.drugStoreSetting.drugStoreSetting_Export = hasPermission(allBehaviors, 'drugStoreSetting_Export');
        break;
    case 'confirmUnitPriceDetail':
        serverSrc.confirmUnitPriceDetail.confirmUnitPriceDetail_Export = hasPermission(allBehaviors, 'confirmUnitPriceDetail_Export');
        serverSrc.confirmUnitPriceDetail.confirmUnitPriceDetail_ConfirmAll = hasPermission(allBehaviors, 'confirmUnitPriceDetail_ConfirmAll');
        serverSrc.confirmUnitPriceDetail.confirmUnitPriceDetail_Confirm = hasPermission(allBehaviors, 'confirmUnitPriceDetail_Confirm');
        serverSrc.confirmUnitPriceDetail.confirmUnitPriceDetail_Search = hasPermission(allBehaviors, 'confirmUnitPriceDetail_Search');
        break;
    case 'militaryRegionManagement':
        serverSrc.militaryRegionManagement.militaryRegionManagement_Search = hasPermission(allBehaviors, 'militaryRegionManagement_Search');
        serverSrc.militaryRegionManagement.militaryRegionManagement_Add = hasPermission(allBehaviors, 'militaryRegionManagement_Add');
        serverSrc.militaryRegionManagement.militaryRegionManagement_Export = hasPermission(allBehaviors, 'militaryRegionManagement_Export');
        serverSrc.militaryRegionManagement.militaryRegionManagement_Edit = hasPermission(allBehaviors, 'militaryRegionManagement_Edit');
        serverSrc.militaryRegionManagement.militaryRegionManagement_Delete = hasPermission(allBehaviors, 'militaryRegionManagement_Delete');
        break;
    case 'returnCertificateRequirement':
        serverSrc.returnCertificateRequirement.returnCertificateRequirement_Search = hasPermission(allBehaviors, 'returnCertificateRequirement_Search');
        serverSrc.returnCertificateRequirement.returnCertificateRequirement_Add = hasPermission(allBehaviors, 'returnCertificateRequirement_Add');
        serverSrc.returnCertificateRequirement.returnCertificateRequirement_Export = hasPermission(allBehaviors, 'returnCertificateRequirement_Export');
        serverSrc.returnCertificateRequirement.returnCertificateRequirement_Edit = hasPermission(allBehaviors, 'returnCertificateRequirement_Edit');
        serverSrc.returnCertificateRequirement.returnCertificateRequirement_Delete = hasPermission(allBehaviors, 'returnCertificateRequirement_Delete');
        break;
    case 'certificateManagement':
        serverSrc.certificateManagement.certificateManagement_Search = hasPermission(allBehaviors, 'certificateManagement_Search');
        serverSrc.certificateManagement.certificateManagement_Add = hasPermission(allBehaviors, 'certificateManagement_Add');
        serverSrc.certificateManagement.certificateManagement_Edit = hasPermission(allBehaviors, 'certificateManagement_Edit');
        serverSrc.certificateManagement.certificateManagement_Delete = hasPermission(allBehaviors, 'certificateManagement_Delete');
        serverSrc.certificateManagement.certificateManagement_Upload = hasPermission(allBehaviors, 'certificateManagement_Upload');
        serverSrc.certificateManagement.certificateManagement_CreatePaymentPlan = hasPermission(allBehaviors, 'certificateManagement_CreatePaymentPlan');
        serverSrc.certificateManagement.certificateManagement_PaymentPlanDetail = hasPermission(allBehaviors, 'certificateManagement_PaymentPlanDetail');
        serverSrc.certificateManagement.certificateManagement_CreateResult = hasPermission(allBehaviors, 'certificateManagement_CreateResult');
        serverSrc.certificateManagement.certificateManagement_Return = hasPermission(allBehaviors, 'certificateManagement_Return');
        serverSrc.certificateManagement.certificateManagement_CollectPaymentCertificate = hasPermission(allBehaviors, 'certificateManagement_CollectPaymentCertificate');
        serverSrc.certificateManagement.certificateManagement_CollectPaymentDetail = hasPermission(allBehaviors, 'certificateManagement_CollectPaymentDetail');
        serverSrc.certificateManagement.certificateManagement_PDFZipDownload = hasPermission(allBehaviors, 'certificateManagement_PDFZipDownload');
        serverSrc.certificateManagement.returnCertificateRequirement_Search = hasPermission(allBehaviors, 'returnCertificateRequirement_Search');
        serverSrc.certificateManagement.certificateManagement_Correct = hasPermission(allBehaviors, 'certificateManagement_Correct');
        serverSrc.certificateManagement.certificateManagement_Stop = hasPermission(allBehaviors, 'certificateManagement_Stop');
        serverSrc.certificateManagement.certificateManagement_SealResult = hasPermission(allBehaviors, 'certificateManagement_SealResult');
        serverSrc.certificateManagement.certificateManagement_BatchSeal = hasPermission(allBehaviors, 'certificateManagement_BatchSeal');
        serverSrc.certificateManagement.certificateManagement_BatchRelease = hasPermission(allBehaviors, 'certificateManagement_BatchRelease');
        serverSrc.certificateManagement.certificateManagement_Release = hasPermission(allBehaviors, 'certificateManagement_Release');
        serverSrc.certificateManagement.certificateManagement_ReSign = hasPermission(allBehaviors, 'certificateManagement_ReSign');
        serverSrc.certificateManagement.certificateManagement_Cancel = hasPermission(allBehaviors, 'certificateManagement_Cancel');
        serverSrc.certificateManagement.certificateManagement_ReplaceNewFile = hasPermission(allBehaviors, 'certificateManagement_ReplaceNewFile');
        serverSrc.certificateManagement.certificateManagement_ApprovelNotice = hasPermission(allBehaviors, 'certificateManagement_ApprovelNotice');
        serverSrc.certificateManagement.certificateManagement_DownloadNotice = hasPermission(allBehaviors, 'certificateManagement_DownloadNotice');
        serverSrc.certificateManagement.certificateManagement_EditNotice = hasPermission(allBehaviors, 'certificateManagement_EditNotice');
        serverSrc.certificateManagement.certificateManagement_ResendEmail = hasPermission(allBehaviors, 'certificateManagement_ResendEmail');
        serverSrc.certificateManagement.certificateManagement_DownloadFile = hasPermission(allBehaviors, 'certificateManagement_DownloadFile');
        serverSrc.certificateManagement.certificateManagement_GenerationFile = hasPermission(allBehaviors, 'certificateManagement_GenerationFile');
        serverSrc.certificateManagement.certificateManagement_Export = hasPermission(allBehaviors, 'certificateManagement_Export');
        break;
    case 'certificateQuery':
        serverSrc.certificateQuery.certificateQuery_Search = hasPermission(allBehaviors, 'certificateQuery_Search');
        serverSrc.certificateQuery.certificateQuery_Export = hasPermission(allBehaviors, 'certificateQuery_Export');
        serverSrc.certificateQuery.certificateQuery_ReturnCertificateDownLoad = hasPermission(allBehaviors, 'certificateQuery_ReturnCertificateDownLoad');
        serverSrc.certificateQuery.certificateQuery_CollectPaymentCertificateDownLoad = hasPermission(allBehaviors, 'certificateQuery_CollectPaymentCertificateDownLoad');
        serverSrc.certificateQuery.certificateQuery_CollectPaymentCertificateDetailDownLoad = hasPermission(allBehaviors, 'certificateQuery_CollectPaymentCertificateDetailDownLoad');
        break;
    case 'kpiSummary':
        serverSrc.kpiSummary.kpiSummary_Search = hasPermission(allBehaviors, 'kpiSummary_Search');
        serverSrc.kpiSummary.kpiSummary_Add_Switch = hasPermission(allBehaviors, 'kpiSummary_Add_Switch');
        break;
    case 'dailySummaryReport':
        serverSrc.dailySummaryReport.dailySummaryReport_Search = hasPermission(allBehaviors, 'dailySummaryReport_Search');
        serverSrc.dailySummaryReport.dailySummaryReport_Export = hasPermission(allBehaviors, 'dailySummaryReport_Export');
        break;
    case 'receiverAliasChangeRequest':
        serverSrc.receiverAliasChangeRequest.receiverAliasChangeRequest_Search = hasPermission(allBehaviors, 'receiverAliasChangeRequest_Search');
        serverSrc.receiverAliasChangeRequest.receiverAliasChangeRequest_Export = hasPermission(allBehaviors, 'receiverAliasChangeRequest_Export');
        serverSrc.receiverAliasChangeRequest.receiverAliasChangeRequest_Confirm = hasPermission(allBehaviors, 'receiverAliasChangeRequest_Confirm');
        serverSrc.receiverAliasChangeRequest.receiverAliasChangeRequest_Reject = hasPermission(allBehaviors, 'receiverAliasChangeRequest_Reject');
        break;
    case 'biddingQueryForRepresentative':
        serverSrc.biddingQueryForRepresentative.biddingQueryForRepresentative_Search = hasPermission(allBehaviors, 'biddingQueryForRepresentative_Search');
        serverSrc.biddingQueryForRepresentative.biddingQueryForRepresentative_AddSecondBargaining = hasPermission(allBehaviors, 'biddingQueryForRepresentative_AddSecondBargaining');
        serverSrc.biddingQueryForRepresentative.biddingQueryForRepresentative_Edit = hasPermission(allBehaviors, 'biddingQueryForRepresentative_Edit');
        serverSrc.biddingQueryForRepresentative.biddingQueryForRepresentative_Export = hasPermission(allBehaviors, 'biddingQueryForRepresentative_Export');
        serverSrc.biddingQueryForRepresentative.biddingQueryForRepresentative_Detail = hasPermission(allBehaviors, 'biddingQueryForRepresentative_Detail');
        break;
    case 'customerManagement':
        serverSrc.customerManagement.customerManagement_Export = hasPermission(allBehaviors, 'customerManagement_Export');
        serverSrc.customerManagement.customerManagement_ApprovalSeal = hasPermission(allBehaviors, 'customerManagement_ApprovalSeal');
        serverSrc.customerManagement.customerManagement_UploadSeal = hasPermission(allBehaviors, 'customerManagement_UploadSeal');
        serverSrc.customerManagement.customerManagement_UnbindWeChat = hasPermission(allBehaviors, 'customerManagement_UnbindWeChat');
        serverSrc.customerManagement.customerManagement_CancelRegister = hasPermission(allBehaviors, 'customerManagement_CancelRegister');
        serverSrc.customerManagement.customerManagement_CreateInvitationCode = hasPermission(allBehaviors, 'customerManagement_CreateInvitationCode');
        serverSrc.customerManagement.customerManagement_Detail = hasPermission(allBehaviors, 'customerManagement_Detail');
        serverSrc.customerManagement.customerManagement_Search = hasPermission(allBehaviors, 'customerManagement_Search');
        break;
    case 'customerNoticeManagement':
        serverSrc.customerNoticeManagement.customerNoticeManagement_Search = hasPermission(allBehaviors, 'customerNoticeManagement_Search');
        serverSrc.customerNoticeManagement.customerNoticeManagement_Delete = hasPermission(allBehaviors, 'customerNoticeManagement_Delete');
        serverSrc.customerNoticeManagement.customerNoticeManagement_Release = hasPermission(allBehaviors, 'customerNoticeManagement_Release');
        serverSrc.customerNoticeManagement.customerNoticeManagement_Cancel = hasPermission(allBehaviors, 'customerNoticeManagement_Cancel');
        serverSrc.customerNoticeManagement.customerNoticeManagement_Edit = hasPermission(allBehaviors, 'customerNoticeManagement_Edit');
        serverSrc.customerNoticeManagement.customerNoticeManagement_Add = hasPermission(allBehaviors, 'customerNoticeManagement_Add');
        break;
    case 'materialGroup':
        serverSrc.materialGroup.materialGroup_Search = hasPermission(allBehaviors, 'materialGroup_Search');
        serverSrc.materialGroup.materialGroup_Export = hasPermission(allBehaviors, 'materialGroup_Export');
        serverSrc.materialGroup.materialGroup_IsCompensation = hasPermission(allBehaviors, 'materialGroup_IsCompensation');
        break;
    case 'certificateDetailRepresentative':
        serverSrc.certificateDetailRepresentative.certificateDetailRepresentative_Search = hasPermission(allBehaviors, 'certificateDetailRepresentative_Search');
        serverSrc.certificateDetailRepresentative.certificateDetailRepresentative_Export = hasPermission(allBehaviors, 'certificateDetailRepresentative_Export');
        break;
    case 'quarterCompensation':
        serverSrc.quarterCompensation.quarterCompensation_Search = hasPermission(allBehaviors, 'quarterCompensation_Search');
        serverSrc.quarterCompensation.quarterCompensation_Add = hasPermission(allBehaviors, 'quarterCompensation_Add');
        serverSrc.quarterCompensation.quarterCompensation_Edit = hasPermission(allBehaviors, 'quarterCompensation_Edit');
        serverSrc.quarterCompensation.quarterCompensation_ReCalculate = hasPermission(allBehaviors, 'quarterCompensation_ReCalculate');
        serverSrc.quarterCompensation.quarterCompensation_Result = hasPermission(allBehaviors, 'quarterCompensation_Result');
        serverSrc.quarterCompensation.quarterCompensation_ResultExport = hasPermission(allBehaviors, 'quarterCompensation_ResultExport');
        serverSrc.quarterCompensation.quarterCompensation_Delete = hasPermission(allBehaviors, 'quarterCompensation_Delete');
        serverSrc.quarterCompensation.quarterCompensation_Error = hasPermission(allBehaviors, 'quarterCompensation_Error');
        break;
    case 'accruedExpense':
        serverSrc.accruedExpense.accruedExpense_Search = hasPermission(allBehaviors, 'accruedExpense_Search');
        serverSrc.accruedExpense.accruedExpense_Add = hasPermission(allBehaviors, 'accruedExpense_Add');
        serverSrc.accruedExpense.accruedExpense_Edit = hasPermission(allBehaviors, 'accruedExpense_Edit');
        serverSrc.accruedExpense.accruedExpense_PriceDetail = hasPermission(allBehaviors, 'accruedExpense_PriceDetail');
        serverSrc.accruedExpense.accruedExpense_ReCalculateAll = hasPermission(allBehaviors, 'accruedExpense_ReCalculateAll');
        serverSrc.accruedExpense.accruedExpense_ExportAll = hasPermission(allBehaviors, 'accruedExpense_ExportAll');
        serverSrc.accruedExpense.accruedExpense_ReCalculateMany = hasPermission(allBehaviors, 'accruedExpense_ReCalculateMany');
        serverSrc.accruedExpense.accruedExpense_EditPrice = hasPermission(allBehaviors, 'accruedExpense_EditPrice');
        serverSrc.accruedExpense.accruedExpense_ReCalculateOne = hasPermission(allBehaviors, 'accruedExpense_ReCalculateOne');
        serverSrc.accruedExpense.accruedExpense_ShowPrice = hasPermission(allBehaviors, 'accruedExpense_ShowPrice');
        serverSrc.accruedExpense.accruedExpense_DeletePrice = hasPermission(allBehaviors, 'accruedExpense_DeletePrice');
        serverSrc.accruedExpense.accruedExpense_BatchDeletePrice = hasPermission(allBehaviors, 'accruedExpense_BatchDeletePrice');
        serverSrc.accruedExpense.accruedExpense_Result = hasPermission(allBehaviors, 'accruedExpense_Result');
        serverSrc.accruedExpense.accruedExpense_ReCalculate = hasPermission(allBehaviors, 'accruedExpense_ReCalculate');
        serverSrc.accruedExpense.accruedExpense_Export = hasPermission(allBehaviors, 'accruedExpense_Export');
        serverSrc.accruedExpense.accruedExpense_Report = hasPermission(allBehaviors, 'accruedExpense_Report');
        serverSrc.accruedExpense.accruedExpense_Delete = hasPermission(allBehaviors, 'accruedExpense_Delete');
        serverSrc.accruedExpense.accruedExpense_Error = hasPermission(allBehaviors, 'accruedExpense_Error');
        serverSrc.accruedExpense.accruedExpense_CalculatePrice = hasPermission(allBehaviors, 'accruedExpense_CalculatePrice');
        serverSrc.accruedExpense.accruedExpense_Calculate = hasPermission(allBehaviors, 'accruedExpense_Calculate');
        serverSrc.accruedExpense.accruedExpense_ExportReport = hasPermission(allBehaviors, 'accruedExpense_ExportReport');
        break;
    case 'inventoryModifyReport':
        serverSrc.inventoryModifyReport.inventoryModifyReport_Search = hasPermission(allBehaviors, 'inventoryModifyReport_Search');
        serverSrc.inventoryModifyReport.inventoryModifyReport_Export = hasPermission(allBehaviors, 'inventoryModifyReport_Export');
        break;
    case 'splitDetailQuery':
        serverSrc.splitDetailQuery.splitDetailQuery_Search = hasPermission(allBehaviors, 'splitDetailQuery_Search');
        serverSrc.splitDetailQuery.splitDetailQuery_Export = hasPermission(allBehaviors, 'splitDetailQuery_Export');
        break;
    case 'visitPlan':
        serverSrc.visitPlan.visitPlan_Search = hasPermission(allBehaviors, 'visitPlan_Search');
        serverSrc.visitPlan.visitPlan_Edit = hasPermission(allBehaviors, 'visitPlan_Edit');
        serverSrc.visitPlan.visitPlan_EnableDisable = hasPermission(allBehaviors, 'visitPlan_EnableDisable');
        serverSrc.visitPlan.visitPlan_GeneratePlan = hasPermission(allBehaviors, 'visitPlan_GeneratePlan');
        serverSrc.visitPlan.visitPlan_PublishPlan = hasPermission(allBehaviors, 'visitPlan_PublishPlan');
        serverSrc.visitPlan.visitPlan_BatchPublish = hasPermission(allBehaviors, 'visitPlan_BatchPublish');
        break;
    case 'visitResult':
        serverSrc.visitResult.visitResult_Search = hasPermission(allBehaviors, 'visitResult_Search');
        serverSrc.visitResult.visitResult_Export = hasPermission(allBehaviors, 'visitResult_Export');
        serverSrc.visitResult.visitResult_Detail = hasPermission(allBehaviors, 'visitResult_Detail');
        break;
    case 'drugstoreManagement':
        serverSrc.drugstoreManagement.drugstoreManagement_Search = hasPermission(allBehaviors, 'drugstoreManagement_Search');
        serverSrc.drugstoreManagement.drugstoreManagement_Add = hasPermission(allBehaviors, 'drugstoreManagement_Add');
        serverSrc.drugstoreManagement.drugstoreManagement_Import = hasPermission(allBehaviors, 'drugstoreManagement_Import');
        serverSrc.drugstoreManagement.drugstoreManagement_Export = hasPermission(allBehaviors, 'drugstoreManagement_Export');
        serverSrc.drugstoreManagement.drugstoreManagement_Delete = hasPermission(allBehaviors, 'drugstoreManagement_Delete');
        serverSrc.drugstoreManagement.drugstoreManagement_BatchDelete = hasPermission(allBehaviors, 'drugstoreManagement_BatchDelete');
        break;
    case 'drugstoreBUManagement':
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_Add = hasPermission(allBehaviors, 'drugstoreBUManagement_Add');
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_Delete = hasPermission(allBehaviors, 'drugstoreBUManagement_Delete');
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_Detail = hasPermission(allBehaviors, 'drugstoreBUManagement_Detail');
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_Edit = hasPermission(allBehaviors, 'drugstoreBUManagement_Edit');
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_Export = hasPermission(allBehaviors, 'drugstoreBUManagement_Export');
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_Import = hasPermission(allBehaviors, 'drugstoreBUManagement_Import');
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_Search = hasPermission(allBehaviors, 'drugstoreBUManagement_Search');
        serverSrc.drugstoreBUManagement.drugstoreBUManagement_BatchDelete = hasPermission(allBehaviors, 'drugstoreBUManagement_BatchDelete');
        break;
    case 'inventoryBatchNumberReport':
        serverSrc.inventoryByBatchNumberReport.inventoryByBatchNumberList_Search = hasPermission(allBehaviors, 'inventoryByBatchNumberList_Search');
        serverSrc.inventoryByBatchNumberReport.inventoryByBatchNumberList_Export = hasPermission(allBehaviors, 'inventoryByBatchNumberList_Export');
        break;
    case 'sfeLogManagement':
        serverSrc.sfeLogManagement.SFELogQuery_Search = hasPermission(allBehaviors, 'SFELogQuery_Search');
        serverSrc.sfeLogManagement.SFELogQuery_Export = hasPermission(allBehaviors, 'SFELogQuery_Export');
        serverSrc.sfeLogManagement.SFELogQuery_ReExecute = hasPermission(allBehaviors, 'SFELogQuery_ReExecute');
        break;
    case 'targetReceiver':
        serverSrc.targetReceiver.targetReceiver_Search = hasPermission(allBehaviors, 'targetReceiver_Search');
        serverSrc.targetReceiver.targetReceiver_Add = hasPermission(allBehaviors, 'targetReceiver_Add');
        serverSrc.targetReceiver.targetReceiver_Edit = hasPermission(allBehaviors, 'targetReceiver_Edit');
        serverSrc.targetReceiver.targetReceiver_Delete = hasPermission(allBehaviors, 'targetReceiver_Delete');
        serverSrc.targetReceiver.targetReceiver_BatchImport = hasPermission(allBehaviors, 'targetReceiver_BatchImport');
        serverSrc.targetReceiver.targetReceiver_BatchDisable = hasPermission(allBehaviors, 'targetReceiver_BatchDisable');
        serverSrc.targetReceiver.targetReceiver_Export = hasPermission(allBehaviors, 'targetReceiver_Export');
        break;
    case 'accountingPrice':
        serverSrc.accountingPrice.accountingPrice_Search = hasPermission(allBehaviors, 'accountingPrice_Search');
        serverSrc.accountingPrice.accountingPrice_Add = hasPermission(allBehaviors, 'accountingPrice_Add');
        serverSrc.accountingPrice.accountingPrice_Edit = hasPermission(allBehaviors, 'accountingPrice_Edit');
        serverSrc.accountingPrice.accountingPrice_Delete = hasPermission(allBehaviors, 'accountingPrice_Delete');
        serverSrc.accountingPrice.accountingPrice_Import = hasPermission(allBehaviors, 'accountingPrice_Import');
        serverSrc.accountingPrice.accountingPrice_Export = hasPermission(allBehaviors, 'accountingPrice_Export');
        break;
    case 'doctor':
        serverSrc.doctor.doctor_Search = hasPermission(allBehaviors, 'doctor_Search');
        serverSrc.doctor.doctor_Export = hasPermission(allBehaviors, 'doctor_Export');
        serverSrc.doctor.doctor_AddSpeaker = hasPermission(allBehaviors, 'doctor_AddSpeaker');
        serverSrc.doctor.doctor_StopDoctor = hasPermission(allBehaviors, 'doctor_StopDoctor');
        serverSrc.doctor.doctor_UpgradeSpeaker = hasPermission(allBehaviors, 'doctor_UpgradeSpeaker');
        serverSrc.doctor.doctor_AddDoctorOfDataManager = hasPermission(allBehaviors, 'doctor_AddDoctorOfDataManager');
        serverSrc.doctor.doctor_AddSpeakerOfDataManager = hasPermission(allBehaviors, 'doctor_AddSpeakerOfDataManager');
        serverSrc.doctor.doctor_EditDoctorAndSpeaker = hasPermission(allBehaviors, 'doctor_EditDoctorAndSpeaker');
        serverSrc.doctor.doctor_Import = hasPermission(allBehaviors, 'doctor_Import');
        serverSrc.doctor.doctor_ChangeImport = hasPermission(allBehaviors, 'doctor_ChangeImport');
        serverSrc.doctor.doctor_UpgradeDoctorOfDataManager = hasPermission(allBehaviors, 'doctor_UpgradeDoctorOfDataManager');
        serverSrc.doctor.doctor_ChangeDoctorAndSpeaker = hasPermission(allBehaviors, 'doctor_ChangeDoctorAndSpeaker');
        break;
    case 'crmDataApproval':
        serverSrc.crmDataApproval.crmDataApproval_Search = hasPermission(allBehaviors, 'crmDataApproval_Search');
        serverSrc.crmDataApproval.crmDataApproval_Export = hasPermission(allBehaviors, 'crmDataApproval_Export');
        serverSrc.crmDataApproval.crmDataApproval_Approval = hasPermission(allBehaviors, 'crmDataApproval_Approval');
        break;
    case 'departmentProject':
        serverSrc.departmentProject.departmentProject_Search = hasPermission(allBehaviors, 'departmentProject_Search');
        serverSrc.departmentProject.departmentProject_Edit = hasPermission(allBehaviors, 'departmentProject_Edit');
        break;
    case 'receiverLevel':
        serverSrc.receiverLevel.receiverLevel_Search = hasPermission(allBehaviors, 'receiverLevel_Search');
        serverSrc.receiverLevel.receiverLevel_Add = hasPermission(allBehaviors, 'receiverLevel_Add');
        serverSrc.receiverLevel.receiverLevel_Edit = hasPermission(allBehaviors, 'receiverLevel_Edit');
        serverSrc.receiverLevel.receiverLevel_Delete = hasPermission(allBehaviors, 'receiverLevel_Delete');
        serverSrc.receiverLevel.receiverLevel_Import = hasPermission(allBehaviors, 'receiverLevel_Import');
        serverSrc.receiverLevel.receiverLevel_Export = hasPermission(allBehaviors, 'receiverLevel_Export');
        break;
    case 'quota':
        serverSrc.quota.quota_Search = hasPermission(allBehaviors, 'quota_Search');
        serverSrc.quota.quota_Add = hasPermission(allBehaviors, 'quota_Add');
        serverSrc.quota.quota_Edit = hasPermission(allBehaviors, 'quota_Edit');
        serverSrc.quota.quota_Delete = hasPermission(allBehaviors, 'quota_Delete');
        serverSrc.quota.quota_Export = hasPermission(allBehaviors, 'quota_Export');
        serverSrc.quota.quota_Split = hasPermission(allBehaviors, 'quota_Split');
        serverSrc.quota.quota_RegionQuota = hasPermission(allBehaviors, 'quota_RegionQuota');
        serverSrc.quota.quota_RegionQuotaExport = hasPermission(allBehaviors, 'quota_RegionQuotaExport');
        break;
    case 'salesFlowComplaints':
        serverSrc.salesFlowComplaints.salesFlowComplaints_Search = hasPermission(allBehaviors, 'salesFlowComplaints_Search');
        serverSrc.salesFlowComplaints.salesFlowComplaints_Complete = hasPermission(allBehaviors, 'salesFlowComplaints_Complete');
        serverSrc.salesFlowComplaints.salesFlowComplaints_DownloadZip = hasPermission(allBehaviors, 'salesFlowComplaints_DownloadZip');
        serverSrc.salesFlowComplaints.salesFlowComplaints_Approval = hasPermission(allBehaviors, 'salesFlowComplaints_Approval');
        break;
    case 'receiverInStation':
        serverSrc.receiverInStationHistory.receiverInStationHistory_Search = hasPermission(allBehaviors, 'receiverStationHistory_Search');
        serverSrc.receiverInStationHistory.receiverInStationHistory_Export = hasPermission(allBehaviors, 'receiverStationHistory_Export');
        serverSrc.receiverInStationHistory.receiverInStationHistory_Edit = hasPermission(allBehaviors, 'receiverStationHistory_Edit');
        break;
    // 人员挂岗历史管理
    case 'employeeStationHistory':
        serverSrc.employeeStationHistory.employeeStationHistory_Search = hasPermission(allBehaviors, 'employeeStationHistory_Search'); // 查询
        serverSrc.employeeStationHistory.employeeStationHistory_Export = hasPermission(allBehaviors, 'employeeStationHistory_Export'); // 导出
        serverSrc.employeeStationHistory.employeeStationHistory_Edit = hasPermission(allBehaviors, 'employeeStationHistory_Edit');
        break;
    case 'receiverQuota':
        serverSrc.receiverQuota.receiverQuota_Search = hasPermission(allBehaviors, 'receiverQuota_Search');
        serverSrc.receiverQuota.receiverQuota_Add = hasPermission(allBehaviors, 'receiverQuota_Add');
        serverSrc.receiverQuota.receiverQuota_Edit = hasPermission(allBehaviors, 'receiverQuota_Edit');
        serverSrc.receiverQuota.receiverQuota_Delete = hasPermission(allBehaviors, 'receiverQuota_Delete');
        serverSrc.receiverQuota.receiverQuota_Export = hasPermission(allBehaviors, 'receiverQuota_Export');
        serverSrc.receiverQuota.receiverQuota_Import = hasPermission(allBehaviors, 'receiverQuota_Import');
        break;
    case 'hospitalProcurement':
        serverSrc.hospitalProcurement.hospitalProcurement_Search = hasPermission(allBehaviors, 'hospitalProcurement_Search');
        serverSrc.hospitalProcurement.hospitalProcurement_Add = hasPermission(allBehaviors, 'hospitalProcurement_Add');
        serverSrc.hospitalProcurement.hospitalProcurement_Edit = hasPermission(allBehaviors, 'hospitalProcurement_Edit');
        serverSrc.hospitalProcurement.hospitalProcurement_Delete = hasPermission(allBehaviors, 'hospitalProcurement_Delete');
        serverSrc.hospitalProcurement.hospitalProcurement_Export = hasPermission(allBehaviors, 'hospitalProcurement_Export');
        serverSrc.hospitalProcurement.hospitalProcurement_Import = hasPermission(allBehaviors, 'hospitalProcurement_Import');
        break;
    case 'areaQuota':
        serverSrc.areaQuota.areaQuota_Search = hasPermission(allBehaviors, 'areaQuota_Search'); // 查询
        serverSrc.areaQuota.areaQuota_Export = hasPermission(allBehaviors, 'areaQuota_Export'); // 导出
        break;
    case 'drugstoreOfHospital':
        serverSrc.drugstoreOfHospital.drugstoreOfHospital_Search = hasPermission(allBehaviors, 'drugstoreOfHospital_Search');
        serverSrc.drugstoreOfHospital.drugstoreOfHospital_Add = hasPermission(allBehaviors, 'drugstoreOfHospital_Add');
        serverSrc.drugstoreOfHospital.drugstoreOfHospital_Edit = hasPermission(allBehaviors, 'drugstoreOfHospital_Edit');
        serverSrc.drugstoreOfHospital.drugstoreOfHospital_Delete = hasPermission(allBehaviors, 'drugstoreOfHospital_Delete');
        serverSrc.drugstoreOfHospital.drugstoreOfHospital_BatchImport = hasPermission(allBehaviors, 'drugstoreOfHospital_BatchImport');
        serverSrc.drugstoreOfHospital.drugstoreOfHospital_Export = hasPermission(allBehaviors, 'drugstoreOfHospital_Export');
        break;
    case 'salesAchievementReport':
        serverSrc.salesAchievementReport.salesAchievementReport_Search = hasPermission(allBehaviors, 'salesAchievementReport_Search'); // 查询
        serverSrc.salesAchievementReport.salesAchievementReport_Export = hasPermission(allBehaviors, 'salesAchievementReport_Export'); // 导出
        break;
    case 'targetHospitalGrowthRateReport':
        serverSrc.targetHospitalGrowthRateReport.targetHospitalGrowthRateReport_Search = hasPermission(allBehaviors, 'targetHospitalGrowthRateReport_Search'); // 查询
        serverSrc.targetHospitalGrowthRateReport.targetHospitalGrowthRateReport_Export = hasPermission(allBehaviors, 'targetHospitalGrowthRateReport_Export'); // 导出
        break;
    case 'employeeQuota':
        serverSrc.employeeQuota.employeeQuota_Search = hasPermission(allBehaviors, 'employeeQuota_Search');
        serverSrc.employeeQuota.employeeQuota_Add = hasPermission(allBehaviors, 'employeeQuota_Add');
        serverSrc.employeeQuota.employeeQuota_Edit = hasPermission(allBehaviors, 'employeeQuota_Edit');
        serverSrc.employeeQuota.employeeQuota_Delete = hasPermission(allBehaviors, 'employeeQuota_Delete');
        serverSrc.employeeQuota.employeeQuota_Export = hasPermission(allBehaviors, 'employeeQuota_Export');
        serverSrc.employeeQuota.employeeQuota_Import = hasPermission(allBehaviors, 'employeeQuota_Import');
        break;
    case 'cPASaleData':
        serverSrc.cPASaleData.cPASaleData_Search = hasPermission(allBehaviors, 'cPASaleData_Search');
        serverSrc.cPASaleData.cPASaleData_Edit = hasPermission(allBehaviors, 'cPASaleData_Edit');
        serverSrc.cPASaleData.cPASaleData_Delete = hasPermission(allBehaviors, 'cPASaleData_Delete');
        serverSrc.cPASaleData.cPASaleData_BatchImport = hasPermission(allBehaviors, 'cPASaleData_BatchImport');
        serverSrc.cPASaleData.cPASaleData_Export = hasPermission(allBehaviors, 'cPASaleData_Export');
        break;
    case 'receiverSalesAchievementReport':
        serverSrc.receiverSalesAchievementReport.receiverSalesAchievementReport_Search = hasPermission(allBehaviors, 'receiverSalesAchievementReport_Search'); // 查询
        serverSrc.receiverSalesAchievementReport.receiverSalesAchievementReport_Export = hasPermission(allBehaviors, 'receiverSalesAchievementReport_Export'); // 导出
        break;
    case 'receiverSalesWarning':
        serverSrc.receiverSalesWarning.receiverSalesWarning_Search = hasPermission(allBehaviors, 'receiverSalesWarning_Search'); // 查询
        serverSrc.receiverSalesWarning.receiverSalesWarning_Export = hasPermission(allBehaviors, 'receiverSalesWarning_Export'); // 导出
        break;
    case 'hospitalDevelopStatistics':
        serverSrc.hospitalDevelopStatistics.hospitalDevelopStatistics_Search = hasPermission(allBehaviors, 'hospitalDevelopStatistics_Search'); // 查询
        serverSrc.hospitalDevelopStatistics.hospitalDevelopStatistics_Export = hasPermission(allBehaviors, 'hospitalDevelopStatistics_Export'); // 导出
        break;
    case 'salesAmountRankReport':
        serverSrc.salesAmountRankReport.salesAmountRank_Search = hasPermission(allBehaviors, 'salesAmountRank_Search'); // 查询
        serverSrc.salesAmountRankReport.salesAmountRank_Export = hasPermission(allBehaviors, 'salesAmountRank_Export'); // 导出
        break;
    case 'salesAmountABSRankReport':
        serverSrc.salesAmountABSRankReport.salesAmountABSRank_Search = hasPermission(allBehaviors, 'salesAmountABSRank_Search'); // 查询
        serverSrc.salesAmountABSRankReport.salesAmountABSRank_Export = hasPermission(allBehaviors, 'salesAmountABSRank_Export'); // 导出
        break;
    case 'hospitalProcurementWarning':
        serverSrc.hospitalProcurementWarning.hospitalProcurementWarning_Search = hasPermission(allBehaviors, 'hospitalProcurementWarning_Search'); // 查询
        serverSrc.hospitalProcurementWarning.hospitalProcurementWarning_Export = hasPermission(allBehaviors, 'hospitalProcurementWarning_Export'); // 导出
        break;
    case 'department':
        serverSrc.department.department_Search = hasPermission(allBehaviors, 'department_Search');
        serverSrc.department.department_Add = hasPermission(allBehaviors, 'department_Add');
        serverSrc.department.department_Edit = hasPermission(allBehaviors, 'department_Edit');
        serverSrc.department.department_Delete = hasPermission(allBehaviors, 'department_Delete');
        break;
    case 'provinceInStationHistory':
        serverSrc.provinceInStationHistory.provinceInStationHistory_Search = hasPermission(allBehaviors, 'provinceInStationHistory_Search');
        serverSrc.provinceInStationHistory.provinceInStationHistory_Export = hasPermission(allBehaviors, 'provinceInStationHistory_Export');
        serverSrc.provinceInStationHistory.provinceInStationHistory_Edit = hasPermission(allBehaviors, 'provinceInStationHistory_Edit');
        break;

    default:
        break;
    }
}
export default {
    serverSrc,
    behaviorsSession
}
