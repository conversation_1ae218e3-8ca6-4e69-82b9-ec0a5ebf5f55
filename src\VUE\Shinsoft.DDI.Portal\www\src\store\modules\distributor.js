import http from '../../utils/axios'
import constDefinition from '../../utils/constDefinition'
const distributor = {
    state: {
        receiverTypeGroupList: [], // 收货方类型Group/类型--下拉数据
        canbeUpgradeReceiverTypeGroupList: [], // 可升级收货方类型Group/类型--下拉数据
        receiverTypeList: [], // 收货方类型--下拉数据
        tierTypeList: [], // 流向商业管理级别--下拉数据
        medicineGroupList: [], // 所属医药集团--下拉数据
        businessModeList: [], // 经营方式--下拉数据
        downstreamPropertyList: [], // 性质名称--下拉数据
        ddiStatusList: [] // DDI状态--下拉数据
    },
    mutations: {
        initReceiverTypeGroupList (state, data) {
            state.receiverTypeGroupList = data
        },
        initCanbeUpgradeReceiverTypeGroupList (state, data) {
            state.canbeUpgradeReceiverTypeGroupList = data
        },
        // 收货方类型
        initReceiverTypeList (state, data) {
            state.receiverTypeList = data
        },
        // 流向商业管理级别
        initTierTypeList (state, data) {
            state.tierTypeList = data
        },
        // 所属医药集团--下拉数据
        initMedicineGroupList (state, data) {
            state.medicineGroupList = data
        },
        // 经营方式--下拉数据
        initBusinessModeList (state, data) {
            state.businessModeList = data
        },
        // 经营状态--下拉数据
        initBusinessStatusList (state, data) {
            state.businessStatusList = data
        },
        // 性质名称--下拉数据
        initDownstreamPropertyList (state, data) {
            state.downstreamPropertyList = data
        },
        // DDI状态--下拉数据
        initDdiStatusList (state, data) {
            state.ddiStatusList = data
        }
    },
    actions: {
        getReceiverTypeGroup ({ commit }) {
            http
                .get('/Receiver/QueryReceiverTypeCascader')
                .then(response => {
                    let dataAll = response.data;
                    if(dataAll) {
                        dataAll.forEach(element => {
                            element.selectedAll = false;
                            element.indeterminate = false;
                            element.selectedChildren = [];
                        });
                        commit('initReceiverTypeGroupList', JSON.parse(JSON.stringify(dataAll)));
                    }
            });
        },
        getCanbeUpgradeReceiverTypeGroup ({ commit }) {
            http
                .get('/Receiver/QueryCanbeUpgradeReceiverTypeCascader')
                .then(response => {
                    let dataAll = response.data;
                    dataAll.forEach(element => {
                        element.selectedAll = false;
                        element.indeterminate = false;
                        element.selectedChildren = [];
                    });
                    commit('initCanbeUpgradeReceiverTypeGroupList', JSON.parse(JSON.stringify(dataAll)));
            });
        },
        // 收货方类型--下拉
        getReceiverType ({ commit }) {
            http.get('/ReceiverType/QueryCanbeUpgradeReceiverType').then(function (response) {
                commit('initReceiverTypeList', response.data)
            })
        },
        // 流向商业管理级别--下拉
        getTierType ({ commit }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    dictionaryCode: constDefinition.receiver.dictionary.TIERTYPE
                }
            }).then(function (response) {
                commit('initTierTypeList', response.data)
            })
        },
        // 所属医药集团--下拉数据
        getMedicineGroup ({ commit }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    dictionaryCode: constDefinition.receiver.dictionary.MEDICINEGROUP
                }
            }).then(function (response) {
                commit('initMedicineGroupList', response.data)
            })
        },
        // 经营方式--下拉数据
        getBusinessMode ({ commit }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    dictionaryCode: constDefinition.receiver.dictionary.BUSINESSMODE
                }
            }).then(function (response) {
                commit('initBusinessModeList', response.data)
            })
        },
        // 经营状态--下拉数据
        getBusinessStatus ({ commit }) {
            http.get('/Receiver/QueryBusinessStateSelectList').then(function (response) {
                commit('initBusinessStatusList', response.data)
            })
        },
        // 性质名称--下拉数据
        getDownstreamProperty ({ commit }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    dictionaryCode: constDefinition.receiver.dictionary.DOWNSTREAMPROPERTY
                }
            }).then(function (response) {
                commit('initDownstreamPropertyList', response.data)
            })
        },
        // DDI状态--下拉数据
        getDDIStatus ({ commit }) {
            http.get('/Dictionary/QueryDictionaryItemSelectList', {
                params: {
                    dictionaryCode: constDefinition.receiver.dictionary.DDISTATUS
                }
            }).then(function (response) {
                commit('initDdiStatusList', response.data)
            })
        }
    }
}
export default distributor
