import http from '../../utils/axios'
const productBatch = {
    state: {
        productBatchList: [],
        productBatch: {},
        totalCount: 1,
        materialCascader: [],
        material: {}
    },
    mutations: {
        QueryProductBatchList (state, data) {
            state.productBatchList = data.Models
            state.totalCount = data.TotalCount
        },
        QueryMaterialCascader (state, data) {
            state.materialCascader = data
        },
        GetMaterial (state, data) {
            state.material = data
        },
        SetMterial (state, payload) {
            if (payload.hasOwnProperty('Code')) {
                state.material.Code = payload.Code
            }
        }
    },
    actions: {
        queryProductBatchList ({ commit }, params) {
            http.get('/Product/QueryProductBatch', { params: params }).then(function (response) {
                commit('QueryProductBatchList', response.data)
            })
        },
        queryMaterialCascader ({ commit }) {
            http.get('/Product/QueryMaterialCascader').then(function (response) {
                commit('QueryMaterialCascader', response.data)
            })
        },
        getMaterial ({ commit }, params) {
            if (params.materialId !== undefined) {
                http.get('/Product/GetMaterial', { params: params }).then(function (response) {
                    commit('GetMaterial', response.data)
                })
            } else {
                commit('SetMterial', { Code: '' })
            }
        }
    }
}
export default productBatch
