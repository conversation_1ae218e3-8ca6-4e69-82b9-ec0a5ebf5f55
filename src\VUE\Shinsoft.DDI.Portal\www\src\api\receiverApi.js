import axios from '@/utils/axios'

/**
 * 收货方相关API
 */
export const receiverApi = {
  /**
   * 查询收货方列表
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageIndexSize 页大小
   * @param {string} params.name 收货方名称
   * @param {string} params.code 收货方编码
   * @param {string} params.provinceId 省份ID
   * @param {string} params.cityId 城市ID
   * @param {string} params.order 排序
   * @returns {Promise} API响应
   */
  queryReceiver(params) {
    return axios.get('/Receiver/QueryReceiver', { params })
  },

  /**
   * 获取收货方详情
   * @param {string} id 收货方ID
   * @returns {Promise} API响应
   */
  getReceiver(id) {
    return axios.get('/Receiver/Get', { params: { id } })
  },

  /**
   * 新增收货方
   * @param {Object} data 收货方数据
   * @param {string} data.Name 收货方名称
   * @param {string} data.ProvinceId 省份ID
   * @param {string} data.CityId 城市ID
   * @param {string} data.CountyId 区县ID
   * @param {string} data.ReceiverTypeId 收货方类型ID
   * @param {string} data.UnifiedSocialCreditCode 统一社会信用代码
   * @param {string} data.Address 详细地址
   * @param {string} data.Telephone 联系电话
   * @param {string} data.EMail 电子邮箱
   * @param {string} data.PostalCode 邮政编码
   * @param {string} data.NetAddress 网络地址
   * @param {string} data.HospitalGradeId 医院等级ID
   * @param {string} data.HospitalLevelId 医院级别ID
   * @param {string} data.Remark 备注
   * @returns {Promise} API响应
   */
  addReceiver(data) {
    return axios.post('/Receiver/Add', data)
  },

  /**
   * 编辑收货方
   * @param {Object} data 收货方数据
   * @param {string} data.SdrCode 授权码
   * @param {string} data.Code 收货方Code
   * @param {string} data.Name 收货方名称
   * @param {string} data.ProvinceId 省份ID
   * @param {string} data.CityId 城市ID
   * @param {string} data.CountyId 区县ID
   * @param {string} data.ReceiverTypeId 收货方类型ID
   * @param {string} data.UnifiedSocialCreditCode 统一社会信用代码
   * @param {string} data.Address 详细地址
   * @param {string} data.Telephone 联系电话
   * @param {string} data.EMail 电子邮箱
   * @param {string} data.PostalCode 邮政编码
   * @param {string} data.NetAddress 网络地址
   * @param {string} data.HospitalGradeId 医院等级ID
   * @param {string} data.HospitalLevelId 医院级别ID
   * @param {string} data.Remark 备注
   * @returns {Promise} API响应
   */
  editReceiver(data) {
    return axios.post('/Receiver/Edit', data)
  },

  /**
    * 删除收货方
    * @param {Object} data 包含收货方ID的对象
    * @returns {Promise} API响应
    */
   deleteReceiver(data) {
     return axios.post('/Receiver/Delete', data)
   },

  /**
   * 导出收货方列表
   * @param {Object} params 导出参数
   * @param {string} params.name 收货方名称
   * @param {string} params.code 收货方编码
   * @param {string} params.provinceId 省份ID
   * @param {string} params.cityId 城市ID
   * @returns {Promise} API响应，返回文件流
   */
  exportReceiver(params) {
    return axios.post('/Receiver/ExportReceiver', params, {
      responseType: 'arraybuffer'
    })
  }
}

export default receiverApi